export interface User {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  role: 'ADMIN' | 'SUPERVISOR' | 'EMPLOYEE';
  last_login?: Date;
  is_active: boolean;
  employee_id: number;
  created_at: Date;
  updated_at: Date;
}

export const mockUsers: User[] = [
  {
    id: 1,
    username: "admin",
    email: "<EMAIL>",
    password_hash: "admin123",
    role: "ADMIN",
    employee_id: 1,
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
    last_login: null
  },
  {
    id: 2,
    username: "supervisor",
    email: "<EMAIL>",
    password_hash: "super123",
    role: "SUPERVISOR",
    employee_id: 2,
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
    last_login: null
  },
  {
    id: 3,
    username: "employee",
    email: "<EMAIL>",
    password_hash: "emp123",
    role: "<PERSON><PERSON><PERSON><PERSON>Y<PERSON>",
    employee_id: 3,
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
    last_login: null
  }
];

export interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  hire_date: Date;
  department_id: number;
  position_id: number;
  manager_id?: number;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  birth_date?: Date;
  gender?: string;
  marital_status?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  status: 'active' | 'inactive';
  created_at: Date;
  updated_at: Date;
}

export const mockEmployees: Employee[] = [
  {
    id: 1,
    employee_id: "EMP001",
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    phone: "************",
    hire_date: new Date("2023-01-15"),
    department_id: 1,
    position_id: 1,
    status: "active",
    created_at: new Date("2023-01-15"),
    updated_at: new Date("2023-01-15")
  }
];

export interface Department {
  id: number;
  name: string;
  description: string;
  head_id: number;
  created_at: Date;
  updated_at: Date;
}

export const mockDepartments: Department[] = [
  {
    id: 1,
    name: "IT Department",
    description: "Information Technology Department",
    head_id: 1,
    created_at: new Date("2023-01-01"),
    updated_at: new Date("2023-01-01")
  },
  // Add more mock departments as needed
];

export interface KoperasiMember {
  id: number;
  employee_id: number;
  join_date: Date;
  monthly_contribution: number;
  total_savings: number;
  status: 'active' | 'inactive';
  created_at: Date;
  updated_at: Date;
}

export interface KoperasiLoan {
  id: number;
  member_id: number;
  amount: number;
  purpose: string;
  application_date: Date;
  approval_date?: Date;
  start_date?: Date;
  end_date?: Date;
  interest_rate: number;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  approved_by?: number;
  created_at: Date;
  updated_at: Date;
}

export interface KoperasiLoanPayment {
  id: number;
  loan_id: number;
  payment_date: Date;
  amount: number;
  payment_method?: string;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export interface KoperasiOverview {
  totalMembers: number;
  totalSavings: number;
  activeLoanCount: number;
  monthlyContributions: number;
  totalLoanAmount: number;
  averageSavingsPerMember: number;
  membershipGrowth: number; // Percentage
  loanApprovalRate: number; // Percentage
}

export interface KoperasiSaving {
  id: number;
  member_id: number;
  balance: number;
  account_type: string;
  interest_rate: number;
  created_at: Date;
  updated_at: Date;
}

export const mockKoperasiSavings: KoperasiSaving[] = [
  {
    id: 1,
    member_id: 1,
    balance: 6500000,
    account_type: "Regular",
    interest_rate: 3.5,
    created_at: new Date("2023-01-15"),
    updated_at: new Date("2024-02-15")
  },
  {
    id: 2,
    member_id: 2,
    balance: ********,
    account_type: "Premium",
    interest_rate: 4.0,
    created_at: new Date("2023-03-20"),
    updated_at: new Date("2024-02-15")
  },
  {
    id: 3,
    member_id: 3,
    balance: 8900000,
    account_type: "Regular",
    interest_rate: 3.5,
    created_at: new Date("2023-06-10"),
    updated_at: new Date("2024-02-15")
  }
];

export const mockKoperasiMembers: KoperasiMember[] = [
  {
    id: 1,
    employee_id: 1,
    join_date: new Date("2023-01-15"),
    monthly_contribution: 500000,
    total_savings: 6500000,
    status: "active",
    created_at: new Date("2023-01-15"),
    updated_at: new Date("2024-02-15")
  },
  {
    id: 2,
    employee_id: 2,
    join_date: new Date("2023-03-20"),
    monthly_contribution: 750000,
    total_savings: ********,
    status: "active",
    created_at: new Date("2023-03-20"),
    updated_at: new Date("2024-02-15")
  },
  {
    id: 3,
    employee_id: 3,
    join_date: new Date("2023-06-10"),
    monthly_contribution: 500000,
    total_savings: 8900000,
    status: "active",
    created_at: new Date("2023-06-10"),
    updated_at: new Date("2024-02-15")
  }
];

export const mockKoperasiLoans: KoperasiLoan[] = [
  {
    id: 1,
    member_id: 1,
    amount: 5000000,
    purpose: "Home Renovation",
    application_date: new Date("2024-01-15"),
    approval_date: new Date("2024-01-20"),
    start_date: new Date("2024-02-01"),
    end_date: new Date("2024-08-01"),
    interest_rate: 1.5,
    status: "approved",
    approved_by: 1,
    created_at: new Date("2024-01-15"),
    updated_at: new Date("2024-01-20")
  },
  {
    id: 2,
    member_id: 2,
    amount: 10000000,
    purpose: "Education",
    application_date: new Date("2024-02-01"),
    status: "pending",
    interest_rate: 1.5,
    created_at: new Date("2024-02-01"),
    updated_at: new Date("2024-02-01")
  },
  {
    id: 3,
    member_id: 3,
    amount: 3000000,
    purpose: "Medical Expenses",
    application_date: new Date("2024-01-10"),
    approval_date: new Date("2024-01-15"),
    start_date: new Date("2024-02-01"),
    end_date: new Date("2024-05-01"),
    interest_rate: 1.5,
    status: "approved",
    approved_by: 1,
    created_at: new Date("2024-01-10"),
    updated_at: new Date("2024-01-15")
  }
];

export const mockKoperasiLoanPayments: KoperasiLoanPayment[] = [
  {
    id: 1,
    loan_id: 1,
    payment_date: new Date("2024-02-15"),
    amount: 850000,
    payment_method: "bank_transfer",
    notes: "Monthly installment payment",
    created_at: new Date("2024-02-15"),
    updated_at: new Date("2024-02-15")
  }
];

export const mockKoperasiOverview: KoperasiOverview = {
  totalMembers: 150,
  totalSavings: **********, // 2.5B
  activeLoanCount: 45,
  monthlyContributions: ********, // 75M
  totalLoanAmount: **********, // 1.25B
  averageSavingsPerMember: ********, // ~16.7M
  membershipGrowth: 15, // 15%
  loanApprovalRate: 85 // 85%
};

// Additional helper data for dropdowns and filters
export const accountTypes = [
  "Regular Savings",
  "Term Deposit",
  "Emergency Fund",
  "Education Savings",
  "Retirement Savings"
];

export const loanPurposes = [
  "Business",
  "Education",
  "Home Improvement",
  "Medical",
  "Personal",
  "Emergency"
];

export const loanTerms = [
  "6 months",
  "12 months",
  "24 months",
  "36 months",
  "48 months"
];

export const loanStatuses = [
  "pending",
  "approved",
  "rejected",
  "completed"
] as const;

export const memberStatuses = [
  "active",
  "inactive"
] as const;

export const paymentMethods = [
  "bank_transfer",
  "cash",
  "salary_deduction",
  "other"
] as const;

export const departments = [
  "IT Department",
  "HR Department",
  "Finance Department",
  "Marketing Department",
  "Operations Department",
  "Sales Department"
];




