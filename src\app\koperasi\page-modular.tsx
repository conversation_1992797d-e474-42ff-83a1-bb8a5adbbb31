"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/lib/auth";
import type {
  KoperasiMember,
  KoperasiLoan,
  KoperasiSaving,
  KoperasiOverview,
} from "@/lib/types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle } from "lucide-react";
// import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import ProtectedRoute from "@/components/ProtectedRoute";
import Navbar from "@/components/layout/Navbar";
import { SavingsTab, LoansTab, MembershipsTab, OverviewTab } from "@/components/koperasi";

export default function KoperasiPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [overview, setOverview] = useState<KoperasiOverview>({
    totalMembers: 0,
    totalActiveMembers: 0,
    totalInactiveMembers: 0,
    memberActivePercentage: 0,
    totalSavings: 0,
    totalOneTimeContributions: 0,
    totalMonthlyContributions: 0,
    totalOptionalContributions: 0,
    averageSavingsPerMember: 0,
    activeLoanCount: 0,
    completedLoanCount: 0,
    totalLoanAmount: 0,
    completedLoanAmount: 0,
    totalLoanCount: 0,
    recentTransactions: [],
    monthlyContributions: 0,
    membershipGrowth: 0,
    loanApprovalRate: 0,
  });
  const [members, setMembers] = useState<KoperasiMember[]>([]);
  const [loans, setLoans] = useState<KoperasiLoan[]>([]);
  const [savings, setSavings] = useState<KoperasiSaving[]>([]);
  const [isViewMemberOpen, setIsViewMemberOpen] = useState(false);
  const [isEditMemberOpen, setIsEditMemberOpen] = useState(false);
  const [isDeleteMemberOpen, setIsDeleteMemberOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<KoperasiMember | null>(null);
  const [notification, setNotification] = useState<{ type: 'success' | 'error', message: string } | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch overview data
      const overviewResponse = await fetch("/api/koperasi/overview");
      if (overviewResponse.ok) {
        const overviewData = await overviewResponse.json();
        setOverview(overviewData);
      }

      // Fetch members data
      const membersResponse = await fetch("/api/koperasi/members");
      if (membersResponse.ok) {
        const membersData = await membersResponse.json();
        setMembers(membersData);
      }

      // Fetch loans data
      const loansResponse = await fetch("/api/koperasi/loans");
      if (loansResponse.ok) {
        const loansData = await loansResponse.json();
        setLoans(loansData);
      }

      // Fetch savings data
      const savingsResponse = await fetch("/api/koperasi/savings");
      if (savingsResponse.ok) {
        const savingsData = await savingsResponse.json();
        setSavings(savingsData);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      setNotification({
        type: 'error',
        message: 'Failed to fetch data. Please try again later.'
      });
    }
  };

  const handleSavingsSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/koperasi/savings/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create savings transaction");
      }

      await fetchData();
      setNotification({
        type: 'success',
        message: 'Savings transaction created successfully'
      });
    } catch (error: any) {
      console.error("Error creating savings transaction:", error);
      setNotification({
        type: 'error',
        message: error.message || 'Failed to create savings transaction'
      });
    }
  };

  const handleLoanSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/koperasi/loans/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create loan application");
      }

      await fetchData();
      setNotification({
        type: 'success',
        message: 'Loan application created successfully'
      });
    } catch (error: any) {
      console.error("Error creating loan application:", error);
      setNotification({
        type: 'error',
        message: error.message || 'Failed to create loan application'
      });
    }
  };

  const handleMemberSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/koperasi/members/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create member");
      }

      await fetchData();
      setNotification({
        type: 'success',
        message: 'Member created successfully'
      });
    } catch (error: any) {
      console.error("Error creating member:", error);
      setNotification({
        type: 'error',
        message: error.message || 'Failed to create member'
      });
    }
  };

  const handleMemberImport = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/koperasi/members/import", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to import members");
      }

      await fetchData();
      setNotification({
        type: 'success',
        message: 'Members imported successfully'
      });
    } catch (error: any) {
      console.error("Error importing members:", error);
      setNotification({
        type: 'error',
        message: error.message || 'Failed to import members'
      });
    }
  };

  const handleViewMember = (member: KoperasiMember) => {
    setSelectedMember(member);
    setIsViewMemberOpen(true);
  };

  const handleEditMember = (member: KoperasiMember) => {
    setSelectedMember(member);
    setIsEditMemberOpen(true);
  };

  const handleDeleteMember = (member: KoperasiMember) => {
    setSelectedMember(member);
    setIsDeleteMemberOpen(true);
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          {notification && (
            <div className={`p-4 mb-4 rounded-md ${notification.type === "success" ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"}`}>
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                <p className="font-medium">
                  {notification.type === "success" ? "Success" : "Error"}
                </p>
              </div>
              <p className="mt-1 text-sm">{notification.message}</p>
            </div>
          )}

          <div className="flex flex-col space-y-8">
            <div className="flex flex-col space-y-2">
              <h1 className="text-3xl font-bold tracking-tight">Koperasi</h1>
              <p className="text-muted-foreground">
                Manage cooperative savings, loans, and memberships.
              </p>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="savings">Transaction</TabsTrigger>
                <TabsTrigger value="loans">Loans</TabsTrigger>
                <TabsTrigger value="memberships">Memberships</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <OverviewTab overview={overview} />
              </TabsContent>

              <TabsContent value="savings">
                <SavingsTab
                  savings={savings}
                  members={members}
                  userRole={user?.role}
                  onSavingsSubmit={handleSavingsSubmit}
                  onRefresh={fetchData}
                />
              </TabsContent>

              <TabsContent value="loans">
                <LoansTab
                  loans={loans}
                  userRole={user?.role}
                  onLoanSubmit={handleLoanSubmit}
                  onRefresh={fetchData}
                />
              </TabsContent>

              <TabsContent value="memberships">
                <MembershipsTab
                  members={members}
                  userRole={user?.role}
                  onMemberSubmit={handleMemberSubmit}
                  onMemberImport={handleMemberImport}
                  onMemberEdit={handleEditMember}
                  onMemberDelete={handleDeleteMember}
                  onMemberView={handleViewMember}
                  onRefresh={fetchData}
                />
              </TabsContent>
            </Tabs>
          </div>
        </main>

        {/* View Member Dialog */}
        <Dialog open={isViewMemberOpen} onOpenChange={setIsViewMemberOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Member Details</DialogTitle>
            </DialogHeader>
            {selectedMember && (
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium">Member ID</p>
                    <p>M{String(selectedMember.id).padStart(3, "0")}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Employee Name</p>
                    <p>{selectedMember.firstName} {selectedMember.lastName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Join Date</p>
                    <p>{selectedMember.join_date ? new Date(selectedMember.join_date).toLocaleDateString() : "-"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <p>{selectedMember.status}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium">One Time Contribution</p>
                    <p>{new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.one_time_contribution || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Monthly Contribution</p>
                    <p>{new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.monthly_contribution || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Optional Contribution</p>
                    <p>{new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.optional_contribution || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Total Savings</p>
                    <p>{new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.total_savings || 0)}</p>
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Edit Member Dialog */}
        <Dialog open={isEditMemberOpen} onOpenChange={setIsEditMemberOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Member</DialogTitle>
            </DialogHeader>
            {selectedMember && (
              <div className="grid gap-4">
                <p>Edit member form would go here</p>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Member Dialog */}
        <Dialog open={isDeleteMemberOpen} onOpenChange={setIsDeleteMemberOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Member</DialogTitle>
            </DialogHeader>
            {selectedMember && (
              <div className="grid gap-4">
                <p>Are you sure you want to delete this member?</p>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsDeleteMemberOpen(false)}>Cancel</Button>
                  <Button variant="destructive">Delete</Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </ProtectedRoute>
  );
}
