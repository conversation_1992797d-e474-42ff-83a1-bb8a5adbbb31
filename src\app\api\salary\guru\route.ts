import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



// GET: Fetch guru salary data
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    const username = searchParams.get('username');
    const role = searchParams.get('role');

    // Build the where clause based on parameters
    let whereClause: any = {};

    // If month and year are provided, filter by period
    if (month && year) {
      const periodStart = new Date(parseInt(year), parseInt(month), 1);
      const periodEnd = new Date(parseInt(year), parseInt(month) + 1, 0);

      whereClause.period = {
        gte: periodStart,
        lte: periodEnd,
      };
    }

    // If username is provided and role is not ADMIN, filter by employee name
    // This ensures SUPERVISOR and EMPLOYEE roles only see their own data
    if (username && role !== 'ADMIN') {
      // For non-ADMIN roles, we'll filter by nama field
      // First, try to get the employee's name from the username
      try {
        const employee = await prisma.employee.findFirst({
          where: {
            employeeId: username.trim(),
            isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
          },
          select: { firstName: true, lastName: true }
        });

        if (employee) {
          // Construct the full name to match against the nama field
          const fullName = employee.lastName
            ? `${employee.firstName} ${employee.lastName}`
            : employee.firstName;

          whereClause.nama = {
            contains: fullName
          };
        }
      } catch (error) {
        console.error('Error finding employee:', error);
      }
    }

    // Fetch guru salary data with the constructed where clause
    const guruSalaries = await prisma.salaryGuru.findMany({
      where: whereClause,
      orderBy: [
        { period: 'desc' },
        { nama: 'asc' },
      ],
    });

    return NextResponse.json(guruSalaries);
  } catch (error) {
    console.error('Failed to fetch guru salary data:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch guru salary data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST: Create or update guru salary data
export async function POST(request: Request) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const data = await request.json();
    const { id, month, year, ...salaryData } = data;

    // Set period date
    const period = new Date(year, month, 1);

    if (id) {
      // Update existing record
      const updatedSalary = await prisma.salaryGuru.update({
        where: { id },
        data: {
          ...salaryData,
          period,
        },
      });

      return NextResponse.json(updatedSalary);
    } else {
      // Create new record
      const newSalary = await prisma.salaryGuru.create({
        data: {
          ...salaryData,
          period,
        },
      });

      return NextResponse.json(newSalary);
    }
  } catch (error) {
    console.error('Failed to save guru salary data:', error);
    return NextResponse.json(
      {
        error: 'Failed to save guru salary data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
