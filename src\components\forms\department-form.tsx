import { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { logger } from "@/lib/logger";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Employee {
  id: number;
  firstName: string;
  lastName: string;
  department: {
    id: number;
    name: string;
  };
  position: {
    title: string;
  };
}

interface GroupedEmployees {
  [key: string]: {
    departmentName: string;
    employees: Employee[];
  };
}

interface DepartmentFormProps {
  onSubmit: (data: any) => void;
  onCancel?: () => void;
  defaultValues?: {
    id?: number;
    name: string;
    description?: string;
    head?: string | null;
  };
}

export function DepartmentForm({ onSubmit, onCancel, defaultValues }: DepartmentFormProps) {
  const [formData, setFormData] = useState({
    name: defaultValues?.name || '',
    description: defaultValues?.description || '',
    head: defaultValues?.head || 'none',
  });

  const [groupedEmployees, setGroupedEmployees] = useState<GroupedEmployees>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchEmployees = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/employees');
        if (!response.ok) {
          throw new Error('Failed to fetch employees');
        }
        const data: Employee[] = await response.json();

        // Log jumlah karyawan yang diambil tanpa menampilkan data sensitif
        logger.debug(`Fetched ${data.length} employees for department form`);

        // Group employees by department, dengan pengecekan department
        const grouped = data.reduce((acc: GroupedEmployees, employee) => {
          // Pastikan employee dan department ada
          if (!employee.department?.id) {
            return acc;
          }

          const deptId = employee.department.id.toString();
          if (!acc[deptId]) {
            acc[deptId] = {
              departmentName: employee.department.name,
              employees: []
            };
          }
          acc[deptId].employees.push(employee);
          return acc;
        }, {});

        // Sort departments by name and employees by name within each department
        const sortedGrouped = Object.fromEntries(
          Object.entries(grouped)
            .sort(([,a], [,b]) => a.departmentName.localeCompare(b.departmentName))
            .map(([deptId, dept]) => [
              deptId,
              {
                ...dept,
                employees: dept.employees.sort((a, b) =>
                  `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
                )
              }
            ])
        );

        setGroupedEmployees(sortedGrouped);
      } catch (err) {
        logger.error('Failed to fetch employees for department form:', err);
        setGroupedEmployees({});
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployees();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      head: formData.head === 'none' ? null : formData.head
    });
  };

  return (
    <form onSubmit={handleSubmit} className="grid gap-4 py-4">
      <div>
        <Label htmlFor="name">Department Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder="Enter department name"
          className="mt-1"
        />
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Enter department description"
          className="mt-1"
        />
      </div>

      <div>
        <Label>Department Head</Label>
        <Select
          value={formData.head}
          onValueChange={(value) => setFormData(prev => ({ ...prev, head: value }))}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select department head" />
          </SelectTrigger>
          <SelectContent>
            <ScrollArea className="h-[300px]">
              <SelectItem value="none">No Head</SelectItem>
              {Object.entries(groupedEmployees).map(([deptId, { departmentName, employees }]) => (
                <SelectGroup key={deptId}>
                  <SelectLabel className="font-semibold">{departmentName}</SelectLabel>
                  {employees.map((employee) => (
                    <SelectItem
                      key={employee.id}
                      value={employee.id.toString()}
                      className="pl-6"
                    >
                      {`${employee.firstName} ${employee.lastName} - ${employee.position.title}`}
                    </SelectItem>
                  ))}
                </SelectGroup>
              ))}
            </ScrollArea>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end gap-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit">Save Changes</Button>
      </div>
    </form>
  );
}
