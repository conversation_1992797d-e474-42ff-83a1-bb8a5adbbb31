"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { useAuth } from "@/lib/auth";

interface HeroSectionProps {
  title?: string;
  description?: string;
  getStartedText?: string;
  backgroundImage?: string;
}

const HeroSection = ({
  title = "Employee Management System",
  description = "Our Employee Management System helps you track leave management, koperasi, manage request, and streamline salary sheet - all in one powerful platform.",
  getStartedText = "Get Started",
  backgroundImage = "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=1200&q=80",
}: HeroSectionProps) => {
  const router = useRouter();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      // If user is logged in, redirect to their appropriate dashboard
      let redirectPath;

      switch (user.role) {
        case 'ADMIN':
          redirectPath = '/dashboard';
          break;
        case 'SUPERVISOR':
          redirectPath = '/employees';
          break;
        case 'HEAD':
          redirectPath = '/employees';
          break;
        case 'OPERATOR_KOP':
          redirectPath = '/koperasi';
          break;
        case 'EMPLOYEE':
        default:
          redirectPath = '/leave-management';
          break;
      }

      // Removed sensitive log for security
      router.push(redirectPath);
    } else {
      // If not logged in, redirect to login page
      router.push('/login');
    }
  };

  return (
    <section className="relative w-full h-[600px] bg-slate-900 overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <Image
          src={backgroundImage}
          alt="Team working together"
          fill
          className="object-cover opacity-30"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 to-slate-900/70" />
      </div>
      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 h-full flex flex-col justify-center items-start">
        <div className="max-w-2xl text-[#b7b5b5]">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            {title}
          </h1>
          <p className="text-lg md:text-xl text-slate-200 mb-8">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              size="lg"
              className="bg-white text-slate-900 hover:bg-gray-100 font-medium px-8 py-6 text-lg transition-transform hover:scale-105"
              onClick={handleGetStarted}
            >
              {user ? 'Go to Dashboard' : getStartedText}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
