"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: "ADMIN" | "SUPERVISOR" | "EMPLOYEE" | "OPERATOR_KOP" | "HEAD" | null;
}

const ProtectedRoute = ({
  children,
  requiredRole = null,
}: ProtectedRouteProps) => {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        router.push("/"); // Mengubah redirect ke landing page
        return;
      }

      if (requiredRole && user.role !== requiredRole) {
        switch (user.role) {
          case "ADMIN":
            router.push("/dashboard");
            break;
          case "SUPERVISOR":
            router.push("/employees");
            break;
          case "HEAD":
            router.push("/employees");
            break;
          case "EMPLOYEE":
            router.push("/leave-management");
            break;
          case "OPERATOR_KOP":
            router.push("/koperasi");
            break;
          default:
            router.push("/");
        }
      }
    }
  }, [user, isLoading, router, requiredRole]);

  // Only show content after first mount to avoid hydration issues
  if (!mounted || isLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return null;
  }

  return <>{children}</>;
};

export default ProtectedRoute;

