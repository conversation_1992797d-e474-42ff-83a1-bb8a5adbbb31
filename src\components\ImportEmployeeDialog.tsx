import { useState } from 'react';
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2 } from "lucide-react";
import * as XLSX from 'xlsx';
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface ImportStatus {
  total: number;
  current: number;
  created: number;
  updated: number;
  failed: number;
  errors: string[];
  isImporting: boolean;
}

interface ImportEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportSuccess: () => void; // Tambahkan prop baru
}

export function ImportEmployeeDialog({
  open,
  onOpenChange,
  onImportSuccess
}: ImportEmployeeDialogProps) {
  const [importStatus, setImportStatus] = useState<ImportStatus & {
    duplicates?: number;
    details?: {
      created: string[];
      updated: string[];
    };
  }>({
    total: 0,
    current: 0,
    created: 0,
    updated: 0,
    failed: 0,
    errors: [],
    isImporting: false
  });

  const readExcel = async (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // Cari sheet Template dengan case insensitive
          const templateSheetName = workbook.SheetNames.find(name =>
            name.toLowerCase() === 'template' || name.toLowerCase().includes('template')
          );

          if (!templateSheetName) {
            console.error('Sheet "Template" not found. Available sheets:', workbook.SheetNames);
            reject(new Error('Sheet "Template" not found in the Excel file. Please use the template provided by clicking "Download Template" button.'));
            return;
          }

          console.log('Using sheet:', templateSheetName);

          const worksheet = workbook.Sheets[templateSheetName];

          if (!worksheet) {
            reject(new Error('Invalid Excel file format. The sheet "Template" was found but could not be read. Please use the template provided by clicking "Download Template" button.'));
            return;
          }

          console.log('Worksheet reference:', worksheet['!ref']);

          // Get headers and validate required fields
          const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
          const headers: string[] = [];
          for (let C = range.s.c; C <= range.e.c; ++C) {
            const cell = worksheet[XLSX.utils.encode_cell({ r: 0, c: C })];
            const headerText = (cell?.v || '').toString().trim();
            headers.push(headerText);
          }

          console.log('Headers found in Excel file:', headers);

          // Validasi urutan kolom
          const expectedOrder = [
            'Employee ID *', 'Full Name *', 'Email *', 'Phone', 'Address',
            'Birth Date', 'Birth Place', 'Gender', 'Religion', 'Marital Status',
            'Education Level', 'Join Date *', 'Status *', 'Bank Name', 'Bank Account',
            'NPWP', 'BPJS Kesehatan', 'BPJS Ketenagakerjaan', 'Department *', 'Position *',
            'Password', 'Role *'
          ];

          // Cek apakah urutan kolom sesuai dengan yang diharapkan
          let orderCorrect = true;
          const headerErrors = [];

          for (let i = 0; i < Math.min(headers.length, expectedOrder.length); i++) {
            const expected = expectedOrder[i];
            const actual = headers[i];

            // Normalisasi untuk perbandingan (hapus spasi, lowercase, hapus *)
            const normalizedExpected = expected.toLowerCase().replace(/\s+/g, '').replace(/\*/g, '');
            const normalizedActual = actual.toLowerCase().replace(/\s+/g, '').replace(/\*/g, '');

            if (normalizedExpected !== normalizedActual) {
              orderCorrect = false;
              headerErrors.push(`Column ${i+1}: Expected "${expected}", found "${actual}"`);
            }
          }

          if (!orderCorrect) {
            console.error('Column order is incorrect:', headerErrors);
            reject(new Error(
              'Column order in the Excel file does not match the expected order.\n' +
              'Please use the template provided by clicking "Download Template" button and do not modify the column order.\n\n' +
              headerErrors.slice(0, 5).join('\n') +
              (headerErrors.length > 5 ? '\n...and more errors' : '')
            ));
            return;
          }

          const requiredFields = [
            'Employee ID *',
            'Full Name *',
            'Email *',
            'Join Date *',
            'Status *',
            'Department *',
            'Position *',
            'Role *'
          ];

          // Validasi dengan lebih fleksibel, menangani spasi dan case sensitivity
          const missingFields = requiredFields.filter(field => {
            const normalizedField = field.toLowerCase().replace(/\s+/g, '');
            return !headers.some(header => {
              const normalizedHeader = header.toLowerCase().replace(/\s+/g, '');
              return normalizedHeader === normalizedField || normalizedHeader === normalizedField.replace('*', '');
            });
          });
          if (missingFields.length > 0) {
            console.error('Missing required fields:', missingFields);
            console.error('Headers found in file:', headers);
            reject(new Error(
              'Missing required columns:\n' +
              missingFields.map(field => `- ${field}`).join('\n') +
              '\n\nPlease use the template provided by clicking "Download Template" button.\n' +
              'Make sure you are using the "Template" sheet and have not modified the header row.'
            ));
            return;
          }

          // Gunakan range yang sudah didefinisikan sebelumnya
          console.log('Excel range:', range);

          // Cari baris terakhir yang memiliki data
          let lastDataRow = 0;
          for (let R = range.s.r; R <= range.e.r; ++R) {
            let hasData = false;
            for (let C = range.s.c; C <= range.e.c; ++C) {
              const cell = worksheet[XLSX.utils.encode_cell({ r: R, c: C })];
              if (cell && cell.v !== undefined && cell.v !== null && cell.v.toString().trim() !== '') {
                hasData = true;
                break;
              }
            }
            if (hasData) {
              lastDataRow = R;
            }
          }

          console.log('Last data row:', lastDataRow);

          // Sesuaikan range untuk hanya mencakup data yang valid
          const validRange = {
            s: { r: range.s.r, c: range.s.c },
            e: { r: lastDataRow, c: range.e.c }
          };

          // Buat pemetaan header untuk menangani perbedaan format
          const headerMap: Record<string, string> = {};
          requiredFields.forEach(requiredField => {
            const normalizedRequired = requiredField.toLowerCase().replace(/\s+/g, '').replace(/\*/g, '');

            // Cari header yang cocok
            const matchingHeader = headers.find(header => {
              const normalizedHeader = header.toLowerCase().replace(/\s+/g, '').replace(/\*/g, '');
              return normalizedHeader === normalizedRequired;
            });

            if (matchingHeader) {
              headerMap[requiredField] = matchingHeader;
            }
          });

          console.log('Header mapping:', headerMap);

          // Cek apakah semua kolom yang diperlukan ada
          const missingHeaders = requiredFields.filter(field => !headerMap[field]);
          if (missingHeaders.length > 0) {
            console.error('Missing headers:', missingHeaders);
            console.error('Available headers:', headers);
            reject(new Error(
              'Missing required columns in the Excel file:\n' +
              missingHeaders.map(field => `- ${field}`).join('\n') +
              '\n\nPlease use the template provided by clicking "Download Template" button.'
            ));
            return;
          }

          // Convert to JSON with proper options and use header mapping
          let jsonData = XLSX.utils.sheet_to_json(worksheet, {
            raw: false,
            defval: '',
            blankrows: false,
            range: { s: { r: 1, c: 0 }, e: { r: lastDataRow, c: range.e.c } }, // Mulai dari baris kedua (indeks 1), abaikan header
            header: headers // Gunakan header yang ditemukan di file
          });

          console.log('First row of data after conversion:', jsonData[0]);
          console.log('Total rows found in Excel:', jsonData.length);

          // Validasi data untuk setiap baris
          const invalidRows: { rowNum: number; missingFields: string[] }[] = [];
          const validRows: any[] = [];

          // Data sudah divalidasi melalui pemetaan header

          jsonData.forEach((row: any, index: number) => {
            // Cek apakah baris ini memiliki data yang valid
            const missingFields: string[] = [];

            // Lewati baris yang benar-benar kosong
            if (Object.keys(row).length === 0) return;

            // Cek kolom-kolom wajib menggunakan pemetaan header
            requiredFields.forEach(field => {
              const columnName = headerMap[field];
              const value = row[columnName];
              if (!value || value.toString().trim() === '' || value === '-') {
                missingFields.push(field);
              }
            });

            // Jika ada kolom wajib yang kosong, tambahkan ke daftar baris tidak valid
            if (missingFields.length > 0) {
              invalidRows.push({ rowNum: index + 3, missingFields }); // +3 karena indeks dimulai dari 0, baris header adalah 1, dan data dimulai dari baris 2
            } else {
              // Buat objek baru dengan nama kolom yang benar
              const validRow: Record<string, any> = {};
              requiredFields.forEach(field => {
                const columnName = headerMap[field];
                validRow[field] = row[columnName];
              });

              // Tambahkan kolom opsional jika ada
              Object.keys(row).forEach(col => {
                if (!Object.values(headerMap).includes(col)) {
                  validRow[col] = row[col];
                }
              });

              validRows.push(validRow);
            }
          });

          // Jika ada baris yang tidak valid, tampilkan error
          if (invalidRows.length > 0) {
            console.error('Invalid rows:', invalidRows);

            // Batasi jumlah baris yang ditampilkan dalam pesan error
            const maxRowsToShow = 10;
            const errorMessage = `Found ${invalidRows.length} row(s) with missing required fields:\n\n` +
              invalidRows.slice(0, maxRowsToShow).map(row =>
                `Row ${row.rowNum}: Missing ${row.missingFields.join(', ')}`
              ).join('\n') +
              (invalidRows.length > maxRowsToShow ? `\n...and ${invalidRows.length - maxRowsToShow} more rows` : '') +
              '\n\nPlease ensure all required fields (*) are filled correctly.\nNote: Empty cells, spaces, or \'-\' are not valid values for required fields.';

            reject(new Error(errorMessage));
            return;
          }

          // Gunakan hanya baris yang valid
          jsonData = validRows;

          // Jika tidak ada baris valid, tampilkan error
          if (jsonData.length === 0) {
            reject(new Error('No valid data rows found in the Excel file.\nPlease ensure at least one row has all required fields filled correctly.'));
            return;
          }

          // Additional data validation
          const validationErrors: string[] = [];
          jsonData.forEach((row: any, index: number) => {
            const rowNum = index + 2;

            // Validate email format
            if (row['Email *'] && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row['Email *'])) {
              validationErrors.push(`Row ${rowNum}: Invalid email format`);
            }

            // Validate birth date format if provided
            if (row['Birth Date'] && row['Birth Date'] !== '-') {
              if (!/^\d{4}-\d{2}-\d{2}$/.test(row['Birth Date'])) {
                validationErrors.push(`Row ${rowNum}: Invalid birth date format (use YYYY-MM-DD)`);
              } else {
                // Check if the date is valid
                const date = new Date(row['Birth Date']);
                if (isNaN(date.getTime())) {
                  validationErrors.push(`Row ${rowNum}: Invalid birth date value`);
                }
              }
            }

            // Validate join date format
            if (row['Join Date *'] && !/^\d{4}-\d{2}-\d{2}$/.test(row['Join Date *'])) {
              validationErrors.push(`Row ${rowNum}: Invalid join date format (use YYYY-MM-DD)`);
            } else {
              // Check if the date is valid
              const date = new Date(row['Join Date *']);
              if (isNaN(date.getTime())) {
                validationErrors.push(`Row ${rowNum}: Invalid join date value`);
              }
            }

            // Validate status
            if (!['Tetap', 'Kontrak', 'Honor'].includes(row['Status *'])) {
              validationErrors.push(`Row ${rowNum}: Invalid status (must be Tetap, Kontrak, or Honor)`);
            }

            // Validate role
            if (!['ADMIN', 'SUPERVISOR', 'EMPLOYEE'].includes(row['Role *'])) {
              validationErrors.push(`Row ${rowNum}: Invalid role (must be ADMIN, SUPERVISOR, or EMPLOYEE)`);
            }
          });

          if (validationErrors.length > 0) {
            reject(new Error(
              `Found ${validationErrors.length} validation error(s):\n\n${validationErrors.join('\n')}\n\n` +
              `Please correct these errors and try again.`
            ));
            return;
          }

          console.log('Valid Excel Data:', jsonData);
          resolve(jsonData);
        } catch (error) {
          console.error('Excel parsing error:', error);
          reject(new Error(
            'Failed to parse Excel file.\n' +
            'Please ensure you are using the correct template and the file is not corrupted.\n' +
            'You can download a new template using the "Download Template" button.'
          ));
        }
      };
      reader.onerror = (error) => reject(error);
      reader.readAsArrayBuffer(file);
    });
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setImportStatus({
        total: 0,
        current: 0,
        created: 0,
        updated: 0,
        failed: 0,
        errors: [],
        isImporting: true
      });

      const employees = await readExcel(file);

      if (!employees || employees.length === 0) {
        throw new Error('No data found in Excel file');
      }

      setImportStatus(prev => ({
        ...prev,
        total: employees.length,
      }));

      const batchSize = 10;
      for (let i = 0; i < employees.length; i += batchSize) {
        const batch = employees.slice(i, i + batchSize);

        setImportStatus(prev => ({
          ...prev,
          current: i
        }));

        const response = await fetch('/api/employees/import', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ employees: batch }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Import failed: ${errorData.error || response.statusText}`);
        }

        const result = await response.json();

        setImportStatus(prev => ({
          ...prev,
          current: i + batch.length,
          created: prev.created + (result.results?.created || 0),
          updated: prev.updated + (result.results?.updated || 0),
          failed: prev.failed + (result.results?.failed || 0),
          duplicates: (prev.duplicates || 0) + (result.results?.duplicates || 0),
          errors: [...prev.errors, ...(result.results?.errors || [])],
          details: {
            created: [...(prev.details?.created || []), ...(result.results?.details?.created || [])],
            updated: [...(prev.details?.updated || []), ...(result.results?.details?.updated || [])]
          }
        }));

        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Panggil onImportSuccess setelah import selesai
      onImportSuccess();

    } catch (error) {
      console.error('Import error:', error);
      setImportStatus(prev => ({
        ...prev,
        errors: [...prev.errors, 'Import process failed: ' + (error as Error).message]
      }));
    } finally {
      setImportStatus(prev => ({
        ...prev,
        current: prev.total,
        isImporting: false
      }));
      event.target.value = '';
    }
  };

  const downloadTemplate = () => {
    const requiredFields = [
      'Employee ID *',
      'Full Name *',
      'Email *',
      'Phone',
      'Address',
      'Birth Date',
      'Birth Place',
      'Gender',
      'Religion',
      'Marital Status',
      'Education Level',
      'Join Date *',
      'Status *',
      'Bank Name',
      'Bank Account',
      'NPWP',
      'BPJS Kesehatan',
      'BPJS Ketenagakerjaan',
      'Department *',
      'Position *',
      'Password',
      'Role *'
    ];

    // Tambahkan contoh data untuk membantu pengguna
    const sampleData = [
      {
        'Employee ID *': 'EMP001',
        'Full Name *': 'Nama Karyawan',
        'Email *': '<EMAIL>',
        'Phone': '************',
        'Address': 'Alamat lengkap',
        'Birth Date': '1990-01-01',
        'Birth Place': 'Jakarta',
        'Gender': 'Laki-laki',
        'Religion': 'Islam',
        'Marital Status': 'Menikah',
        'Education Level': 'S1',
        'Join Date *': '2023-01-01',
        'Status *': 'Tetap',
        'Bank Name': 'BCA',
        'Bank Account': '**********',
        'NPWP': '12.345.678.9-012.000',
        'BPJS Kesehatan': '*************',
        'BPJS Ketenagakerjaan': '*************',
        'Department *': 'IT',
        'Position *': 'Staff IT',
        'Password': 'password123',
        'Role *': 'EMPLOYEE'
      }
    ];

    // Pastikan semua kolom yang diperlukan ada di contoh data
    requiredFields.forEach(field => {
      if (!(field in sampleData[0])) {
        sampleData[0][field] = field.includes('*') ? 'Required Value' : 'Optional Value';
      }
    });

    // Create workbook and add instructions
    const wb = XLSX.utils.book_new();

    // Tambahkan sheet instruksi
    const instructionData = [
      ['PETUNJUK PENGISIAN TEMPLATE IMPORT KARYAWAN'],
      [''],
      ['1. Isi data karyawan di sheet "Template" sesuai dengan format yang telah disediakan.'],
      ['2. Kolom dengan tanda * adalah wajib diisi.'],
      ['3. Format tanggal: YYYY-MM-DD (contoh: 2023-01-31)'],
      ['4. Status harus salah satu dari: Tetap, Kontrak, atau Honor'],
      ['5. Role harus salah satu dari: ADMIN, SUPERVISOR, atau EMPLOYEE (huruf kapital)'],
      ['6. Jangan mengubah nama kolom atau urutan kolom.'],
      ['7. Hapus baris contoh sebelum mengimpor data.'],
      ['8. Simpan file dalam format .xlsx'],
      [''],
      ['Catatan Penting:'],
      ['- Employee ID akan otomatis menjadi username untuk login.'],
      ['- Department dan Position harus sesuai dengan data yang ada di sistem.'],
      [''],
      ['Jika terjadi error saat import:'],
      ['1. Pastikan Anda menggunakan sheet "Template".'],
      ['2. Pastikan semua kolom wajib (*) telah diisi dengan benar.'],
      ['3. Pastikan tidak ada baris kosong di antara data.'],
      ['4. Pastikan format data sesuai dengan ketentuan di atas.']
    ];
    const wsInstructions = XLSX.utils.aoa_to_sheet(instructionData);
    XLSX.utils.book_append_sheet(wb, wsInstructions, 'Instruksi');

    // Buat template sheet dengan header dan contoh data
    // Pastikan header dibuat dengan benar
    const wsTemplate = XLSX.utils.aoa_to_sheet([requiredFields]);
    // Tambahkan data contoh di baris kedua
    XLSX.utils.sheet_add_json(wsTemplate, sampleData, { skipHeader: true, origin: 'A2' });

    // Tambahkan informasi di footer
    const lastRow = 3;
    XLSX.utils.sheet_add_aoa(wsTemplate, [
      ['Catatan: Hapus baris contoh ini sebelum mengimpor data sebenarnya.']
    ], { origin: { r: lastRow, c: 0 } });
    XLSX.utils.book_append_sheet(wb, wsTemplate, 'Template');

    // Save the file
    XLSX.writeFile(wb, 'employee_import_template.xlsx');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Import Employees</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button onClick={downloadTemplate}>
              Download Template
            </Button>
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleImport}
              disabled={importStatus.isImporting}
              className="hidden"
              id="excel-import"
            />
            <Button
              onClick={() => document.getElementById('excel-import')?.click()}
              disabled={importStatus.isImporting}
            >
              {importStatus.isImporting ? 'Importing...' : 'Select Excel File'}
            </Button>
          </div>

          {importStatus.isImporting && (
            <div className="space-y-2">
              <Progress
                value={(importStatus.current / importStatus.total) * 100}
                className="h-2"
              />
              <p className="text-sm text-gray-500">
                Processing: {importStatus.current} of {importStatus.total} employees
              </p>
            </div>
          )}

          {(importStatus.created > 0 || importStatus.updated > 0 || importStatus.failed > 0) && (
            <div className="space-y-2">
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertDescription>
                  <p className="font-semibold">Import Summary:</p>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-2">
                    <div>Total Rows:</div>
                    <div>{importStatus.total}</div>

                    <div>Processed:</div>
                    <div>{importStatus.current}</div>

                    <div className="text-green-600">Created:</div>
                    <div className="text-green-600">{importStatus.created} employees</div>

                    <div className="text-blue-600">Updated:</div>
                    <div className="text-blue-600">{importStatus.updated} employees</div>

                    <div className="text-amber-600">Duplicates:</div>
                    <div className="text-amber-600">{importStatus.duplicates || 0} employees</div>

                    <div className="text-red-600">Failed:</div>
                    <div className="text-red-600">{importStatus.failed} employees</div>
                  </div>
                </AlertDescription>
              </Alert>

              {importStatus.details && (importStatus.details.created.length > 0 || importStatus.details.updated.length > 0) && (
                <div className="mt-4">
                  <Accordion type="single" collapsible className="w-full">
                    {importStatus.details.created.length > 0 && (
                      <AccordionItem value="created">
                        <AccordionTrigger>Created Employees ({importStatus.details.created.length})</AccordionTrigger>
                        <AccordionContent>
                          <div className="max-h-40 overflow-y-auto">
                            <ul className="list-disc list-inside">
                              {importStatus.details.created.map((item, index) => (
                                <li key={`created-${index}`} className="text-sm">{item}</li>
                              ))}
                            </ul>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}

                    {importStatus.details.updated.length > 0 && (
                      <AccordionItem value="updated">
                        <AccordionTrigger>Updated Employees ({importStatus.details.updated.length})</AccordionTrigger>
                        <AccordionContent>
                          <div className="max-h-40 overflow-y-auto">
                            <ul className="list-disc list-inside">
                              {importStatus.details.updated.map((item, index) => (
                                <li key={`updated-${index}`} className="text-sm">{item}</li>
                              ))}
                            </ul>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}
                  </Accordion>
                </div>
              )}

              {importStatus.failed > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <p>Failed: {importStatus.failed} employees</p>
                    <div className="mt-2 max-h-40 overflow-y-auto">
                      {importStatus.errors.map((error, index) => (
                        <p key={index} className="text-sm">{error}</p>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}











