import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';



// GET: Fetch a specific guru salary record
export async function GET(request: NextRequest) {
  try {
    // Extract ID from URL path
    const id = parseInt(request.nextUrl.pathname.split('/').pop() || '0');

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    const guruSalary = await prisma.salaryGuru.findUnique({
      where: { id },
    });

    if (!guruSalary) {
      return NextResponse.json({ error: 'Guru salary not found' }, { status: 404 });
    }

    return NextResponse.json(guruSalary);
  } catch (error) {
    console.error('Failed to fetch guru salary:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch guru salary',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PUT: Update a specific guru salary record
export async function PUT(request: NextRequest) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Extract ID from URL path
    const id = parseInt(request.nextUrl.pathname.split('/').pop() || '0');

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    const data = await request.json();
    const { month, year, ...salaryData } = data;

    // Set period date if provided
    const updateData = { ...salaryData };
    if (month !== undefined && year !== undefined) {
      updateData.period = new Date(year, month, 1);
    }

    const updatedSalary = await prisma.salaryGuru.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json(updatedSalary);
  } catch (error) {
    console.error('Failed to update guru salary:', error);
    return NextResponse.json(
      {
        error: 'Failed to update guru salary',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE: Delete a specific guru salary record
export async function DELETE(_request: NextRequest) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Extract ID from URL path
    const id = parseInt(_request.nextUrl.pathname.split('/').pop() || '0');

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    await prisma.salaryGuru.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete guru salary:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete guru salary',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
