import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';



export async function POST(request: Request) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Read file
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: 'array' });

    // Get first sheet
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];

    // Convert to JSON
    const data = XLSX.utils.sheet_to_json(sheet);

    if (!data || data.length === 0) {
      return NextResponse.json({ error: 'No data found in file' }, { status: 400 });
    }

    // Process data
    let imported = 0;
    let errors = 0;

    // Debug: Log the first row to see the structure
    if (data.length > 0) {
      console.log('First row of data:', JSON.stringify(data[0], null, 2));
    }

    for (const row of data as Record<string, any>[]) {
      try {
        // Map Excel columns to database fields
        const employeeIdValue = String(row['EmployeeId'] || '');

        // Check if employeeId exists in the database
        if (employeeIdValue) {
          // Try to find by employee_id (string format)
          const employeeByCode = await prisma.employee.findFirst({
            where: {
              employeeId: employeeIdValue,
              isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
            }
          });

          if (employeeByCode) {
            // Found by employee_id
            row._employeeId = employeeByCode.id; // Store the actual ID for later use
          } else {
            // Try to find by numeric ID
            const employeeId = parseInt(employeeIdValue);
            if (!isNaN(employeeId) && employeeId > 0) {
              const employeeById = await prisma.employee.findFirst({
                where: {
                  id: employeeId,
                  isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
                }
              });

              if (employeeById) {
                // Found by numeric ID
                row._employeeId = employeeById.id;
              } else {
                console.error(`Employee with ID ${employeeIdValue} not found or has been soft-deleted`);
                // Tetap lanjutkan, tapi dengan employeeId null
                row._employeeId = null;
                // Tambahkan warning ke log
                console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
              }
            } else {
              console.error(`Invalid employee ID format: ${employeeIdValue}`);
              // Tetap lanjutkan, tapi dengan employeeId null
              row._employeeId = null;
              // Tambahkan warning ke log
              console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
            }
          }
        } else {
          console.error('No employee ID provided');
          // Tetap lanjutkan, tapi dengan employeeId null
          row._employeeId = null;
          // Tambahkan warning ke log
          console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
        }

        // Helper function to safely parse numeric values
        const parseNumber = (value: any): number => {
          if (value === undefined || value === null || value === '') return 0;
          if (typeof value === 'number') return value;
          if (typeof value === 'string') {
            // Remove any non-numeric characters except decimal point
            const cleanedValue = value.replace(/[^0-9.]/g, '');
            return parseFloat(cleanedValue) || 0;
          }
          return 0;
        };


        const salaryData = {
          employeeId: row._employeeId || null,
          nama: String(row['Nama'] || ''),
          gp1: parseNumber(row['GP1']),
          pph_dibayar_sklh: parseNumber(row['PPh Dibayar Sekolah']),
          tot_tun_tep: parseNumber(row['Total Tunjangan Tetap']),
          hnr: parseNumber(row['Honor']),
          jp: parseNumber(row['JP']),
          tot_hnr: parseNumber(row['Total Honor']),
          jlh_hdr: parseInt(String(row['Jumlah Hadir'] || '0')),
          hnr_hdr: parseNumber(row['Honor Hadir']),
          tot_hnr_hdr: parseNumber(row['Total Honor Hadir']),
          sblm_pph: parseNumber(row['Sebelum PPh']),
          jlhxc: parseInt(String(row['Jumlah XC'] || '0')),
          xc: parseNumber(row['XC']),
          tmbhn: parseNumber(row['Tambahan']),
          gaji_bruto: parseNumber(row['Gaji Bruto']),
          bruto_stlh_pot: parseNumber(row['Bruto Setelah Potongan']),
          nettstlhjamsostek: parseNumber(row['Nett Setelah Jamsostek']),
          pph21: parseNumber(row['PPh21']),
          pinj_lainnya: parseNumber(row['Pinjaman Lainnya']),
          iuran_wajib: parseNumber(row['Iuran Wajib']),
          pinj_kop: parseNumber(row['Pinjaman Koperasi']),
          piutang: parseNumber(row['Piutang']),
          pot_bank: parseNumber(row['Potongan Bank']),
          gaji_netto: parseNumber(row['Gaji Netto']),
          period: parsePeriod(String(row['Periode'] || '')),
        };

        // Debug: Log the processed data
        console.log('Processed salary data:', JSON.stringify(salaryData, null, 2));

        // Create record
        await prisma.salaryHonor.create({
          data: salaryData,
        });

        imported++;
      } catch (error) {
        console.error('Error importing row:', error);
        errors++;
      }
    }

    return NextResponse.json({ imported, errors });
  } catch (error) {
    console.error('Failed to import honor salary data:', error);
    return NextResponse.json(
      {
        error: 'Failed to import honor salary data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Helper function to parse period from string like "1/2023" (month/year)
function parsePeriod(periodStr: string): Date {
  if (!periodStr) {
    return new Date();
  }

  try {
    // Log the period string for debugging
    console.log('Parsing period string:', periodStr);

    // Handle different formats
    if (periodStr.includes('/')) {
      const parts = periodStr.split('/');
      if (parts.length === 2) {
        const month = parseInt(parts[0]) - 1; // 0-based month
        const year = parseInt(parts[1]);
        console.log(`Parsed month: ${month}, year: ${year}`);
        return new Date(year, month, 1);
      }
    } else if (periodStr.includes('-')) {
      const parts = periodStr.split('-');
      if (parts.length === 2) {
        const month = parseInt(parts[0]) - 1; // 0-based month
        const year = parseInt(parts[1]);
        console.log(`Parsed month: ${month}, year: ${year}`);
        return new Date(year, month, 1);
      }
    }

    // If we can't parse the string, log it and return current date
    console.error('Could not parse period string:', periodStr);
  } catch (error) {
    console.error('Error parsing period:', error);
  }

  return new Date();
}
