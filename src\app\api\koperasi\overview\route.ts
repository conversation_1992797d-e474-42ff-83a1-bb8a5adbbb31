import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { logger } from '@/lib/logger';



export async function GET() {
  logger.debug('GET /api/koperasi/overview - Start fetching overview');
  try {
    const [
      totalActiveMembers,
      totalInactiveMembers,
      totalSavings,
      totalOneTimeContributions,
      totalMonthlyContributions,
      totalOptionalContributions,
      recentTransactions
    ] = await Promise.all([
      // Get total active members
      prisma.koperasiMember.count({
        where: { status: 'active' }
      }),

      // Get total inactive members
      prisma.koperasiMember.count({
        where: { status: 'inactive' }
      }),

      // Get total savings from KoperasiMember
      prisma.koperasiMember.aggregate({
        where: { status: 'active' },
        _sum: {
          totalSavings: true
        }
      }).then(result => {
        console.log('Total savings query result:', result);
        return result;
      }),

      // Get total one-time contributions
      prisma.koperasiMember.aggregate({
        where: { status: 'active' },
        _sum: {
          oneTimeContribution: true
        }
      }),

      // Get total monthly contributions
      prisma.koperasiMember.aggregate({
        where: { status: 'active' },
        _sum: {
          monthlyContribution: true
        }
      }),

      // Get total optional contributions
      prisma.koperasiMember.aggregate({
        where: { status: 'active' },
        _sum: {
          optionalContribution: true
        }
      }),



      // Get recent transactions (last 5)
      prisma.koperasiSaving.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          member: {
            include: {
              employee: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        }
      })
    ]);

    // Format recent transactions
    const formattedTransactions = recentTransactions ? recentTransactions.map(transaction => ({
      id: transaction.id,
      member_name: `${transaction.member?.employee?.firstName || ''} ${transaction.member?.employee?.lastName || ''}`.trim(),
      amount: Number(transaction.amount) || 0,
      type: transaction.type || 'deposit',
      contributionType: transaction.contributionType,
      date: transaction.date || new Date(),
      created_at: transaction.createdAt || new Date()
    })) : [];

    console.log('Overview query results:', {
      totalActiveMembers,
      totalInactiveMembers,
      totalSavings: totalSavings._sum,
      totalOneTimeContributions: totalOneTimeContributions._sum,
      totalMonthlyContributions: totalMonthlyContributions._sum,
      totalOptionalContributions: totalOptionalContributions._sum
    });

    const totalMembers = (totalActiveMembers || 0) + (totalInactiveMembers || 0);

    // Log overview data securely without exposing sensitive details
    logger.debug(`Overview data prepared with ${totalActiveMembers} active members`);

    // Hitung total savings dari komponen-komponennya jika totalSavings tidak tersedia
    const oneTimeContrib = Number(totalOneTimeContributions?._sum?.oneTimeContribution || 0);
    const monthlyContrib = Number(totalMonthlyContributions?._sum?.monthlyContribution || 0);
    const optionalContrib = Number(totalOptionalContributions?._sum?.optionalContribution || 0);
    const calculatedTotalSavings = oneTimeContrib + monthlyContrib + optionalContrib;

    // Gunakan totalSavings dari database jika tersedia, jika tidak gunakan hasil perhitungan
    const finalTotalSavings = Number(totalSavings?._sum?.totalSavings || 0) || calculatedTotalSavings;

    console.log('Calculated total savings:', {
      fromDatabase: Number(totalSavings?._sum?.totalSavings || 0),
      calculated: calculatedTotalSavings,
      final: finalTotalSavings
    });

    const overview = {
      // Member statistics
      totalMembers: totalMembers || 0,
      totalActiveMembers: totalActiveMembers || 0,
      totalInactiveMembers: totalInactiveMembers || 0,
      memberActivePercentage: totalMembers > 0 ? (totalActiveMembers / totalMembers) * 100 : 0,

      // Savings statistics
      totalSavings: finalTotalSavings,
      totalOneTimeContributions: oneTimeContrib,
      totalMonthlyContributions: monthlyContrib,
      totalOptionalContributions: optionalContrib,
      averageSavingsPerMember: totalActiveMembers > 0
        ? finalTotalSavings / totalActiveMembers
        : 0,

      // Loan statistics
      activeLoanCount: 0,
      completedLoanCount: 0,
      totalLoanAmount: 0,
      completedLoanAmount: 0,
      totalLoanCount: 0,

      // Recent transactions
      recentTransactions: formattedTransactions || [],

      // Legacy fields for backward compatibility
      membershipGrowth: 0,
      loanApprovalRate: 0,
      monthlyContributions: monthlyContrib
    };

    return NextResponse.json(overview);
  } catch (error) {
    logger.error('GET /api/koperasi/overview - Failed to fetch overview:', error);
    return NextResponse.json({ error: 'Failed to fetch overview' }, { status: 500 });
  }
}

