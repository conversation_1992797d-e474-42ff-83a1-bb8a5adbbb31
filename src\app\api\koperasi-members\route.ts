import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function POST(request: Request) {
  try {
    // Verify admin access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie || JSON.parse(userCookie.value).role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { employeeId, monthlyContribution } = data;

    // Validate required fields
    if (!employeeId || !monthlyContribution) {
      return NextResponse.json(
        { error: 'Employee ID and monthly contribution are required' },
        { status: 400 }
      );
    }

    // Check if employee is already a member
    const existingMember = await prisma.koperasiMember.findUnique({
      where: { employeeId }
    });

    if (existingMember) {
      return NextResponse.json(
        { error: 'Employee is already a koperasi member' },
        { status: 400 }
      );
    }

    // Create new koperasi member
    const member = await prisma.koperasiMember.create({
      data: {
        employee: { connect: { id: employeeId } },
        joinDate: new Date(),
        monthlyContribution: parseFloat(monthlyContribution),
        totalSavings: 0,
        status: 'active'
      }
    });

    return NextResponse.json(member);
  } catch (error) {
    console.error('Failed to create koperasi member:', error);
    return NextResponse.json(
      { error: 'Failed to create koperasi member' },
      { status: 500 }
    );
  }
}