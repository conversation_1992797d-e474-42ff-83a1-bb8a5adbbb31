/**
 * API Route: GET /api/koperasi/savings/get
 * 
 * Deskripsi: Mengambil daftar simpanan koperasi
 * Penggunaan: Halaman daftar simpanan koperasi
 * 
 * Query Parameters:
 * - memberId: ID anggota koperasi (opsional)
 * - type: <PERSON><PERSON><PERSON> simpanan ('deposit' atau 'withdrawal', opsional)
 * 
 * Response:
 * - 200: Daftar simpanan koperasi
 * - 401: Tidak terautentikasi
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function GET(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('memberId');
    const type = searchParams.get('type');
    
    // Build where clause
    const whereClause: any = {};
    
    if (memberId) {
      whereClause.memberId = parseInt(memberId);
    }
    
    if (type && ['deposit', 'withdrawal'].includes(type)) {
      whereClause.type = type;
    }

    const savings = await prisma.koperasiSaving.findMany({
      where: whereClause,
      include: {
        member: {
          include: {
            employee: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Format response
    const formattedSavings = savings.map(saving => ({
      id: saving.id,
      member_id: saving.memberId,
      member_name: `${saving.member.employee.firstName} ${saving.member.employee.lastName}`,
      amount: Number(saving.amount),
      type: saving.type,
      date: saving.date,
      notes: saving.notes,
      created_at: saving.createdAt,
      updated_at: saving.updatedAt
    }));

    return NextResponse.json(formattedSavings);
  } catch (error) {
    console.error('Error fetching koperasi savings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch koperasi savings' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
