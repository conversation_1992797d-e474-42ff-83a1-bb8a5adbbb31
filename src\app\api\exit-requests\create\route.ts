import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';

// Helper function to convert time string (HH:MM) to minutes
function convertTimeToMinutes(timeStr: string): number {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}



export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { employeeId, exitType, exitDate, exitTime, comebackTime, notComeback, reason } = data;

    // Validate required fields
    if (!employeeId || !exitType || !exitDate || !exitTime) {
      return NextResponse.json(
        { error: 'Employee ID, exit type, date, and exit time are required' },
        { status: 400 }
      );
    }

    // Validate exit time and comeback time
    if (!notComeback && comebackTime) {
      const exitTimeMinutes = convertTimeToMinutes(exitTime);
      const comebackTimeMinutes = convertTimeToMinutes(comebackTime);

      if (comebackTimeMinutes <= exitTimeMinutes) {
        return NextResponse.json(
          { error: 'Comeback time must be later than exit time' },
          { status: 400 }
        );
      }
    }

    // Create exit request
    const exitRequest = await prisma.exitRequest.create({
      data: {
        employeeId: parseInt(employeeId),
        exitType,
        exitDate: new Date(exitDate),
        exitTime,
        comebackTime: notComeback ? '16:00' : comebackTime,
        notComeback,
        reason,
        status: 'pending',
      },
    });

    return NextResponse.json({
      message: 'Exit request created successfully',
      exitRequest,
    });
  } catch (error) {
    console.error('Error creating exit request:', error);
    return NextResponse.json(
      { error: 'Failed to create exit request' },
      { status: 500 }
    );
  }
}
