import { Construction } from "lucide-react";

interface LoansTabProps {
  loans?: any[];
  userRole?: string;
  onLoanSubmit?: (data: any) => Promise<void>;
  onRefresh?: () => Promise<void>;
}

export function LoansTab({ loans, userRole, onLoanSubmit, onRefresh }: LoansTabProps) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Loans</h2>
      </div>

      <div className="flex flex-col items-center justify-center p-10 border border-dashed rounded-lg bg-muted/50">
        <Construction className="h-16 w-16 mb-4 text-muted-foreground" />
        <h3 className="text-2xl font-bold mb-2">Under Construction</h3>
        <p className="text-center text-muted-foreground max-w-md mb-6">
          The Loans feature is currently under development and will be available in a future update.
        </p>
        <p className="text-sm text-muted-foreground">
          Coming Soon
        </p>
      </div>
    </div>
  );
}
