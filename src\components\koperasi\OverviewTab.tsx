import type { KoperasiOverview, KoperasiTransaction } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  CreditCard,
  DollarSign,
  Users,
  AlertCircle,
  CheckCircle2,
  History,
  UserCheck,
  UserX,
  ArrowUp,
  ArrowDown,
  Calendar
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

interface OverviewTabProps {
  overview: KoperasiOverview;
}

// Helper function to format currency
const formatCurrency = (amount: number | undefined) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    maximumFractionDigits: 0,
  }).format(amount || 0);
};

// Helper function to format date
const formatDate = (date: Date | undefined) => {
  if (!date) return "-";
  return format(new Date(date), "dd/MM/yyyy");
};

export function OverviewTab({ overview }: OverviewTabProps) {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-blue-800 flex items-center">
        <span className="bg-blue-100 text-blue-800 p-2 rounded-lg mr-2">
          <DollarSign className="h-6 w-6" />
        </span>
        Koperasi Overview
      </h2>

      {/* Member Statistics */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold flex items-center">
          <Users className="h-5 w-5 text-blue-500 mr-2" />
          <span className="text-blue-700">Member Statistics</span>
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalMembers}</div>
              <p className="text-xs">
                <span className="text-green-600 font-medium">Active: {overview.totalActiveMembers}</span> | <span className="text-red-600 font-medium">Inactive: {overview.totalInactiveMembers}</span>
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Members</CardTitle>
              <UserCheck className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalActiveMembers}</div>
              <p className="text-xs">
                <span className="text-green-600 font-medium">{(overview.memberActivePercentage || 0).toFixed(1)}%</span> of total members
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Members</CardTitle>
              <UserX className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalInactiveMembers}</div>
              <p className="text-xs">
                <span className="text-red-600 font-medium">{(100 - (overview.memberActivePercentage || 0)).toFixed(1)}%</span> of total members
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Savings Statistics */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold flex items-center">
          <DollarSign className="h-5 w-5 text-emerald-500 mr-2" />
          <span className="text-emerald-700">Savings Statistics</span>
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Savings</CardTitle>
              <DollarSign className="h-4 w-4 text-emerald-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(overview.totalSavings)}
              </div>
              <p className="text-xs">
                Average: <span className="text-emerald-600 font-medium">{formatCurrency(overview.averageSavingsPerMember)}</span> per member
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">One-Time Contributions</CardTitle>
              <DollarSign className="h-4 w-4 text-indigo-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(overview.totalOneTimeContributions)}
              </div>
              <p className="text-xs">
                <span className="text-indigo-600 font-medium">{overview.totalSavings > 0 ? ((overview.totalOneTimeContributions || 0) / overview.totalSavings * 100).toFixed(1) : '0'}%</span> of total savings
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Contributions</CardTitle>
              <DollarSign className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(overview.totalMonthlyContributions)}
              </div>
              <p className="text-xs">
                <span className="text-purple-600 font-medium">{overview.totalSavings > 0 ? ((overview.totalMonthlyContributions || 0) / overview.totalSavings * 100).toFixed(1) : '0'}%</span> of total savings
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Optional Contributions</CardTitle>
              <DollarSign className="h-4 w-4 text-teal-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(overview.totalOptionalContributions)}
              </div>
              <p className="text-xs">
                <span className="text-teal-600 font-medium">{overview.totalSavings > 0 ? ((overview.totalOptionalContributions || 0) / overview.totalSavings * 100).toFixed(1) : '0'}%</span> of total savings
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Loan Statistics */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold flex items-center">
          <CreditCard className="h-5 w-5 text-amber-500 mr-2" />
          <span className="text-amber-700">Loan Statistics</span>
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Loans</CardTitle>
              <CreditCard className="h-4 w-4 text-amber-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalLoanCount}</div>
              <p className="text-xs">
                <span className="text-orange-600 font-medium">Active: {overview.activeLoanCount}</span> | <span className="text-green-600 font-medium">Completed: {overview.completedLoanCount}</span>
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Outstanding Loans</CardTitle>
              <AlertCircle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(overview.totalLoanAmount)}
              </div>
              <p className="text-xs">
                <span className="text-orange-600 font-medium">{overview.activeLoanCount}</span> active loans
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Loans</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(overview.completedLoanAmount)}
              </div>
              <p className="text-xs">
                <span className="text-green-600 font-medium">{overview.completedLoanCount}</span> completed loans
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold flex items-center">
          <History className="h-5 w-5 text-blue-500 mr-2" />
          <span className="text-blue-700">Recent Transactions</span>
        </h3>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <History className="h-5 w-5 text-blue-500 mr-2" />
              <span>Transaction History</span>
            </CardTitle>
            <CardDescription className="text-blue-400">The 5 most recent transactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {overview.recentTransactions && overview.recentTransactions.length > 0 ? (
                overview.recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center space-x-3">
                      {transaction.type === 'deposit' ? (
                        <ArrowUp className="h-5 w-5 text-green-500" />
                      ) : (
                        <ArrowDown className="h-5 w-5 text-red-500" />
                      )}
                      <div>
                        <p className="font-medium">{transaction.member_name}</p>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-3 w-3 text-blue-400" />
                          <p className="text-xs text-blue-400">{formatDate(transaction.date)}</p>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.type === 'deposit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </p>
                      {transaction.contributionType && (
                        <Badge variant="outline" className="text-xs">
                          {transaction.contributionType === 'one_time' ? 'One Time' :
                           transaction.contributionType === 'monthly' ? 'Monthly' : 'Optional'}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-gray-500 italic">No recent transactions</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
