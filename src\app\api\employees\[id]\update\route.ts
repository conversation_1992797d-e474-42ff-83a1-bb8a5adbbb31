/**
 * API Route: PATCH /api/employees/[id]/update
 *
 * Deskripsi: Memperbarui data karyawan berdasarkan ID
 * Penggunaan: Form edit karyawan
 *
 * Path Parameters:
 * - id: ID karyawan (number)
 *
 * Body:
 * - firstName: <PERSON><PERSON> depan (string)
 * - lastName: <PERSON><PERSON> (string, opsional)
 * - email: <PERSON><PERSON> (string, opsional)
 * - phone: Nomor telepon (string, opsional)
 * - departmentId: ID departemen (number)
 * - positionId: ID posisi (number)
 * - dll.
 *
 * Response:
 * - 200: Karyawan berhasil diperbarui
 * - 400: Data tidak valid
 * - 404: Karyawan tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

export async function PATCH(request: NextRequest) {
  try {
    // Verifikasi akses admin/supervisor
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    const isAdmin = userData.role === 'ADMIN';

    // Extract ID from URL path
    const id = parseInt(request.nextUrl.pathname.split('/')[3] || '0');
    const data = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid employee ID' },
        { status: 400 }
      );
    }

    // Jika bukan admin, periksa apakah user adalah kepala departemen dari karyawan ini
    if (!isAdmin) {
      const employee = await prisma.employee.findUnique({
        where: { id },
        include: {
          department: true
        }
      });

      if (!employee) {
        return NextResponse.json(
          { error: 'Employee not found' },
          { status: 404 }
        );
      }

      // Periksa apakah user adalah kepala departemen dari karyawan ini
      if (employee.department.headId !== parseInt(userData.id)) {
        return NextResponse.json(
          { error: 'Forbidden' },
          { status: 403 }
        );
      }
    }

    // Convert departmentId, positionId, and academicYearId to integers
    const departmentId = data.departmentId ? parseInt(data.departmentId) : undefined;
    const positionId = data.positionId ? parseInt(data.positionId) : undefined;

    // Handle academicYearId - set to null if "none" or empty, otherwise convert to integer
    let academicYearId = null;
    if (data.academicYearId && data.academicYearId !== "none") {
      academicYearId = parseInt(data.academicYearId);
    }

    // Gunakan transaksi untuk memastikan update employee dan user berhasil atau gagal bersama
    const updatedEmployee = await prisma.$transaction(async (tx) => {
      // Update employee
      const employee = await tx.employee.update({
        where: { id },
        data: {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phone: data.phone,
          address: data.address,
          birthDate: data.birthDate ? new Date(data.birthDate) : undefined,
          birthPlace: data.birthPlace || undefined,
          gender: data.gender,
          religion: data.religion || undefined,
          maritalStatus: data.maritalStatus,
          educationLevel: data.educationLevel || undefined,
          educationMajor: data.educationMajor || undefined,
          educationInstitution: data.educationInstitution || undefined,
          identityType: data.identityType || undefined,
          identityNumber: data.identityNumber || undefined,
          npwp: data.npwp || undefined,
          bpjsKesehatan: data.bpjsKesehatan || undefined,
          bpjsKetenagakerjaan: data.bpjsKetenagakerjaan || undefined,
          bankName: data.bankName || undefined,
          bankAccount: data.bankAccount || undefined,
          city: data.city,
          state: data.state,
          postalCode: data.postalCode,
          country: data.country,
          emergencyContactName: data.emergencyContactName,
          emergencyContactPhone: data.emergencyContactPhone,
          departmentId: isAdmin ? departmentId : undefined, // Hanya admin yang bisa mengubah departemen
          positionId: isAdmin ? positionId : undefined, // Hanya admin yang bisa mengubah posisi
          academicYearId: isAdmin ? academicYearId : undefined, // Hanya admin yang bisa mengubah tahun akademik
          status: isAdmin ? data.status || undefined : undefined, // Hanya admin yang bisa mengubah status
          contractType: isAdmin ? data.contractType || undefined : undefined, // Hanya admin yang bisa mengubah tipe kontrak
        },
        include: {
          department: {
            select: {
              name: true,
            },
          },
          position: {
            select: {
              title: true,
            },
          },
          academicYear: {
            select: {
              id: true,
              ta: true,
              description: true,
            },
          },
          user: {
            select: {
              id: true,
              username: true,
              role: true
            }
          }
        },
      });

      // Jika role diubah dan user adalah admin, update role di tabel user
      if (isAdmin && data.role && employee.user) {
        console.log(`Updating user role from ${employee.user.role} to ${data.role}`);
        await tx.user.update({
          where: { id: employee.user.id },
          data: {
            role: data.role
          }
        });

        // Update role di objek employee untuk response
        employee.user.role = data.role;
      }

      return employee;
    });

    return NextResponse.json(updatedEmployee);
  } catch (error: any) {
    console.error('Error updating employee:', error);
    return NextResponse.json(
      { error: 'Failed to update employee' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
