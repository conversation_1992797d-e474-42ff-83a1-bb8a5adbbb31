import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const employees = await prisma.employee.findMany({
      where: {
        AND: [
          {
            // Exclude admin web and external members
            NOT: {
              OR: [
                { employeeId: { startsWith: "EMP" } }, // Mengecualikan admin web dan semua yang diawali EMP
                { employeeId: { startsWith: "EXT" } } // Mengecualikan anggota eksternal
              ]
            }
          },
          {
            isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
          },
          {
            OR: [
              // Uppercase variants
              { position: { title: { contains: 'KEPALA' } } },
              { position: { title: { contains: 'MANAGER' } } },
              { position: { title: { contains: 'DIREKTUR' } } },
              { position: { title: { contains: 'CHIEF' } } },
              { position: { title: { contains: 'KOORDINATOR' } } },
              { position: { title: { contains: 'PSIKOLOG' } } },
              { position: { title: { contains: 'HEADMASTER' } } },

              // Lowercase variants
              { position: { title: { contains: 'kepala' } } },
              { position: { title: { contains: 'manager' } } },
              { position: { title: { contains: 'direktur' } } },
              { position: { title: { contains: 'chief' } } },
              { position: { title: { contains: 'koordinator' } } },
              { position: { title: { contains: 'psikolog' } } },
              { position: { title: { contains: 'headmaster' } } }
            ]
          }
        ]
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        employeeId: true,  // Changed from nip to employeeId
        department: {
          select: {
            id: true,
            name: true
          }
        },
        position: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: {
        position: {
          title: 'asc'
        }
      }
    });

    return NextResponse.json(employees);

  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}



