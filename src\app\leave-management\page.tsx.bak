"use client";

import React, { useState, useEffect } from "react";
import Navbar from "@/components/layout/Navbar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  CalendarIcon,
  Clock,
  FileText,
  Plus,
  Filter,
  Download,
  MoreHorizontal,
  Eye,
  Pencil,
  Trash2,
  Check,
  X,
  Edit
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { addDays } from "date-fns";
import { useAuth } from "@/lib/auth";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useToast } from "@/components/ui/use-toast";
import { Toast } from "@/components/ui/toast";
import { LeaveTypeForm } from "@/components/forms/leave-type-form";
import { LeaveType } from "@/types/leave";
// import { Eye, Pencil, Trash2, MoreHorizontal } from "lucide-react"; // Removed

interface LeaveRequest {
  id: number;
  employee: {
    firstName: string;
    lastName: string;
  };
  leaveType: {
    name: string;
  };
  startDate: Date;
  endDate: Date;
  status: 'pending' | 'approved' | 'rejected';
}

const LeaveTypesSection = () => {
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [isAddLeaveTypeOpen, setIsAddLeaveTypeOpen] = useState(false);
  const [isEditLeaveTypeOpen, setIsEditLeaveTypeOpen] = useState(false);
  const [selectedLeaveType, setSelectedLeaveType] = useState<LeaveType | null>(null);
  const { toast } = useToast();

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      const response = await fetch('/api/leave-types');
      if (!response.ok) throw new Error('Failed to fetch leave types');
      const data = await response.json();
      setLeaveTypes(sortLeaveTypes(data));
    } catch (error) {
      console.error('Error fetching leave types:', error);
      toast({
        title: "Error",
        description: "Failed to fetch leave types",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchLeaveTypes();
  }, []);

  const handleEdit = (leaveType: LeaveType) => {
    setSelectedLeaveType(leaveType);
    setIsEditLeaveTypeOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this leave type?')) {
      return;
    }

    try {
      const response = await fetch(`/api/leave-types/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete leave type');
      }

      await fetchLeaveTypes(); // Refresh the list

      toast({
        title: "Success",
        description: "Leave type deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to delete leave type',
        variant: "destructive",
      });
    }
  };

  const handleEditSubmit = async (data: LeaveTypeFormData) => {
    if (!selectedLeaveType) return;

    try {
      console.log('Submitting data:', data); // Debug log

      const response = await fetch(`/api/leave-types/${selectedLeaveType.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update leave type');
      }

      await fetchLeaveTypes(); // Refresh the list
      setIsEditLeaveTypeOpen(false);
      setSelectedLeaveType(null);

      toast({
        title: "Success",
        description: "Leave type updated successfully",
      });
    } catch (error) {
      console.error('Error updating leave type:', error); // Debug log
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to update leave type',
        variant: "destructive",
      });
    }
  };

  // Helper function to sort leave types by name
  const sortLeaveTypes = (types: LeaveType[]) => {
    return [...types].sort((a, b) => a.name.localeCompare(b.name));
  };

  const handleAddLeaveType = async (data: LeaveTypeFormData) => {
    try {
      const response = await fetch('/api/leave-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create leave type');
      }

      await fetchLeaveTypes(); // Refresh the list
      setIsAddLeaveTypeOpen(false);

      toast({
        title: "Success",
        description: "Leave type created successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to create leave type',
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
      <div className="p-4 flex justify-between items-center border-b border-border">
        <h2 className="text-lg font-semibold">Leave Types</h2>
        <Dialog open={isAddLeaveTypeOpen} onOpenChange={setIsAddLeaveTypeOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" /> Add Leave Type
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Leave Type</DialogTitle>
              <DialogDescription>
                Create a new type of leave for employees to request.
              </DialogDescription>
            </DialogHeader>
            <LeaveTypeForm
              onSubmit={handleAddLeaveType}
              onCancel={() => setIsAddLeaveTypeOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Days Allowed</TableHead>
              <TableHead>Requires Approval</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {leaveTypes.map((leaveType) => (
              <TableRow key={leaveType.id}>
                <TableCell>{leaveType.name}</TableCell>
                <TableCell>{leaveType.daysAllowed}</TableCell>
                <TableCell>{leaveType.requiresApproval ? 'Yes' : 'No'}</TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
                    onClick={() => handleEdit(leaveType)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-900 hover:bg-red-50"
                    onClick={() => handleDelete(leaveType.id)}
                  >
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditLeaveTypeOpen} onOpenChange={setIsEditLeaveTypeOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Leave Type</DialogTitle>
            <DialogDescription>
              Make changes to the leave type details.
            </DialogDescription>
          </DialogHeader>
          <LeaveTypeForm
            onSubmit={handleEditSubmit}
            onCancel={() => {
              setIsEditLeaveTypeOpen(false);
              setSelectedLeaveType(null);
            }}
            initialData={selectedLeaveType}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default function LeaveManagementPage() {
  const { user } = useAuth();
  const userRole = user?.role;
  const { toast } = useToast();

  // State untuk data
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [lateRequests, setLateRequests] = useState<any[]>([]);
  const [exitRequests, setExitRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);

  // State untuk statistik
  const [stats, setStats] = useState({
    pendingLeaveRequests: 0,
    pendingLateRequests: 0,
    pendingExitRequests: 0
  });

  // State untuk form leave request
  const [leaveRequestForm, setLeaveRequestForm] = useState({
    employeeId: '',
    leaveTypeId: '',
    startdate: '',
    enddate: '',
    duration: '',
    reason: '',
    attachment: null as File | null
  });
  const [calculatedEndDate, setCalculatedEndDate] = useState<string>('');
  const [isAddLeaveRequestOpen, setIsAddLeaveRequestOpen] = useState(false);

  // State untuk dialog view dan edit leave request
  const [selectedLeaveRequest, setSelectedLeaveRequest] = useState<any>(null);
  const [isViewLeaveRequestOpen, setIsViewLeaveRequestOpen] = useState(false);
  const [isEditLeaveRequestOpen, setIsEditLeaveRequestOpen] = useState(false);

  // State untuk form edit leave request
  const [editLeaveForm, setEditLeaveForm] = useState({
    id: '',
    leaveTypeId: '',
    startdate: '',
    enddate: '',
    duration: '',
    reason: '',
    attachmentUrl: '',
    attachment: null as File | null
  });

  // State untuk form late request
  const [lateRequestForm, setLateRequestForm] = useState({
    employeeId: '',
    lateType: '',
    lateDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    estimatedTime: '08:00', // Set default ke jam 8 pagi
    reason: ''
  });

  // State untuk dialog late request
  const [isAddLateRequestOpen, setIsAddLateRequestOpen] = useState(false);
  const [selectedLateRequest, setSelectedLateRequest] = useState<any>(null);
  const [isViewLateRequestOpen, setIsViewLateRequestOpen] = useState(false);
  const [isEditLateRequestOpen, setIsEditLateRequestOpen] = useState(false);

  // State untuk form edit late request
  const [editLateForm, setEditLateForm] = useState({
    id: '',
    lateType: '',
    lateDate: '',
    estimatedTime: '',
    reason: ''
  });

  // State untuk form exit request
  const [exitRequestForm, setExitRequestForm] = useState({
    employeeId: '',
    exitType: '',
    exitDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    exitTime: '08:00', // Set default ke jam 8 pagi
    comebackTime: '16:00', // Set default ke jam 4 sore
    notComeback: false,
    reason: ''
  });

  // State untuk dialog exit request
  const [isAddExitRequestOpen, setIsAddExitRequestOpen] = useState(false);
  const [selectedExitRequest, setSelectedExitRequest] = useState<any>(null);
  const [isViewExitRequestOpen, setIsViewExitRequestOpen] = useState(false);
  const [isEditExitRequestOpen, setIsEditExitRequestOpen] = useState(false);

  // State untuk form edit exit request
  const [editExitForm, setEditExitForm] = useState({
    id: '',
    exitType: '',
    exitDate: '',
    exitTime: '',
    comebackTime: '',
    notComeback: false,
    reason: ''
  });

  // Fungsi untuk mengambil data leave requests
  const fetchLeaveRequests = async () => {
    try {
      const response = await fetch('/api/leave-management');
      const data = await response.json();
      setLeaveRequests(data);

      // Hitung jumlah leave requests yang pending untuk supervisor
      if (userRole === 'SUPERVISOR') {
        const pendingRequests = data.filter(req =>
          req.status === 'pending' &&
          req.employee !== user?.name
        );
        setStats(prev => ({ ...prev, pendingLeaveRequests: pendingRequests.length }));
      }
    } catch (error) {
      console.error('Failed to fetch leave requests:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data leave requests",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk mengambil data leave types
  const fetchLeaveTypes = async () => {
    try {
      const response = await fetch('/api/leave-types');
      if (!response.ok) throw new Error('Failed to fetch leave types');
      const data = await response.json();
      setLeaveTypes(data);
    } catch (error) {
      console.error('Error fetching leave types:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data leave types",
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk mengambil data employees (untuk admin)
  const fetchEmployees = async () => {
    if (userRole !== 'ADMIN') return;

    try {
      const response = await fetch('/api/employees');
      if (!response.ok) throw new Error('Failed to fetch employees');
      const data = await response.json();
      setEmployees(data);
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  // Fungsi untuk menghitung end date berdasarkan start date dan durasi (mengabaikan Sabtu dan Minggu)
  const calculateEndDate = (startDateStr: string, durationDays: number): string => {
    if (!startDateStr || durationDays <= 0) return '';

    const startDate = new Date(startDateStr);
    if (isNaN(startDate.getTime())) return '';

    // Cek apakah hari pertama adalah hari kerja
    const startDayOfWeek = startDate.getDay();
    let endDate = new Date(startDate);
    let daysAdded = 0;

    // Jika hari pertama adalah hari kerja, hitung sebagai 1 hari
    if (startDayOfWeek !== 0 && startDayOfWeek !== 6) {
      daysAdded = 1;
    }

    while (daysAdded < durationDays) {
      // Tambah 1 hari
      endDate.setDate(endDate.getDate() + 1);

      // Cek apakah hari ini bukan Sabtu (6) atau Minggu (0)
      const dayOfWeek = endDate.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        daysAdded++;
      }
    }

    // Format tanggal ke YYYY-MM-DD untuk input date
    return endDate.toISOString().split('T')[0];
  };

  // Handler untuk perubahan input form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    const fieldName = id.replace('-', '');

    // Update form state
    setLeaveRequestForm(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Jika start date atau duration diubah, hitung end date
    if (fieldName === 'startdate' || fieldName === 'duration') {
      const startDate = fieldName === 'startdate' ? value : leaveRequestForm.startdate;
      const duration = fieldName === 'duration' ? parseInt(value) || 0 : parseInt(leaveRequestForm.duration) || 0;

      if (startDate && duration > 0) {
        const newEndDate = calculateEndDate(startDate, duration);
        setLeaveRequestForm(prev => ({
          ...prev,
          [fieldName]: value,
          enddate: newEndDate
        }));
        setCalculatedEndDate(newEndDate);
      } else if (fieldName === 'startdate') {
        // Jika hanya start date yang diubah tapi belum ada duration
        setLeaveRequestForm(prev => ({
          ...prev,
          [fieldName]: value,
          enddate: ''
        }));
        setCalculatedEndDate('');
      }
    }
  };

  // Handler untuk perubahan select
  const handleSelectChange = (name: string, value: string) => {
    setLeaveRequestForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setLeaveRequestForm(prev => ({
        ...prev,
        attachment: e.target.files![0]
      }));
    }
  };

  // Handler untuk submit form
  const handleSubmitLeaveRequest = async () => {
    // Validasi form
    if (!leaveRequestForm.leaveTypeId) {
      toast({
        title: "Error",
        description: "Silakan pilih tipe cuti",
        variant: "destructive",
      });
      return;
    }

    if (!leaveRequestForm.startdate) {
      toast({
        title: "Error",
        description: "Tanggal mulai harus diisi",
        variant: "destructive",
      });
      return;
    }

    if (!leaveRequestForm.duration || parseInt(leaveRequestForm.duration) <= 0) {
      toast({
        title: "Error",
        description: "Lama leave harus diisi dengan nilai lebih dari 0",
        variant: "destructive",
      });
      return;
    }

    if (!leaveRequestForm.enddate) {
      toast({
        title: "Error",
        description: "Tanggal akhir belum dihitung, silakan periksa input lama cuti",
        variant: "destructive",
      });
      return;
    }

    // Jika user bukan admin, gunakan ID user saat ini
    const employeeId = userRole === 'ADMIN' ? leaveRequestForm.employeeId : user?.id;

    if (!employeeId) {
      toast({
        title: "Error",
        description: "ID karyawan tidak valid",
        variant: "destructive",
      });
      return;
    }

    try {
      // Jika ada attachment, upload file terlebih dahulu
      let attachmentUrl = null;
      if (leaveRequestForm.attachment) {
        const formData = new FormData();
        formData.append('file', leaveRequestForm.attachment);

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload attachment');
        }

        const uploadResult = await uploadResponse.json();
        attachmentUrl = uploadResult.url;
      }

      const response = await fetch('/api/leave-management/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId,
          leaveTypeId: leaveRequestForm.leaveTypeId,
          startDate: leaveRequestForm.startdate,
          endDate: leaveRequestForm.enddate,
          durationDays: parseInt(leaveRequestForm.duration),
          reason: leaveRequestForm.reason,
          attachmentUrl
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create leave request');
      }

      // Reset form dan tutup dialog
      setLeaveRequestForm({
        employeeId: '',
        leaveTypeId: '',
        startdate: '',
        enddate: '',
        duration: '',
        reason: '',
        attachment: null
      });
      setCalculatedEndDate('');
      setIsAddLeaveRequestOpen(false);

      // Ambil nama leave type untuk pesan sukses
      const leaveTypeName = leaveTypes.find(lt => lt.id.toString() === leaveRequestForm.leaveTypeId)?.name || 'leave';

      // Refresh data
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: `Permintaan ${leaveTypeName} berhasil dibuat`,
      });
    } catch (error) {
      console.error('Error creating leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal membuat permintaan cuti',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk mengambil data late requests
  const fetchLateRequests = async () => {
    try {
      const response = await fetch('/api/late-requests');
      const data = await response.json();
      setLateRequests(data);

      // Hitung jumlah late requests yang pending untuk supervisor
      if (userRole === 'SUPERVISOR') {
        const pendingRequests = data.filter(req =>
          req.status === 'pending' &&
          req.employee.firstName + ' ' + req.employee.lastName !== user?.name
        );
        setStats(prev => ({ ...prev, pendingLateRequests: pendingRequests.length }));
      }
    } catch (error) {
      console.error('Failed to fetch late requests:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data late requests",
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk mengambil data exit requests
  const fetchExitRequests = async () => {
    try {
      const response = await fetch('/api/exit-requests');
      const data = await response.json();

      // Pastikan data adalah array
      const exitRequestsArray = Array.isArray(data) ? data : [];
      setExitRequests(exitRequestsArray);

      // Hitung jumlah exit requests yang pending untuk supervisor
      if (userRole === 'SUPERVISOR') {
        const pendingRequests = exitRequestsArray.filter((req: any) =>
          req.status === 'pending' &&
          req.employee?.firstName + ' ' + req.employee?.lastName !== user?.name
        );
        setStats(prev => ({ ...prev, pendingExitRequests: pendingRequests.length }));
      }
    } catch (error) {
      console.error('Failed to fetch exit requests:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data exit requests",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    // Hanya fetch data jika user sudah login
    if (user) {
      fetchLeaveRequests();
      fetchLeaveTypes();
      fetchEmployees();
      fetchLateRequests();
      fetchExitRequests();
    }
  }, [user, userRole]);

  if (loading) {
    return <div>Loading...</div>;
  }

  const leaveEvents = leaveRequests.map(request => {
    // Pastikan tanggal diformat dengan benar
    const fromDate = new Date(request.startDate);
    fromDate.setHours(0, 0, 0, 0);

    const toDate = new Date(request.endDate);
    toDate.setHours(0, 0, 0, 0);

    return {
      id: `LV${String(request.id).padStart(3, '0')}`,
      employee: `${request.employee.firstName} ${request.employee.lastName}`,
      type: request.leaveType.name,
      from: fromDate,
      to: toDate,
      status: request.status
    };
  });

  // Function to check if a date has events with specific status
  const isDayWithApprovedEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'approved';
    });
  };

  const isDayWithPendingEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'pending';
    });
  };

  const isDayWithRejectedEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'rejected';
    });
  };

  // Custom modifiers for the calendar
  const modifiers = {
    hasApprovedEvent: (date: Date) => isDayWithApprovedEvent(date),
    hasPendingEvent: (date: Date) => isDayWithPendingEvent(date),
    hasRejectedEvent: (date: Date) => isDayWithRejectedEvent(date),
  };

  // Custom modifiers styles
  const modifiersStyles = {
    hasApprovedEvent: {
      backgroundColor: "rgba(34, 197, 94, 0.2)", // Green for approved
      borderRadius: "0",
    },
    hasPendingEvent: {
      backgroundColor: "rgba(234, 179, 8, 0.2)", // Yellow for pending
      borderRadius: "0",
    },
    hasRejectedEvent: {
      backgroundColor: "rgba(239, 68, 68, 0.2)", // Red for rejected
      borderRadius: "0",
    }
  };

  // Fungsi untuk menangani view, edit, delete, approve, dan reject leave request

  const handleView = async (id: string) => {
    try {
      // Ambil ID numerik dari format LVxxx
      const numericId = id.replace('LV', '').replace(/^0+/, '');

      // Ambil detail leave request dari API
      const response = await fetch(`/api/leave-management/${numericId}/get`);

      if (!response.ok) {
        throw new Error('Failed to fetch leave request details');
      }

      const detailedLeaveRequest = await response.json();

      // Cari leave request dari leaveEvents untuk data tambahan
      const leaveRequest = leaveEvents.find(leave => leave.id === id);

      if (leaveRequest && detailedLeaveRequest) {
        // Gabungkan data dari kedua sumber
        setSelectedLeaveRequest({
          ...leaveRequest,
          reason: detailedLeaveRequest.reason || '-',
          attachmentUrl: detailedLeaveRequest.attachmentUrl || null
        });
        setIsViewLeaveRequestOpen(true);
      }
    } catch (error) {
      console.error('Error fetching leave request details:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil detail permintaan leave",
        variant: "destructive",
      });
    }
  };


  const handleEdit = async (id: string) => {
    try {
      // Ambil ID numerik dari format LVxxx
      const numericId = id.replace('LV', '').replace(/^0+/, '');

      // Ambil detail leave request dari API
      const response = await fetch(`/api/leave-management/${numericId}/get`);

      if (!response.ok) {
        throw new Error('Failed to fetch leave request details');
      }

      const detailedLeaveRequest = await response.json();

      // Cari leave request dari leaveEvents untuk data tambahan
      const leaveRequest = leaveEvents.find(leave => leave.id === id);

      if (leaveRequest && detailedLeaveRequest) {
        // Set data untuk form edit
        setEditLeaveForm({
          id: numericId,
          leaveTypeId: detailedLeaveRequest.leaveTypeId.toString(),
          startdate: new Date(detailedLeaveRequest.startDate).toISOString().split('T')[0],
          enddate: new Date(detailedLeaveRequest.endDate).toISOString().split('T')[0],
          duration: '', // Akan dihitung berdasarkan start dan end date
          reason: detailedLeaveRequest.reason || '',
          attachmentUrl: detailedLeaveRequest.attachmentUrl || '',
          attachment: null
        });

        setSelectedLeaveRequest({
          ...leaveRequest,
          reason: detailedLeaveRequest.reason || '',
          attachmentUrl: detailedLeaveRequest.attachmentUrl || ''
        });
        setIsEditLeaveRequestOpen(true);
      }
    } catch (error) {
      console.error('Error fetching leave request details for edit:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil detail permintaan leave untuk diedit",
        variant: "destructive",
      });
    }
  };

  // Handler untuk perubahan input form edit
  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    const fieldName = id.replace('edit-', '');

    setEditLeaveForm(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Handler untuk file upload pada form edit
  const handleEditFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setEditLeaveForm(prev => ({
        ...prev,
        attachment: e.target.files![0],
        // Jangan hapus attachmentUrl yang lama sampai file baru berhasil diupload
      }));
    }
  };

  // Handler untuk menghapus attachment
  const handleRemoveAttachment = () => {
    setEditLeaveForm(prev => ({
      ...prev,
      attachmentUrl: '',
      attachment: null
    }));
  };

  // Handler untuk perubahan select pada form edit
  const handleEditSelectChange = (name: string, value: string) => {
    setEditLeaveForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk submit form edit
  const handleSubmitEditLeave = async () => {
    try {
      // Validasi form
      if (!editLeaveForm.leaveTypeId) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe leave",
          variant: "destructive",
        });
        return;
      }

      if (!editLeaveForm.startdate || !editLeaveForm.enddate) {
        toast({
          title: "Error",
          description: "Tanggal mulai dan selesai harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Validasi end date tidak boleh lebih kecil dari start date
      const startDate = new Date(editLeaveForm.startdate);
      const endDate = new Date(editLeaveForm.enddate);

      if (endDate < startDate) {
        toast({
          title: "Error",
          description: "Tanggal selesai tidak boleh lebih awal dari tanggal mulai",
          variant: "destructive",
        });
        return;
      }

      // Jika ada attachment baru, upload file terlebih dahulu
      let attachmentUrl = editLeaveForm.attachmentUrl;
      if (editLeaveForm.attachment) {
        const formData = new FormData();
        formData.append('file', editLeaveForm.attachment);

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload attachment');
        }

        const uploadResult = await uploadResponse.json();
        attachmentUrl = uploadResult.url;
      }

      // Kirim data ke API
      const response = await fetch(`/api/leave-management/${editLeaveForm.id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          leaveTypeId: parseInt(editLeaveForm.leaveTypeId),
          startDate: editLeaveForm.startdate,
          endDate: editLeaveForm.enddate,
          reason: editLeaveForm.reason,
          attachmentUrl
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update leave request');
      }

      // Refresh data setelah berhasil update
      await fetchLeaveRequests();

      // Tutup dialog dan reset form
      setIsEditLeaveRequestOpen(false);
      setSelectedLeaveRequest(null);

      toast({
        title: "Sukses",
        description: "Permintaan leave berhasil diperbarui",
      });
    } catch (error) {
      console.error('Error updating leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal memperbarui permintaan leave',
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: string) => {
    // Konfirmasi penghapusan
    if (!confirm('Apakah Anda yakin ingin menghapus permintaan leave ini?')) {
      return;
    }

    try {
      // Ambil ID numerik dari format LVxxx
      const numericId = id.replace('LV', '').replace(/^0+/, '');

      const response = await fetch(`/api/leave-management/${numericId}/delete`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete leave request');
      }

      // Refresh data setelah berhasil menghapus
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan leave berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menghapus permintaan leave',
        variant: "destructive",
      });
    }
  };

  const handleApprove = async (id: string) => {
    try {
      // Ambil ID numerik dari format LVxxx
      const numericId = id.replace('LV', '').replace(/^0+/, '');

      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/leave-management/${numericId}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name // Akan disimpan ke field reason
        }),
      });

      if (!response.ok) {
        console.log('Response not OK:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(errorData.error || 'Gagal menyetujui permintaan leave');
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
          throw new Error(`Gagal menyetujui permintaan leave: ${response.status} ${response.statusText}`);
        }
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan leave berhasil disetujui",
      });
    } catch (error) {
      console.error('Error approving leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menyetujui permintaan leave',
        variant: "destructive",
      });
    }
  };

  const handleReject = async (id: string) => {
    try {
      // Ambil ID numerik dari format LVxxx
      const numericId = id.replace('LV', '').replace(/^0+/, '');

      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/leave-management/${numericId}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name // Akan disimpan ke field reason
        }),
      });

      if (!response.ok) {
        console.log('Response not OK:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(errorData.error || 'Gagal menolak permintaan leave');
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
          throw new Error(`Gagal menolak permintaan leave: ${response.status} ${response.statusText}`);
        }
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan leave berhasil ditolak",
      });
    } catch (error) {
      console.error('Error rejecting leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menolak permintaan leave',
        variant: "destructive",
      });
    }
  };

  // Handler untuk input form late request
  const handleLateInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    const fieldName = id.replace('late-', '');

    setLateRequestForm(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Handler untuk select pada form late request
  const handleLateSelectChange = (name: string, value: string) => {
    setLateRequestForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk submit form late request
  const handleSubmitLateRequest = async () => {
    // Validasi form
    if (!lateRequestForm.lateType) {
      toast({
        title: "Error",
        description: "Silakan pilih tipe keterlambatan",
        variant: "destructive",
      });
      return;
    }

    if (!lateRequestForm.lateDate) {
      toast({
        title: "Error",
        description: "Tanggal keterlambatan harus diisi",
        variant: "destructive",
      });
      return;
    }

    if (!lateRequestForm.estimatedTime) {
      toast({
        title: "Error",
        description: "Estimasi waktu kedatangan harus diisi",
        variant: "destructive",
      });
      return;
    }

    // Jika user bukan admin, gunakan ID user saat ini
    const employeeId = userRole === 'ADMIN' ? lateRequestForm.employeeId : user?.id;

    if (!employeeId) {
      toast({
        title: "Error",
        description: "ID karyawan tidak valid",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch('/api/late-requests/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId,
          lateType: lateRequestForm.lateType,
          lateDate: lateRequestForm.lateDate,
          estimatedTime: lateRequestForm.estimatedTime,
          reason: lateRequestForm.reason
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create late request');
      }

      // Reset form dan tutup dialog
      setLateRequestForm({
        employeeId: '',
        lateType: '',
        lateDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
        estimatedTime: '08:00', // Set default ke jam 8 pagi
        reason: ''
      });
      setIsAddLateRequestOpen(false);

      // Refresh data
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: `Permintaan keterlambatan berhasil dibuat`,
      });
    } catch (error) {
      console.error('Error creating late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal membuat permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  // Handler untuk view, edit, delete, approve, dan reject late request
  const handleViewLate = async (id: number) => {
    try {
      // Ambil detail late request dari API
      const response = await fetch(`/api/late-requests/${id}/get`);

      if (!response.ok) {
        throw new Error('Failed to fetch late request details');
      }

      const lateRequest = await response.json();

      if (lateRequest) {
        setSelectedLateRequest(lateRequest);
        setIsViewLateRequestOpen(true);
      }
    } catch (error) {
      console.error('Error fetching late request details:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil detail permintaan keterlambatan",
        variant: "destructive",
      });
    }
  };

  const handleEditLate = async (id: number) => {
    try {
      // Ambil detail late request dari API
      const response = await fetch(`/api/late-requests/${id}/get`);

      if (!response.ok) {
        throw new Error('Failed to fetch late request details');
      }

      const lateRequest = await response.json();

      if (lateRequest) {
        // Set data untuk form edit
        setEditLateForm({
          id: id.toString(),
          lateType: lateRequest.lateType,
          lateDate: new Date(lateRequest.lateDate).toISOString().split('T')[0],
          estimatedTime: lateRequest.estimatedTime,
          reason: lateRequest.reason || ''
        });

        setSelectedLateRequest(lateRequest);
        setIsEditLateRequestOpen(true);
      }
    } catch (error) {
      console.error('Error fetching late request details for edit:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil detail permintaan keterlambatan untuk diedit",
        variant: "destructive",
      });
    }
  };

  // Handler untuk perubahan input form edit late
  const handleEditLateInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    const fieldName = id.replace('edit-late-', '');

    setEditLateForm(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Handler untuk perubahan select pada form edit late
  const handleEditLateSelectChange = (name: string, value: string) => {
    setEditLateForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk submit form edit late
  const handleSubmitEditLate = async () => {
    try {
      // Validasi form
      if (!editLateForm.lateType) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe keterlambatan",
          variant: "destructive",
        });
        return;
      }

      if (!editLateForm.lateDate) {
        toast({
          title: "Error",
          description: "Tanggal keterlambatan harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editLateForm.estimatedTime) {
        toast({
          title: "Error",
          description: "Estimasi waktu kedatangan harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Kirim data ke API
      const response = await fetch(`/api/late-requests/${editLateForm.id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lateType: editLateForm.lateType,
          lateDate: editLateForm.lateDate,
          estimatedTime: editLateForm.estimatedTime,
          reason: editLateForm.reason
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update late request');
      }

      // Refresh data setelah berhasil update
      await fetchLateRequests();

      // Tutup dialog dan reset form
      setIsEditLateRequestOpen(false);
      setSelectedLateRequest(null);

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil diperbarui",
      });
    } catch (error) {
      console.error('Error updating late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal memperbarui permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  const handleDeleteLate = async (id: number) => {
    // Konfirmasi penghapusan
    if (!confirm('Apakah Anda yakin ingin menghapus permintaan keterlambatan ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/late-requests/${id}/delete`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete late request');
      }

      // Refresh data setelah berhasil menghapus
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menghapus permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  const handleApproveLate = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/late-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name
        }),
      });

      if (!response.ok) {
        console.log('Response not OK:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(errorData.error || 'Gagal menyetujui permintaan keterlambatan');
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
          throw new Error(`Gagal menyetujui permintaan keterlambatan: ${response.status} ${response.statusText}`);
        }
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil disetujui",
      });
    } catch (error) {
      console.error('Error approving late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menyetujui permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  const handleRejectLate = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/late-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name
        }),
      });

      if (!response.ok) {
        console.log('Response not OK:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(errorData.error || 'Gagal menolak permintaan keterlambatan');
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
          throw new Error(`Gagal menolak permintaan keterlambatan: ${response.status} ${response.statusText}`);
        }
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil ditolak",
      });
    } catch (error) {
      console.error('Error rejecting late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menolak permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  // Handler untuk input form exit request
  const handleExitInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    const fieldName = id.replace('exit-', '');

    setExitRequestForm(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Handler untuk checkbox not comeback
  const handleNotComebackChange = (checked: boolean) => {
    setExitRequestForm(prev => ({
      ...prev,
      notComeback: checked,
      comebackTime: checked ? '16:00' : prev.comebackTime
    }));
  };

  // Handler untuk select pada form exit request
  const handleExitSelectChange = (name: string, value: string) => {
    setExitRequestForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Validasi exit time dan comeback time
  const validateExitTimes = (exitTime: string, comebackTime: string, notComeback: boolean): boolean => {
    if (notComeback) return true;

    const exitTimeMinutes = convertTimeToMinutes(exitTime);
    const comebackTimeMinutes = convertTimeToMinutes(comebackTime);

    return comebackTimeMinutes > exitTimeMinutes;
  };

  // Helper function to convert time string (HH:MM) to minutes
  const convertTimeToMinutes = (timeStr: string): number => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // Handler untuk submit form exit request
  const handleSubmitExitRequest = async () => {
    // Validasi form
    if (!exitRequestForm.exitType) {
      toast({
        title: "Error",
        description: "Silakan pilih tipe exit",
        variant: "destructive",
      });
      return;
    }

    if (!exitRequestForm.exitDate) {
      toast({
        title: "Error",
        description: "Tanggal exit harus diisi",
        variant: "destructive",
      });
      return;
    }

    if (!exitRequestForm.exitTime) {
      toast({
        title: "Error",
        description: "Waktu exit harus diisi",
        variant: "destructive",
      });
      return;
    }

    if (!exitRequestForm.notComeback && !exitRequestForm.comebackTime) {
      toast({
        title: "Error",
        description: "Waktu kembali harus diisi atau pilih Not Comeback",
        variant: "destructive",
      });
      return;
    }

    // Validasi exit time dan comeback time
    if (!validateExitTimes(exitRequestForm.exitTime, exitRequestForm.comebackTime, exitRequestForm.notComeback)) {
      toast({
        title: "Error",
        description: "Waktu kembali harus lebih besar dari waktu exit",
        variant: "destructive",
      });
      return;
    }

    // Jika user bukan admin, gunakan ID user saat ini
    const employeeId = userRole === 'ADMIN' ? exitRequestForm.employeeId : user?.id;

    if (!employeeId) {
      toast({
        title: "Error",
        description: "ID karyawan tidak valid",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch('/api/exit-requests/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId,
          exitType: exitRequestForm.exitType,
          exitDate: exitRequestForm.exitDate,
          exitTime: exitRequestForm.exitTime,
          comebackTime: exitRequestForm.comebackTime,
          notComeback: exitRequestForm.notComeback,
          reason: exitRequestForm.reason
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create exit request');
      }

      // Reset form dan tutup dialog
      setExitRequestForm({
        employeeId: '',
        exitType: '',
        exitDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
        exitTime: '08:00', // Set default ke jam 8 pagi
        comebackTime: '16:00', // Set default ke jam 4 sore
        notComeback: false,
        reason: ''
      });
      setIsAddExitRequestOpen(false);

      // Refresh data
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: `Permintaan exit from school berhasil dibuat`,
      });
    } catch (error) {
      console.error('Error creating exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal membuat permintaan exit from school',
        variant: "destructive",
      });
    }
  };

  // Handler untuk view, edit, delete, approve, dan reject exit request
  const handleViewExit = async (id: number) => {
    try {
      // Ambil detail exit request dari API
      const response = await fetch(`/api/exit-requests/${id}/get`);

      if (!response.ok) {
        throw new Error('Failed to fetch exit request details');
      }

      const exitRequest = await response.json();

      if (exitRequest) {
        setSelectedExitRequest(exitRequest);
        setIsViewExitRequestOpen(true);
      }
    } catch (error) {
      console.error('Error fetching exit request details:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil detail permintaan exit from school",
        variant: "destructive",
      });
    }
  };

  const handleEditExit = async (id: number) => {
    try {
      // Ambil detail exit request dari API
      const response = await fetch(`/api/exit-requests/${id}/get`);

      if (!response.ok) {
        throw new Error('Failed to fetch exit request details');
      }

      const exitRequest = await response.json();

      if (exitRequest) {
        // Set data untuk form edit
        setEditExitForm({
          id: id.toString(),
          exitType: exitRequest.exitType,
          exitDate: new Date(exitRequest.exitDate).toISOString().split('T')[0],
          exitTime: exitRequest.exitTime,
          comebackTime: exitRequest.comebackTime || '',
          notComeback: exitRequest.notComeback,
          reason: exitRequest.reason || ''
        });

        setSelectedExitRequest(exitRequest);
        setIsEditExitRequestOpen(true);
      }
    } catch (error) {
      console.error('Error fetching exit request details for edit:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil detail permintaan exit from school untuk diedit",
        variant: "destructive",
      });
    }
  };

  // Handler untuk perubahan input form edit exit
  const handleEditExitInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    const fieldName = id.replace('edit-exit-', '');

    setEditExitForm(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Handler untuk checkbox not comeback pada form edit
  const handleEditNotComebackChange = (checked: boolean) => {
    setEditExitForm(prev => ({
      ...prev,
      notComeback: checked,
      comebackTime: checked ? '16:00' : prev.comebackTime
    }));
  };

  // Handler untuk perubahan select pada form edit exit
  const handleEditExitSelectChange = (name: string, value: string) => {
    setEditExitForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk submit form edit exit
  const handleSubmitEditExit = async () => {
    try {
      // Validasi form
      if (!editExitForm.exitType) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe exit",
          variant: "destructive",
        });
        return;
      }

      if (!editExitForm.exitDate) {
        toast({
          title: "Error",
          description: "Tanggal exit harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editExitForm.exitTime) {
        toast({
          title: "Error",
          description: "Waktu exit harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editExitForm.notComeback && !editExitForm.comebackTime) {
        toast({
          title: "Error",
          description: "Waktu kembali harus diisi atau pilih Not Comeback",
          variant: "destructive",
        });
        return;
      }

      // Validasi exit time dan comeback time
      if (!validateExitTimes(editExitForm.exitTime, editExitForm.comebackTime, editExitForm.notComeback)) {
        toast({
          title: "Error",
          description: "Waktu kembali harus lebih besar dari waktu exit",
          variant: "destructive",
        });
        return;
      }

      // Kirim data ke API
      const response = await fetch(`/api/exit-requests/${editExitForm.id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          exitType: editExitForm.exitType,
          exitDate: editExitForm.exitDate,
          exitTime: editExitForm.exitTime,
          comebackTime: editExitForm.comebackTime,
          notComeback: editExitForm.notComeback,
          reason: editExitForm.reason
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update exit request');
      }

      // Refresh data setelah berhasil update
      await fetchExitRequests();

      // Tutup dialog dan reset form
      setIsEditExitRequestOpen(false);
      setSelectedExitRequest(null);

      toast({
        title: "Sukses",
        description: "Permintaan exit from school berhasil diperbarui",
      });
    } catch (error) {
      console.error('Error updating exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal memperbarui permintaan exit from school',
        variant: "destructive",
      });
    }
  };

  const handleDeleteExit = async (id: number) => {
    // Konfirmasi penghapusan
    if (!confirm('Apakah Anda yakin ingin menghapus permintaan exit from school ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/exit-requests/${id}/delete`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete exit request');
      }

      // Refresh data setelah berhasil menghapus
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan exit from school berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menghapus permintaan exit from school',
        variant: "destructive",
      });
    }
  };

  const handleApproveExit = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/exit-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name
        }),
      });

      if (!response.ok) {
        console.log('Response not OK:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(errorData.error || 'Gagal menyetujui permintaan exit from school');
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
          throw new Error(`Gagal menyetujui permintaan exit from school: ${response.status} ${response.statusText}`);
        }
      }

      // Refresh data setelah berhasil mengubah status
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan exit from school berhasil disetujui",
      });
    } catch (error) {
      console.error('Error approving exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menyetujui permintaan exit from school',
        variant: "destructive",
      });
    }
  };

  const handleRejectExit = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/exit-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name
        }),
      });

      if (!response.ok) {
        console.log('Response not OK:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(errorData.error || 'Gagal menolak permintaan exit from school');
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
          throw new Error(`Gagal menolak permintaan exit from school: ${response.status} ${response.statusText}`);
        }
      }

      // Refresh data setelah berhasil mengubah status
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan exit from school berhasil ditolak",
      });
    } catch (error) {
      console.error('Error rejecting exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menolak permintaan exit from school',
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={userRole} />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Leave Management</h1>
          <p className="text-muted-foreground mt-2">
            Track and manage employee leave requests
          </p>
          {userRole === 'SUPERVISOR' && (
            <div className="flex gap-4 mt-4 bg-card p-4 rounded-lg shadow-sm">
              <div className="text-sm">
                <span className="font-medium">Pending Leave Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingLeaveRequests}
                </span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Pending Late Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingLateRequests}
                </span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Pending Exit Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingExitRequests}
                </span>
              </div>
            </div>
          )}
        </div>

        <div className="mb-8">

          <Tabs defaultValue="leave-requests" className="w-full">
            <TabsList className="grid w-full md:w-auto md:inline-flex grid-cols-2 md:grid-cols-none mb-6">
              <TabsTrigger value="leave-requests" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Leave Requests
              </TabsTrigger>

              <TabsTrigger value="calendar" className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                Calendar View
              </TabsTrigger>

              <TabsTrigger value="late" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Late
              </TabsTrigger>

              <TabsTrigger value="exit" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Exit From School
              </TabsTrigger>

              {userRole === "ADMIN" && (
                <TabsTrigger value="leave-types" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Leave Types
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="leave-requests" className="space-y-4">
              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                  <div className="p-4 flex justify-between items-center border-b border-border">
                    <div>
                      <h2 className="text-lg font-semibold">Leave Requests</h2>
                      {userRole === 'EMPLOYEE' && (
                        <p className="text-xs text-muted-foreground">Showing your leave requests</p>
                      )}
                      {userRole === 'SUPERVISOR' && (
                        <p className="text-xs text-muted-foreground">Showing leave requests from your department</p>
                      )}
                      {userRole === 'ADMIN' && (
                        <p className="text-xs text-muted-foreground">Showing all leave requests</p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Dialog open={isAddLeaveRequestOpen} onOpenChange={setIsAddLeaveRequestOpen}>
                        <DialogTrigger asChild>
                          <Button className="flex items-center gap-2">
                            <Plus className="h-4 w-4" /> Add Leave Request
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add New Leave Request</DialogTitle>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            {userRole === "ADMIN" && (
                              <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="employee" className="text-right">
                                  Employee
                                </Label>
                                <Select
                                  onValueChange={(value) => handleSelectChange('employeeId', value)}
                                  value={leaveRequestForm.employeeId}
                                >
                                  <SelectTrigger className="col-span-3">
                                    <SelectValue placeholder="Select Employee" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {employees.map(employee => (
                                      <SelectItem key={employee.id} value={employee.id.toString()}>
                                        {employee.firstName} {employee.lastName}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            )}
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="leave-type" className="text-right">
                                Leave Type
                              </Label>
                              <Select
                                onValueChange={(value) => handleSelectChange('leaveTypeId', value)}
                                value={leaveRequestForm.leaveTypeId}
                              >
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select Leave Type" />
                                </SelectTrigger>
                                <SelectContent>
                                  {leaveTypes.map(leaveType => (
                                    <SelectItem key={leaveType.id} value={leaveType.id.toString()}>
                                      {leaveType.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="start-date" className="text-right">
                                Start Date
                              </Label>
                              <Input
                                id="start-date"
                                type="date"
                                className="col-span-3"
                                value={leaveRequestForm.startdate}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="duration" className="text-right">
                                Lama Leave (hari)
                              </Label>
                              <div className="col-span-3">
                                <Input
                                  id="duration"
                                  type="number"
                                  min="1"
                                  className="w-full"
                                  value={leaveRequestForm.duration}
                                  onChange={handleInputChange}
                                  placeholder="Masukkan jumlah hari leave"
                                />
                                <p className="text-xs text-muted-foreground mt-1">
                                  *Tidak termasuk hari Sabtu dan Minggu
                                </p>
                              </div>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="end-date" className="text-right">
                                End Date
                              </Label>
                              <div className="col-span-3">
                                <Input
                                  id="end-date"
                                  type="date"
                                  className="w-full"
                                  value={leaveRequestForm.enddate}
                                  readOnly
                                />
                                {calculatedEndDate && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    Tanggal akhir leave: {new Date(calculatedEndDate).toLocaleDateString('id-ID', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="reason" className="text-right">
                                Reason
                              </Label>
                              <Textarea
                                id="reason"
                                className="col-span-3"
                                placeholder="Reason for leave"
                                value={leaveRequestForm.reason}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="attachment" className="text-right">
                                Attachment
                              </Label>
                              <div className="col-span-3">
                                <Input
                                  id="attachment"
                                  type="file"
                                  className="w-full"
                                  onChange={handleFileChange}
                                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                />
                                <p className="text-xs text-muted-foreground mt-1">
                                  *Opsional. Upload surat dokter atau dokumen pendukung lainnya (PDF, JPG, PNG, DOC).
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsAddLeaveRequestOpen(false)}>Cancel</Button>
                            <Button onClick={handleSubmitLeaveRequest}>Submit Request</Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                      {userRole === "ADMIN" && (
                        <>
                          <Button variant="outline" size="sm">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Employee</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>From</TableHead>
                          <TableHead>To</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {leaveEvents.map((leave) => (
                          <TableRow key={leave.id}>
                            <TableCell>{leave.id}</TableCell>
                            <TableCell>{leave.employee}</TableCell>
                            <TableCell>{leave.type}</TableCell>
                            <TableCell>{new Date(leave.from).toLocaleDateString()}</TableCell>
                            <TableCell>{new Date(leave.to).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                leave.status === 'approved'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                  : leave.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              }`}>
                                {leave.status.charAt(0).toUpperCase() + leave.status.slice(1)}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleView(leave.id)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  {/* ADMIN dapat approve/reject semua leave request yang masih pending */}
                                  {userRole === "ADMIN" && leave.status === "pending" && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleApprove(leave.id)}>
                                        <Check className="mr-2 h-4 w-4" />
                                        Approve
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleReject(leave.id)}>
                                        <X className="mr-2 h-4 w-4" />
                                        Reject
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleEdit(leave.id)}>
                                        <Pencil className="mr-2 h-4 w-4" />
                                        Edit Permintaan
                                      </DropdownMenuItem>
                                    </>
                                  )}

                                  {/* SUPERVISOR: Cek apakah leave request adalah milik bawahannya */}
                                  {userRole === "SUPERVISOR" && (
                                    <>
                                      {/* Jika leave request bukan milik supervisor sendiri (milik bawahan) dan masih pending */}
                                      {leave.employee !== user?.name && leave.status === "pending" && (
                                        <>
                                          <DropdownMenuItem onClick={() => handleApprove(leave.id)}>
                                            <Check className="mr-2 h-4 w-4" />
                                            Approve
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => handleReject(leave.id)}>
                                            <X className="mr-2 h-4 w-4" />
                                            Reject
                                          </DropdownMenuItem>
                                        </>
                                      )}

                                      {/* Jika leave request milik supervisor sendiri dan masih pending */}
                                      {leave.employee === user?.name && leave.status === "pending" && (
                                        <DropdownMenuItem onClick={() => handleEdit(leave.id)}>
                                          <Pencil className="mr-2 h-4 w-4" />
                                          Edit Permintaan Saya
                                        </DropdownMenuItem>
                                      )}
                                    </>
                                  )}

                                  {/* EMPLOYEE dapat mengedit leave request miliknya sendiri yang masih pending */}
                                  {userRole === "EMPLOYEE" && leave.status === "pending" && (
                                    <DropdownMenuItem onClick={() => handleEdit(leave.id)}>
                                      <Pencil className="mr-2 h-4 w-4" />
                                      Edit Permintaan
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    <div className="flex items-center justify-center py-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious href="#" />
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#" isActive>
                              1
                            </PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#">2</PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#">3</PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationNext href="#" />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="late" className="space-y-4">
              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                  <div className="p-4 flex justify-between items-center border-b border-border">
                    <div>
                      <h2 className="text-lg font-semibold">Late Requests</h2>
                      {userRole === 'EMPLOYEE' && (
                        <p className="text-xs text-muted-foreground">Showing your late requests</p>
                      )}
                      {userRole === 'SUPERVISOR' && (
                        <p className="text-xs text-muted-foreground">Showing late requests from your department</p>
                      )}
                      {userRole === 'ADMIN' && (
                        <p className="text-xs text-muted-foreground">Showing all late requests</p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Dialog open={isAddLateRequestOpen} onOpenChange={setIsAddLateRequestOpen}>
                        <DialogTrigger asChild>
                          <Button className="flex items-center gap-2">
                            <Plus className="h-4 w-4" /> Add Late Request
                          </Button>
                        </DialogTrigger>
                      </Dialog>
                      {userRole === "ADMIN" && (
                        <>
                          <Button variant="outline" size="sm">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                          </Button>
                        </>
                      )}
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add New Late Request</DialogTitle>
                          </DialogHeader>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Late Request</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          {userRole === "ADMIN" && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="late-employee" className="text-right">
                                Employee
                              </Label>
                              <Select
                                onValueChange={(value) => handleLateSelectChange('employeeId', value)}
                                value={lateRequestForm.employeeId}
                              >
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select Employee" />
                                </SelectTrigger>
                                <SelectContent>
                                  {employees.map(employee => (
                                    <SelectItem key={employee.id} value={employee.id.toString()}>
                                      {employee.firstName} {employee.lastName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="late-type" className="text-right">
                              Late Type
                            </Label>
                            <Select
                              onValueChange={(value) => handleLateSelectChange('lateType', value)}
                              value={lateRequestForm.lateType}
                            >
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select Late Type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Urgent">Urgent</SelectItem>
                                <SelectItem value="Work">Work</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="late-date" className="text-right">
                              Date Late
                            </Label>
                            <Input
                              id="late-date"
                              type="date"
                              className="col-span-3"
                              value={lateRequestForm.lateDate}
                              onChange={handleLateInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="late-estimatedTime" className="text-right">
                              Est. Time Come
                            </Label>
                            <Input
                              id="late-estimatedTime"
                              type="time"
                              className="col-span-3"
                              value={lateRequestForm.estimatedTime}
                              onChange={handleLateInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="late-reason" className="text-right">
                              Reason
                            </Label>
                            <Textarea
                              id="late-reason"
                              className="col-span-3"
                              placeholder="Reason for late"
                              value={lateRequestForm.reason}
                              onChange={handleLateInputChange}
                            />
                          </div>
                        </div>
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" onClick={() => setIsAddLateRequestOpen(false)}>Cancel</Button>
                          <Button onClick={handleSubmitLateRequest}>Submit Request</Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Employee</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Est. Time</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {lateRequests.map((late) => (
                          <TableRow key={late.id}>
                            <TableCell>LT{String(late.id).padStart(3, '0')}</TableCell>
                            <TableCell>{late.employee.firstName} {late.employee.lastName}</TableCell>
                            <TableCell>{late.lateType}</TableCell>
                            <TableCell>{new Date(late.lateDate).toLocaleDateString()}</TableCell>
                            <TableCell>{late.estimatedTime}</TableCell>
                            <TableCell>
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                late.status === 'approved'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                  : late.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              }`}>
                                {late.status.charAt(0).toUpperCase() + late.status.slice(1)}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleViewLate(late.id)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  {/* ADMIN dapat approve/reject semua late request yang masih pending */}
                                  {userRole === "ADMIN" && late.status === "pending" && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleApproveLate(late.id)}>
                                        <Check className="mr-2 h-4 w-4" />
                                        Approve
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleRejectLate(late.id)}>
                                        <X className="mr-2 h-4 w-4" />
                                        Reject
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleEditLate(late.id)}>
                                        <Pencil className="mr-2 h-4 w-4" />
                                        Edit Permintaan
                                      </DropdownMenuItem>
                                    </>
                                  )}

                                  {/* SUPERVISOR: Cek apakah late request adalah milik bawahannya */}
                                  {userRole === "SUPERVISOR" && (
                                    <>
                                      {/* Jika late request bukan milik supervisor sendiri (milik bawahan) dan masih pending */}
                                      {late.employee.firstName + ' ' + late.employee.lastName !== user?.name && late.status === "pending" && (
                                        <>
                                          <DropdownMenuItem onClick={() => handleApproveLate(late.id)}>
                                            <Check className="mr-2 h-4 w-4" />
                                            Approve
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => handleRejectLate(late.id)}>
                                            <X className="mr-2 h-4 w-4" />
                                            Reject
                                          </DropdownMenuItem>
                                        </>
                                      )}

                                      {/* Jika late request milik supervisor sendiri dan masih pending */}
                                      {late.employee.firstName + ' ' + late.employee.lastName === user?.name && late.status === "pending" && (
                                        <DropdownMenuItem onClick={() => handleEditLate(late.id)}>
                                          <Pencil className="mr-2 h-4 w-4" />
                                          Edit Permintaan Saya
                                        </DropdownMenuItem>
                                      )}
                                    </>
                                  )}

                                  {/* EMPLOYEE dapat mengedit late request miliknya sendiri yang masih pending */}
                                  {userRole === "EMPLOYEE" && late.status === "pending" && (
                                    <DropdownMenuItem onClick={() => handleEditLate(late.id)}>
                                      <Pencil className="mr-2 h-4 w-4" />
                                      Edit Permintaan
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    <div className="flex items-center justify-center py-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious href="#" />
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#" isActive>
                              1
                            </PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#">2</PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#">3</PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationNext href="#" />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="exit" className="space-y-4">
              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                  <div className="p-4 flex justify-between items-center border-b border-border">
                    <div>
                      <h2 className="text-lg font-semibold">Exit From School Requests</h2>
                      {userRole === 'EMPLOYEE' && (
                        <p className="text-xs text-muted-foreground">Showing your exit from school requests</p>
                      )}
                      {userRole === 'SUPERVISOR' && (
                        <p className="text-xs text-muted-foreground">Showing exit from school requests from your department</p>
                      )}
                      {userRole === 'ADMIN' && (
                        <p className="text-xs text-muted-foreground">Showing all exit from school requests</p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Dialog open={isAddExitRequestOpen} onOpenChange={setIsAddExitRequestOpen}>
                        <DialogTrigger asChild>
                          <Button className="flex items-center gap-2">
                            <Plus className="h-4 w-4" /> Add Exit Request
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add New Exit From School Request</DialogTitle>
                          </DialogHeader>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Exit From School Request</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          {userRole === "ADMIN" && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="exit-employee" className="text-right">
                                Employee
                              </Label>
                              <Select
                                onValueChange={(value) => handleExitSelectChange('employeeId', value)}
                                value={exitRequestForm.employeeId}
                              >
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select Employee" />
                                </SelectTrigger>
                                <SelectContent>
                                  {employees.map(employee => (
                                    <SelectItem key={employee.id} value={employee.id.toString()}>
                                      {employee.firstName} {employee.lastName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exit-exitType" className="text-right">
                              Exit Type
                            </Label>
                            <Select
                              onValueChange={(value) => handleExitSelectChange('exitType', value)}
                              value={exitRequestForm.exitType}
                            >
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select Exit Type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Work">Work</SelectItem>
                                <SelectItem value="Personal">Personal</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exit-exitTime" className="text-right">
                              Exit Time
                            </Label>
                            <Input
                              id="exit-exitTime"
                              type="time"
                              className="col-span-3"
                              value={exitRequestForm.exitTime}
                              onChange={handleExitInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exit-notComeback" className="text-right">
                              Not Comeback
                            </Label>
                            <div className="col-span-3 flex items-center">
                              <Checkbox
                                id="exit-notComeback"
                                checked={exitRequestForm.notComeback}
                                onCheckedChange={handleNotComebackChange}
                              />
                              <Label htmlFor="exit-notComeback" className="ml-2">
                                Will not return to school today
                              </Label>
                            </div>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exit-comebackTime" className="text-right">
                              Comeback Time
                            </Label>
                            <Input
                              id="exit-comebackTime"
                              type="time"
                              className="col-span-3"
                              value={exitRequestForm.comebackTime}
                              onChange={handleExitInputChange}
                              disabled={exitRequestForm.notComeback}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exit-reason" className="text-right">
                              Reason
                            </Label>
                            <Textarea
                              id="exit-reason"
                              className="col-span-3"
                              placeholder="Reason for exit from school"
                              value={exitRequestForm.reason}
                              onChange={handleExitInputChange}
                            />
                          </div>
                        </div>
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" onClick={() => setIsAddExitRequestOpen(false)}>Cancel</Button>
                          <Button onClick={handleSubmitExitRequest}>Submit Request</Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Employee</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Exit Time</TableHead>
                          <TableHead>Comeback Time</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {exitRequests && exitRequests.length > 0 ? (
                          exitRequests.map((exit) => (
                            <TableRow key={exit.id}>
                              <TableCell>EX{String(exit.id).padStart(3, '0')}</TableCell>
                              <TableCell>{exit.employee?.firstName} {exit.employee?.lastName}</TableCell>
                              <TableCell>{exit.exitType}</TableCell>
                              <TableCell>{exit.exitTime}</TableCell>
                              <TableCell>{exit.notComeback ? 'Not Returning' : exit.comebackTime}</TableCell>
                              <TableCell>
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  exit.status === 'approved'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                    : exit.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                                }`}>
                                  {exit.status.charAt(0).toUpperCase() + exit.status.slice(1)}
                                </span>
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                    >
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => handleViewExit(exit.id)}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      View Details
                                    </DropdownMenuItem>
                                    {/* ADMIN dapat approve/reject semua exit request yang masih pending */}
                                    {userRole === "ADMIN" && exit.status === "pending" && (
                                      <>
                                        <DropdownMenuItem onClick={() => handleApproveExit(exit.id)}>
                                          <Check className="mr-2 h-4 w-4" />
                                          Approve
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleRejectExit(exit.id)}>
                                          <X className="mr-2 h-4 w-4" />
                                          Reject
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleEditExit(exit.id)}>
                                          <Pencil className="mr-2 h-4 w-4" />
                                          Edit Permintaan
                                        </DropdownMenuItem>
                                      </>
                                    )}

                                    {/* SUPERVISOR: Cek apakah exit request adalah milik bawahannya */}
                                    {userRole === "SUPERVISOR" && (
                                      <>
                                        {/* Jika exit request bukan milik supervisor sendiri (milik bawahan) dan masih pending */}
                                        {exit.employee?.firstName + ' ' + exit.employee?.lastName !== user?.name && exit.status === "pending" && (
                                          <>
                                            <DropdownMenuItem onClick={() => handleApproveExit(exit.id)}>
                                              <Check className="mr-2 h-4 w-4" />
                                              Approve
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleRejectExit(exit.id)}>
                                              <X className="mr-2 h-4 w-4" />
                                              Reject
                                            </DropdownMenuItem>
                                          </>
                                        )}

                                        {/* Jika exit request milik supervisor sendiri dan masih pending */}
                                        {exit.employee?.firstName + ' ' + exit.employee?.lastName === user?.name && exit.status === "pending" && (
                                          <DropdownMenuItem onClick={() => handleEditExit(exit.id)}>
                                            <Pencil className="mr-2 h-4 w-4" />
                                            Edit Permintaan Saya
                                          </DropdownMenuItem>
                                        )}
                                      </>
                                    )}

                                    {/* EMPLOYEE dapat mengedit exit request miliknya sendiri yang masih pending */}
                                    {userRole === "EMPLOYEE" && exit.status === "pending" && (
                                      <DropdownMenuItem onClick={() => handleEditExit(exit.id)}>
                                        <Pencil className="mr-2 h-4 w-4" />
                                        Edit Permintaan
                                      </DropdownMenuItem>
                                    )}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-4">
                              No exit requests found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                    <div className="flex items-center justify-center py-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious href="#" />
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#" isActive>
                              1
                            </PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#">2</PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationLink href="#">3</PaginationLink>
                          </PaginationItem>
                          <PaginationItem>
                            <PaginationNext href="#" />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {userRole === "ADMIN" && (
              <TabsContent value="leave-types" className="space-y-4">
                <LeaveTypesSection />
              </TabsContent>
            )}

            <TabsContent value="calendar" className="space-y-4">
              <div className="flex flex-col lg:flex-row gap-6">
                <div className="bg-card text-card-foreground rounded-lg shadow-md p-6 w-fit">
                  <div className="mb-4">
                    <h2 className="text-lg font-semibold">Leave Calendar</h2>
                    {userRole === 'EMPLOYEE' && (
                      <p className="text-xs text-muted-foreground">Showing your leave schedule</p>
                    )}
                    {userRole === 'SUPERVISOR' && (
                      <p className="text-xs text-muted-foreground">Showing department leave schedule</p>
                    )}
                    {userRole === 'ADMIN' && (
                      <p className="text-xs text-muted-foreground">Showing all leave schedules</p>
                    )}
                  </div>
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    className="rounded-md border"
                    modifiers={modifiers}
                    modifiersStyles={modifiersStyles}
                  />
                </div>

                <div className="bg-card text-card-foreground rounded-lg shadow-md p-6 flex-1">
                  <h2 className="text-lg font-semibold mb-4">Leave Events</h2>
                  {/* Container dengan tinggi tetap untuk menampilkan 3 event */}
                  <div className="h-[300px] overflow-hidden relative">
                    {/* Wrapper dengan scrollbar yang akan aktif jika ada lebih dari 3 event */}
                    <div className="absolute inset-0 overflow-y-auto pr-2">
                      <div className="space-y-4">
                        {/* Menampilkan semua leave events dalam container dengan scrollbar */}
                        {leaveEvents.map((event) => (
                          <div
                            key={event.id}
                            className={`p-3 rounded-lg border ${
                              event.status === 'approved'
                                ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                                : event.status === 'pending'
                                  ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
                                  : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                            }`}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{event.employee}</p>
                                <p className="text-sm text-gray-600">{event.type}</p>
                              </div>
                              <span
                                className={`px-2 py-1 text-xs font-semibold rounded-full ${
                                  event.status === 'approved'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300'
                                    : event.status === 'pending'
                                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300'
                                      : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'
                                }`}
                              >
                                {event.status}
                              </span>
                            </div>
                            <div className="mt-2 text-sm text-gray-500">
                              {event.from.toLocaleDateString()} - {event.to.toLocaleDateString()}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {/* Dialog untuk melihat detail leave request */}
      <Dialog open={isViewLeaveRequestOpen} onOpenChange={setIsViewLeaveRequestOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Detail Leave Request</DialogTitle>
          </DialogHeader>
          {selectedLeaveRequest && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">ID</div>
                <div className="col-span-2">{selectedLeaveRequest.id}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Employee</div>
                <div className="col-span-2">{selectedLeaveRequest.employee}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Leave Type</div>
                <div className="col-span-2">{selectedLeaveRequest.type}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Start Date</div>
                <div className="col-span-2">{new Date(selectedLeaveRequest.from).toLocaleDateString('id-ID', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">End Date</div>
                <div className="col-span-2">{new Date(selectedLeaveRequest.to).toLocaleDateString('id-ID', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Status</div>
                <div className="col-span-2">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${selectedLeaveRequest.status === 'approved' ? 'bg-green-100 text-green-800' : selectedLeaveRequest.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                    {selectedLeaveRequest.status.charAt(0).toUpperCase() + selectedLeaveRequest.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Reason</div>
                <div className="col-span-2">
                  {selectedLeaveRequest.reason || '-'}
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Supporting Document</div>
                <div className="col-span-2">
                  {selectedLeaveRequest.attachmentUrl ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <a
                          href={selectedLeaveRequest.attachmentUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline flex items-center gap-1"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                          </svg>
                          View Document
                        </a>
                        <span className="text-xs text-muted-foreground">
                          {selectedLeaveRequest.attachmentUrl.split('/').pop()}
                        </span>
                      </div>

                      {selectedLeaveRequest.attachmentUrl.match(/\.(jpg|jpeg|png)$/i) && (
                        <div className="mt-2 border rounded-md overflow-hidden">
                          <img
                            src={selectedLeaveRequest.attachmentUrl}
                            alt="Supporting document"
                            className="max-w-full h-auto max-h-[200px] object-contain"
                          />
                        </div>
                      )}

                      {selectedLeaveRequest.attachmentUrl.match(/\.(pdf)$/i) && (
                        <div className="mt-2 border rounded-md p-2 bg-gray-50 flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                          </svg>
                          <span className="text-sm">PDF Document</span>
                        </div>
                      )}

                      {selectedLeaveRequest.attachmentUrl.match(/\.(doc|docx)$/i) && (
                        <div className="mt-2 border rounded-md p-2 bg-gray-50 flex items-center gap-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                          </svg>
                          <span className="text-sm">Word Document</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">No supporting document attached</span>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsViewLeaveRequestOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk mengedit leave request */}
      <Dialog open={isEditLeaveRequestOpen} onOpenChange={setIsEditLeaveRequestOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Leave Request</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-leave-type" className="text-right">
                Leave Type
              </Label>
              <Select
                onValueChange={(value) => handleEditSelectChange('leaveTypeId', value)}
                value={editLeaveForm.leaveTypeId}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select Leave Type" />
                </SelectTrigger>
                <SelectContent>
                  {leaveTypes.map(leaveType => (
                    <SelectItem key={leaveType.id} value={leaveType.id.toString()}>
                      {leaveType.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-startdate" className="text-right">
                Start Date
              </Label>
              <Input
                id="edit-startdate"
                type="date"
                className="col-span-3"
                value={editLeaveForm.startdate}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-enddate" className="text-right">
                End Date
              </Label>
              <Input
                id="edit-enddate"
                type="date"
                className="col-span-3"
                value={editLeaveForm.enddate}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-reason" className="text-right">
                Reason
              </Label>
              <Textarea
                id="edit-reason"
                className="col-span-3"
                placeholder="Reason for leave"
                value={editLeaveForm.reason}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-attachment" className="text-right">
                Supporting Document
              </Label>
              <div className="col-span-3 space-y-2">
                {editLeaveForm.attachmentUrl ? (
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center justify-between">
                      <a
                        href={editLeaveForm.attachmentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline flex items-center gap-1"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        {editLeaveForm.attachmentUrl.split('/').pop()}
                      </a>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={handleRemoveAttachment}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Click the button above to remove the current document and upload a new one.
                    </p>
                  </div>
                ) : (
                  <div>
                    <Input
                      id="edit-attachment"
                      type="file"
                      className="w-full"
                      onChange={handleEditFileChange}
                      accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      *Opsional. Upload surat dokter atau dokumen pendukung lainnya (PDF, JPG, PNG, DOC).
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditLeaveRequestOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmitEditLeave}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk melihat detail late request */}
      <Dialog open={isViewLateRequestOpen} onOpenChange={setIsViewLateRequestOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Detail Late Request</DialogTitle>
          </DialogHeader>
          {selectedLateRequest && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">ID</div>
                <div className="col-span-2">LT{String(selectedLateRequest.id).padStart(3, '0')}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Employee</div>
                <div className="col-span-2">{selectedLateRequest.employee.firstName} {selectedLateRequest.employee.lastName}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Late Type</div>
                <div className="col-span-2">{selectedLateRequest.lateType}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Date</div>
                <div className="col-span-2">{new Date(selectedLateRequest.lateDate).toLocaleDateString('id-ID', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Est. Time Come</div>
                <div className="col-span-2">{selectedLateRequest.estimatedTime}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Status</div>
                <div className="col-span-2">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${selectedLateRequest.status === 'approved' ? 'bg-green-100 text-green-800' : selectedLateRequest.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                    {selectedLateRequest.status.charAt(0).toUpperCase() + selectedLateRequest.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Reason</div>
                <div className="col-span-2">{selectedLateRequest.reason || '-'}</div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Dialog untuk edit late request */}
      <Dialog open={isEditLateRequestOpen} onOpenChange={setIsEditLateRequestOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Late Request</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-late-type" className="text-right">
                Late Type
              </Label>
              <Select
                onValueChange={(value) => handleEditLateSelectChange('lateType', value)}
                value={editLateForm.lateType}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select Late Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Urgent">Urgent</SelectItem>
                  <SelectItem value="Work">Work</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-late-date" className="text-right">
                Date Late
              </Label>
              <Input
                id="edit-late-date"
                type="date"
                className="col-span-3"
                value={editLateForm.lateDate}
                onChange={handleEditLateInputChange}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-late-estimatedTime" className="text-right">
                Est. Time Come
              </Label>
              <Input
                id="edit-late-estimatedTime"
                type="time"
                className="col-span-3"
                value={editLateForm.estimatedTime}
                onChange={handleEditLateInputChange}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-late-reason" className="text-right">
                Reason
              </Label>
              <Textarea
                id="edit-late-reason"
                className="col-span-3"
                placeholder="Reason for late"
                value={editLateForm.reason}
                onChange={handleEditLateInputChange}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditLateRequestOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmitEditLate}>Update Request</Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk melihat detail exit request */}
      <Dialog open={isViewExitRequestOpen} onOpenChange={setIsViewExitRequestOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Detail Exit From School Request</DialogTitle>
          </DialogHeader>
          {selectedExitRequest && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">ID</div>
                <div className="col-span-2">EX{String(selectedExitRequest.id).padStart(3, '0')}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Employee</div>
                <div className="col-span-2">{selectedExitRequest.employee.firstName} {selectedExitRequest.employee.lastName}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Exit Type</div>
                <div className="col-span-2">{selectedExitRequest.exitType}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Date</div>
                <div className="col-span-2">{new Date(selectedExitRequest.exitDate).toLocaleDateString('id-ID', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Exit Time</div>
                <div className="col-span-2">{selectedExitRequest.exitTime}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Comeback Time</div>
                <div className="col-span-2">{selectedExitRequest.notComeback ? 'Not Returning' : selectedExitRequest.comebackTime}</div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Status</div>
                <div className="col-span-2">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${selectedExitRequest.status === 'approved' ? 'bg-green-100 text-green-800' : selectedExitRequest.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                    {selectedExitRequest.status.charAt(0).toUpperCase() + selectedExitRequest.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="font-medium">Reason</div>
                <div className="col-span-2">{selectedExitRequest.reason || '-'}</div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Dialog untuk edit exit request */}
      <Dialog open={isEditExitRequestOpen} onOpenChange={setIsEditExitRequestOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Exit From School Request</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-exit-exitType" className="text-right">
                Exit Type
              </Label>
              <Select
                onValueChange={(value) => handleEditExitSelectChange('exitType', value)}
                value={editExitForm.exitType}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select Exit Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Work">Work</SelectItem>
                  <SelectItem value="Personal">Personal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-exit-exitTime" className="text-right">
                Exit Time
              </Label>
              <Input
                id="edit-exit-exitTime"
                type="time"
                className="col-span-3"
                value={editExitForm.exitTime}
                onChange={handleEditExitInputChange}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-exit-notComeback" className="text-right">
                Not Comeback
              </Label>
              <div className="col-span-3 flex items-center">
                <Checkbox
                  id="edit-exit-notComeback"
                  checked={editExitForm.notComeback}
                  onCheckedChange={handleEditNotComebackChange}
                />
                <Label htmlFor="edit-exit-notComeback" className="ml-2">
                  Will not return to school today
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-exit-comebackTime" className="text-right">
                Comeback Time
              </Label>
              <Input
                id="edit-exit-comebackTime"
                type="time"
                className="col-span-3"
                value={editExitForm.comebackTime}
                onChange={handleEditExitInputChange}
                disabled={editExitForm.notComeback}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-exit-reason" className="text-right">
                Reason
              </Label>
              <Textarea
                id="edit-exit-reason"
                className="col-span-3"
                placeholder="Reason for exit from school"
                value={editExitForm.reason}
                onChange={handleEditExitInputChange}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditExitRequestOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmitEditExit}>Update Request</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
