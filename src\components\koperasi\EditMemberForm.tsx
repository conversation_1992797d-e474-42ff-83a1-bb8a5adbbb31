import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { KoperasiMember } from "@/lib/types";
import { DialogFooter } from "@/components/ui/dialog";
import { format } from "date-fns";

interface EditMemberFormProps {
  member: KoperasiMember;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
}

export function EditMemberForm({ member, onSubmit, onCancel }: EditMemberFormProps) {
  // Format tanggal untuk input date
  const formatDateForInput = (date: Date) => {
    return format(new Date(date), "yyyy-MM-dd");
  };

  const [formData, setFormData] = useState({
    joinDate: formatDateForInput(member.join_date),
    oneTimeContribution: member.one_time_contribution || 0,
    monthlyContribution: member.monthly_contribution,
    optionalContribution: member.optional_contribution || 0,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validasi input
      const joinDate = new Date(formData.joinDate);
      if (isNaN(joinDate.getTime())) {
        alert("Please enter a valid date");
        setIsSubmitting(false);
        return;
      }

      const oneTimeContribution = parseFloat(String(formData.oneTimeContribution));
      if (isNaN(oneTimeContribution) || oneTimeContribution < 0) {
        alert("Please enter a valid positive number for One Time Contribution");
        setIsSubmitting(false);
        return;
      }

      const monthlyContribution = parseFloat(String(formData.monthlyContribution));
      if (isNaN(monthlyContribution) || monthlyContribution < 0) {
        alert("Please enter a valid positive number for Monthly Contribution");
        setIsSubmitting(false);
        return;
      }

      const optionalContribution = parseFloat(String(formData.optionalContribution));
      if (isNaN(optionalContribution) || optionalContribution < 0) {
        alert("Please enter a valid positive number for Optional Contribution");
        setIsSubmitting(false);
        return;
      }

      // Hitung total savings baru
      const totalSavings = oneTimeContribution + monthlyContribution + optionalContribution;

      // Kirim data ke parent component dengan format yang sesuai dengan API
      await onSubmit({
        joinDate, // Menambahkan join date
        monthlyContribution,
        oneTimeContribution,
        optionalContribution,
        status: member.status,
        notes: member.notes
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Failed to update member. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="joinDate" className="text-right">
            Join Date
          </Label>
          <Input
            id="joinDate"
            name="joinDate"
            type="date"
            value={formData.joinDate}
            onChange={handleChange}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="oneTimeContribution" className="text-right">
            One Time Contribution
          </Label>
          <Input
            id="oneTimeContribution"
            name="oneTimeContribution"
            type="number"
            min="0"
            step="0.01"
            value={formData.oneTimeContribution}
            onChange={handleChange}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="monthlyContribution" className="text-right">
            Monthly Contribution
          </Label>
          <Input
            id="monthlyContribution"
            name="monthlyContribution"
            type="number"
            min="0"
            step="0.01"
            value={formData.monthlyContribution}
            onChange={handleChange}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="optionalContribution" className="text-right">
            Optional Contribution
          </Label>
          <Input
            id="optionalContribution"
            name="optionalContribution"
            type="number"
            min="0"
            step="0.01"
            value={formData.optionalContribution}
            onChange={handleChange}
            className="col-span-3"
            required
          />
        </div>
      </div>
      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Save Changes"}
        </Button>
      </DialogFooter>
    </form>
  );
}
