import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';

// Helper function to parse period from string like "1/2023" (month/year)
function parsePeriod(periodStr: string): Date {
  if (!periodStr) {
    return new Date();
  }

  try {
    // Log the period string for debugging
    console.log('Parsing period string:', periodStr);

    // Handle different formats
    if (periodStr.includes('/')) {
      const parts = periodStr.split('/');
      if (parts.length === 2) {
        const month = parseInt(parts[0]) - 1; // 0-based month
        const year = parseInt(parts[1]);
        console.log(`Parsed month: ${month}, year: ${year}`);
        return new Date(year, month, 1);
      }
    } else if (periodStr.includes('-')) {
      const parts = periodStr.split('-');
      if (parts.length === 2) {
        const month = parseInt(parts[0]) - 1; // 0-based month
        const year = parseInt(parts[1]);
        console.log(`Parsed month: ${month}, year: ${year}`);
        return new Date(year, month, 1);
      }
    }

    // If we can't parse the string, log it and return current date
    console.error('Could not parse period string:', periodStr);
  } catch (error) {
    console.error('Error parsing period:', error);
  }

  return new Date();
}



export async function POST(request: Request) {
  console.log('Staff salary import API called');

  // Wrap everything in a try-catch to ensure we always return JSON
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    console.log('User cookie found:', !!userCookie);

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse form data
    console.log('Parsing form data...');
    const formData = await request.formData();
    const file = formData.get('file') as File;

    console.log('File received:', !!file, file ? `(${file.name}, ${file.size} bytes)` : '');

    if (!file) {
      console.error('No file provided in the request');
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Read file
    console.log('Reading file...');
    let workbook, sheet;
    try {
      const buffer = await file.arrayBuffer();
      console.log('File buffer size:', buffer.byteLength);
      workbook = XLSX.read(buffer, { type: 'array' });
      console.log('XLSX workbook loaded successfully');

      // Get first sheet
      const sheetName = workbook.SheetNames[0];
      console.log('Sheet names:', workbook.SheetNames);
      sheet = workbook.Sheets[sheetName];
      console.log('First sheet loaded:', sheetName);
    } catch (readError) {
      console.error('Error reading Excel file:', readError);
      return NextResponse.json(
        { error: 'Failed to read Excel file', details: readError instanceof Error ? readError.message : 'Unknown error' },
        { status: 400 }
      );
    }

    // Convert to JSON
    console.log('Converting sheet to JSON...');
    const data = XLSX.utils.sheet_to_json(sheet);

    if (!data || data.length === 0) {
      console.error('No data found in the Excel file');
      return NextResponse.json({ error: 'No data found in file' }, { status: 400 });
    }

    console.log(`Found ${data.length} rows of data in the Excel file`);

    // Process data
    let imported = 0;
    let errors = 0;
    let errorDetails = [];

    // Debug: Log the first row to see the structure
    if (data.length > 0) {
      console.log('First row of data:', JSON.stringify(data[0], null, 2));
    }

    console.log('Starting to process data rows...');

    for (const row of data as Record<string, any>[]) {
      try {
        // Add detailed logging for each row
        console.log('Processing row:', JSON.stringify(row, null, 2));

        // Map Excel columns to database fields
        const employeeIdValue = String(row['EmployeeId'] || '');

        // Check if employeeId exists in the database
        if (employeeIdValue) {
          // Try to find by employee_id (string format)
          const employeeByCode = await prisma.employee.findFirst({
            where: {
              employeeId: employeeIdValue,
              isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
            }
          });

          if (employeeByCode) {
            // Found by employee_id
            row._employeeId = employeeByCode.id; // Store the actual ID for later use
          } else {
            // Try to find by numeric ID
            const employeeId = parseInt(employeeIdValue);
            if (!isNaN(employeeId) && employeeId > 0) {
              const employeeById = await prisma.employee.findFirst({
                where: {
                  id: employeeId,
                  isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
                }
              });

              if (employeeById) {
                // Found by numeric ID
                row._employeeId = employeeById.id;
              } else {
                console.error(`Employee with ID ${employeeIdValue} not found or has been soft-deleted`);
                // Tetap lanjutkan, tapi dengan employeeId null
                row._employeeId = null;
                // Tambahkan warning ke log
                console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
              }
            } else {
              console.error(`Invalid employee ID format: ${employeeIdValue}`);
              // Tetap lanjutkan, tapi dengan employeeId null
              row._employeeId = null;
              // Tambahkan warning ke log
              console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
            }
          }
        } else {
          console.error('No employee ID provided');
          // Tetap lanjutkan, tapi dengan employeeId null
          row._employeeId = null;
          // Tambahkan warning ke log
          console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
        }

        // Helper function to safely parse numeric values
        const parseNumber = (value: any): number => {
          if (value === undefined || value === null || value === '') return 0;
          if (typeof value === 'number') return value;
          if (typeof value === 'string') {
            // Remove any non-numeric characters except decimal point
            const cleanedValue = value.replace(/[^0-9.]/g, '');
            return parseFloat(cleanedValue) || 0;
          }
          return 0;
        };

        // Helper function to safely parse boolean or numeric values for absensi fields
        const parseBoolean = (value: any): number => {
          // If it's already a boolean, convert to number (1 for true, 0 for false)
          if (typeof value === 'boolean') return value ? 1 : 0;

          // If it's a number, return the number
          if (typeof value === 'number') return value;

          // If it's a string that can be parsed as a number
          if (typeof value === 'string') {
            // Check if it's a string representation of a number
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
              return numValue; // Return the numeric value
            }

            // Otherwise check for boolean string values and convert to number
            return (value.toLowerCase() === 'ya' || value.toLowerCase() === 'yes' || value.toLowerCase() === 'true') ? 1 : 0;
          }

          // Default case
          return 0;
        };

        const salaryData = {
          employeeId: row._employeeId || null,
          nama: String(row['Nama'] || ''),
          gp: parseNumber(row['GP']),
          gol_kontrak: parseNumber(row['Gol Kontrak']),
          gol_tetap: parseNumber(row['Gol Tetap']),
          beban_kerja: parseNumber(row['Beban Kerja']),
          insentif: parseNumber(row['Insentif']),
          tunj_keahlian: parseNumber(row['Tunjangan Keahlian']),
          tunj_jab: parseNumber(row['Tunjangan Jabatan']),
          tot_gross: parseNumber(row['Total Gross']),
          bpjstku_yay: parseNumber(row['BPJS TK Yayasan']),
          bpjskes_yay: parseNumber(row['BPJS Kesehatan Yayasan']),
          pph_yay: parseNumber(row['PPh Yayasan']),
          tot_gross_yay: parseNumber(row['Total Gross Yayasan']),
          awol: parseBoolean(row['AWOL']),
          jlh_awol: parseInt(String(row['Jumlah AWOL'] || '0')),
          freq: parseBoolean(row['Freq']),
          minute: parseBoolean(row['Minute']),
          jlh_freq: parseInt(String(row['Jumlah Freq'] || '0')),
          jlh_minute: parseInt(String(row['Jumlah Minute'] || '0')),
          pot_abs: parseNumber(row['Potongan Absensi']),
          gaji_stlh_pot_abs: parseNumber(row['Gaji Setelah Potongan Absensi']),
          bpjstku: parseNumber(row['BPJS TK']),
          bpjskes: parseNumber(row['BPJS Kesehatan']),
          nett_stlh_bpjs: parseNumber(row['Nett Setelah BPJS']),
          pph: parseNumber(row['PPh']),
          pinj_lain: parseNumber(row['Pinjaman Lain']),
          iuran_wajib: parseNumber(row['Iuran Wajib']),
          pinj_kop: parseNumber(row['Pinjaman Koperasi']),
          piutang: parseNumber(row['Piutang']),
          pot_bank: parseNumber(row['Potongan Bank']),
          tot_pot: parseNumber(row['Total Potongan']),
          gaji_netto: parseNumber(row['Gaji Netto']),
          period: parsePeriod(String(row['Periode'] || '')),
        };

        // Debug: Log the processed data
        console.log('Processed salary data:', JSON.stringify(salaryData, null, 2));

        // Validate data types before sending to database
        try {
          // Make sure absensi fields are numbers
          salaryData.awol = Number(salaryData.awol || 0);
          salaryData.freq = Number(salaryData.freq || 0);
          salaryData.minute = Number(salaryData.minute || 0);

          // Ensure minute value is within valid range for INT
          if (salaryData.minute > **********) { // Max value for INT
            salaryData.minute = **********;
          }

          console.log('Validated salary data:', JSON.stringify(salaryData, null, 2));
        } catch (validationError) {
          console.error('Error validating data types:', validationError);
          throw new Error(`Data validation error: ${validationError instanceof Error ? validationError.message : 'Unknown validation error'}`);
        }

        // Create record
        try {
          await prisma.salaryStaff.create({
            data: salaryData,
          });
        } catch (dbError) {
          console.error('Database error creating record:', dbError);
          // Add more context to the error
          if (dbError instanceof Error) {
            throw new Error(`Database error: ${dbError.message}`);
          } else {
            throw new Error('Unknown database error');
          }
        }

        imported++;
      } catch (error) {
        console.error('Error importing row:', error);
        console.error('Error details:', error instanceof Error ? error.stack : 'Unknown error');
        console.error('Row data that caused the error:', JSON.stringify(row, null, 2));
        errors++;
        errorDetails.push({
          error: error instanceof Error ? error.message : 'Unknown error',
          row: row
        });
      }
    }

    console.log(`Import completed: ${imported} imported, ${errors} errors`);
    return NextResponse.json({ imported, errors, errorDetails: errorDetails.slice(0, 5) });  // Send first 5 errors for debugging
  } catch (error) {
    console.error('Failed to import staff salary data:', error);
    console.error('Error details:', error instanceof Error ? error.stack : 'Unknown error');

    // Create a more detailed error message
    let errorMessage = 'Failed to import staff salary data';
    let errorDetails = 'Unknown error';

    if (error instanceof Error) {
      errorDetails = error.message;
      if (error.cause) {
        errorDetails += ' - Cause: ' + JSON.stringify(error.cause);
      }
      if (error.stack) {
        console.error('Error stack:', error.stack);
      }
    }

    // Always return JSON, never HTML
    return new NextResponse(
      JSON.stringify({
        error: errorMessage,
        details: errorDetails,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
