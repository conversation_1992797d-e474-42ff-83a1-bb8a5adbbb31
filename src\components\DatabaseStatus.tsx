"use client";

import { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle2, XCircle } from 'lucide-react';

export function DatabaseStatus() {
  const [status, setStatus] = useState<'connected' | 'disconnected' | 'error'>('connected');
  const [message, setMessage] = useState<string>('');
  const [show, setShow] = useState(true);

  const checkDatabaseConnection = async () => {
    try {
      const response = await fetch('/api/health/db', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('connected');
        setMessage(data.message || 'Database Connected');
      } else {
        setStatus('error');
        setMessage(data.message || 'Database connection issue');
      }
    } catch (error) {
      setStatus('disconnected');
      setMessage('Cannot connect to database');
      console.error('Database connection check failed:', error);
    }
  };

  useEffect(() => {
    checkDatabaseConnection();
    // Check connection every 30 seconds
    const interval = setInterval(checkDatabaseConnection, 30000);
    return () => clearInterval(interval);
  }, []);

  if (!show) return null;

  const statusStyles = {
    connected: 'bg-green-500/10 text-green-500 border-green-500/20',
    disconnected: 'bg-red-500/10 text-red-500 border-red-500/20',
    error: 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20'
  };

  const StatusIcon = {
    connected: CheckCircle2,
    disconnected: XCircle,
    error: AlertCircle
  }[status];

  return (
    <div className={`fixed bottom-4 right-4 flex items-center gap-2 px-3 py-2 rounded-md border ${statusStyles[status]}`}>
      <StatusIcon className="h-4 w-4" />
      <span className="text-sm font-medium">{message}</span>
    </div>
  );
}

