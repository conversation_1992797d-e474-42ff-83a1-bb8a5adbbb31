/**
 * API Route: DELETE /api/koperasi/savings/[id]/delete
 *
 * Deskripsi: Menghapus simpanan koperasi
 * Penggunaan: Tombol hapus di halaman daftar simpanan koperasi
 *
 * Path Parameters:
 * - id: ID simpanan koperasi (number)
 *
 * Response:
 * - 200: Simpanan koperasi berhasil dihapus
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Simpanan koperasi tidak ditemukan
 * - 409: Simpanan koperasi tidak dapat dihapus (akan menyebabkan saldo negatif)
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { db } from '@/lib/db';
import { NextRequest } from 'next/server';

// Use the shared Prisma client instance
const prisma = db;

export async function DELETE(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Verify admin or operator_kop access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from params
    const id = params.id;

    // Check if saving exists
    const saving = await prisma.koperasiSaving.findUnique({
      where: { id: parseInt(id) },
      include: {
        member: true
      }
    });

    if (!saving) {
      return NextResponse.json({ error: 'Koperasi saving not found' }, { status: 404 });
    }

    // Hanya hapus transaksi tanpa mempengaruhi data anggota
    // Transaksi yang sudah diproses tidak seharusnya mempengaruhi data anggota ketika dihapus
    await prisma.koperasiSaving.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Koperasi saving deleted successfully' });
  } catch (error) {
    console.error('Error deleting koperasi saving:', error);
    return NextResponse.json(
      { error: 'Failed to delete koperasi saving' },
      { status: 500 }
    );
  } finally {
    // Don't disconnect the shared Prisma client
    // The shared client is managed by the db.ts module
  }
}
