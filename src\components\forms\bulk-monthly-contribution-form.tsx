import { useState } from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";

interface BulkMonthlyContributionFormProps {
  onSubmit: (data: BulkMonthlyContributionFormData) => void;
  onCancel: () => void;
}

interface BulkMonthlyContributionFormData {
  date: string;
  notes?: string;
}

export function BulkMonthlyContributionForm({ onSubmit, onCancel }: BulkMonthlyContributionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<BulkMonthlyContributionFormData>({
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      notes: ""
    }
  });

  const handleSubmit = form.handleSubmit((data) => {
    setIsSubmitting(true);
    onSubmit(data);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tanggal Transaksi</FormLabel>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
              <FormDescription>
                Tanggal transaksi untuk semua iuran wajib bulanan
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Catatan</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Catatan untuk transaksi massal (opsional)"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Jika dikosongkan, catatan akan otomatis dibuat: "Iuran Wajib Bulan [Nama Bulan] [Tahun]"
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel} type="button">
            Batal
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Proses Transaksi Massal
          </Button>
        </div>
      </form>
    </Form>
  );
}
