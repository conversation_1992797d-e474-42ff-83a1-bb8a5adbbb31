/**
 * API Route: PUT /api/leave-management/[id]/edit
 *
 * Deskripsi: Mengedit permintaan leave
 * Penggunaan: Form edit permintaan leave
 *
 * Path Parameters:
 * - id: ID permintaan leave (number)
 *
 * Body:
 * - leaveTypeId: ID tipe leave (number, opsional)
 * - startDate: <PERSON><PERSON> mulai (string, format: YYYY-MM-DD, opsional)
 * - endDate: <PERSON><PERSON> selesai (string, format: YYYY-MM-DD, opsional)
 * - reason: <PERSON>asan leave (string, opsional)
 *
 * Response:
 * - 200: Permintaan leave berhasil diperbarui
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Permintaan leave tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function PUT(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    // Extract ID from URL path
    const pathParts = request.url.split('/');
    const id = pathParts[pathParts.length - 2] || '';
    const data = await request.json();
    const { leaveTypeId, startDate, endDate, reason, attachmentUrl } = data;

    // Check if leave request exists
    const leaveRequest = await prisma.leaveRequest.findUnique({
      where: { id: parseInt(id) },
      include: {
        employee: true
      }
    });

    if (!leaveRequest) {
      return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
    }

    // Check user role and permissions
    const isOwner = leaveRequest.employeeId === parseInt(userData.id);
    const isAdmin = userData.role === 'ADMIN';

    // EMPLOYEE dan SUPERVISOR hanya dapat mengedit leave request miliknya sendiri
    // ADMIN dapat mengedit semua leave request
    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Anda hanya dapat mengedit permintaan leave Anda sendiri' },
        { status: 403 }
      );
    }

    // Check if leave request is already approved or rejected
    if (leaveRequest.status !== 'pending') {
      return NextResponse.json(
        { error: 'Tidak dapat mengedit permintaan leave yang sudah disetujui atau ditolak' },
        { status: 400 }
      );
    }

    // Validate dates if provided
    let start = leaveRequest.startDate;
    let end = leaveRequest.endDate;

    if (startDate) {
      start = new Date(startDate);
      if (isNaN(start.getTime())) {
        return NextResponse.json(
          { error: 'Invalid start date format' },
          { status: 400 }
        );
      }
    }

    if (endDate) {
      end = new Date(endDate);
      if (isNaN(end.getTime())) {
        return NextResponse.json(
          { error: 'Invalid end date format' },
          { status: 400 }
        );
      }
    }

    if (start > end) {
      return NextResponse.json(
        { error: 'Start date cannot be after end date' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: {
      updatedAt: Date;
      leaveTypeId?: number;
      startDate?: Date;
      endDate?: Date;
      reason?: string | null;
      attachmentUrl?: string | null;
    } = {
      updatedAt: new Date()
    };

    if (leaveTypeId) updateData.leaveTypeId = parseInt(leaveTypeId);
    if (startDate) updateData.startDate = start;
    if (endDate) updateData.endDate = end;
    if (reason !== undefined) updateData.reason = reason || null;
    if (attachmentUrl !== undefined) updateData.attachmentUrl = attachmentUrl || null;

    // Update leave request
    const updatedLeaveRequest = await prisma.leaveRequest.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        employee: true,
        leaveType: true
      }
    });

    return NextResponse.json(updatedLeaveRequest);
  } catch (error) {
    console.error('Error editing leave request:', error);
    return NextResponse.json(
      { error: 'Failed to edit leave request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
