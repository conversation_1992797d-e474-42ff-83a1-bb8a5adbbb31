import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { db } from '@/lib/db';
import { hash } from 'bcryptjs';

// Use the shared Prisma client instance
const prisma = db;

// Fungsi untuk membuat "dummy employee" untuk anggota eksternal
async function createDummyEmployee(firstName: string, lastName: string, email: string) {
  try {
    // Generate unique employeeId
    const employeeCount = await prisma.employee.count();
    const employeeId = `EXT${(employeeCount + 1).toString().padStart(3, '0')}`;

    // Create dummy employee
    const employee = await prisma.employee.create({
      data: {
        employeeId: employeeId,
        firstName: firstName,
        lastName: lastName || '',
        email: email || `external_${employeeId}@example.com`,
        hireDate: new Date(),
        departmentId: 1, // Gunakan department default
        positionId: 1, // Gunakan position default
        status: 'Kontrak' // Menggunakan status Kontrak untuk anggota eksternal
      }
    });

    return employee;
  } catch (error) {
    console.error('Error creating dummy employee:', error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    // Verify admin or operator_kop access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    console.log('POST /api/koperasi/members/external - Received data:', data);

    const {
      firstName,
      lastName,
      email,
      phone,
      address,
      monthlyContribution,
      oneTimeContribution,
      optionalContribution,
      joinDate,
      notes
    } = data;

    // Validate required fields
    if (!firstName || !lastName || !monthlyContribution) {
      return NextResponse.json(
        { error: 'First name, last name, and monthly contribution are required' },
        { status: 400 }
      );
    }

    // Validate email if provided
    if (email) {
      // Check if email is already used by another employee
      const existingEmployee = await prisma.employee.findUnique({
        where: { email: email }
      });

      if (existingEmployee) {
        return NextResponse.json(
          { error: 'Email is already used by another employee or member' },
          { status: 400 }
        );
      }
    }

    // Create dummy employee and user account in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create dummy employee for external member
      const dummyEmployee = await createDummyEmployee(firstName, lastName, email);

      // Generate member code
      const memberCount = await tx.koperasiMember.count();
      const memberCode = `E${(memberCount + 1).toString().padStart(3, '0')}`;

      // Konversi nilai kontribusi ke Decimal tanpa mengubah nilai asli
      const monthlyContributionValue = parseFloat(monthlyContribution) || 0;
      const oneTimeContributionValue = parseFloat(oneTimeContribution) || 0;
      const optionalContributionValue = parseFloat(optionalContribution) || 0;

      // Hitung total savings awal
      const totalSavings = monthlyContributionValue + oneTimeContributionValue + optionalContributionValue;

      // Create new koperasi member
      const member = await tx.koperasiMember.create({
        data: {
          employeeId: dummyEmployee.id,
          joinDate: joinDate ? new Date(joinDate) : new Date(),
          monthlyContribution: monthlyContributionValue,
          oneTimeContribution: oneTimeContributionValue > 0 ? oneTimeContributionValue : null,
          optionalContribution: optionalContributionValue > 0 ? optionalContributionValue : null,
          totalSavings: totalSavings,
          status: 'active',
          notes: `[EXTERNAL MEMBER] ${firstName} ${lastName}. Contact: ${phone || 'N/A'}. Address: ${address || 'N/A'}. ${notes || ''}`
        },
        include: {
          employee: true
        }
      });

      // Create user account for the external member
      // Check if user already exists for this employee
      const existingUser = await tx.user.findUnique({
        where: { employeeId: dummyEmployee.id }
      });

      if (!existingUser) {
        // Hash the default password
        const hashedPassword = await hash('defaultpass123', 10);

        // Create user account
        await tx.user.create({
          data: {
            username: dummyEmployee.employeeId, // Use external employee ID as username
            password: hashedPassword,
            role: 'EMPLOYEE', // Standard employee role
            email: dummyEmployee.email,
            employeeId: dummyEmployee.id,
            isActive: true
          }
        });

        console.log(`Created user account for external member ${dummyEmployee.employeeId} with default password 'defaultpass123'`);
      } else {
        console.log(`User account already exists for external member ID ${dummyEmployee.id}`);
      }

      return { member, dummyEmployee, memberCode };
    });

    const { member, dummyEmployee, memberCode } = result;

    // Get contribution values for savings transactions tanpa mengubah nilai asli
    const monthlyContributionVal = parseFloat(monthlyContribution) || 0;
    const oneTimeContributionVal = parseFloat(oneTimeContribution) || 0;
    const optionalContributionVal = parseFloat(optionalContribution) || 0;

    // Jika ada kontribusi awal, buat transaksi savings
    if (oneTimeContributionVal > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: member.id,
          amount: oneTimeContributionVal,
          type: 'deposit',
          contributionType: 'one_time',
          date: new Date(),
          notes: 'Initial one-time contribution'
        }
      });
    }

    if (monthlyContributionVal > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: member.id,
          amount: monthlyContributionVal,
          type: 'deposit',
          contributionType: 'monthly',
          date: new Date(),
          notes: 'Initial monthly contribution'
        }
      });
    }

    if (optionalContributionVal > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: member.id,
          amount: optionalContributionVal,
          type: 'deposit',
          contributionType: 'optional',
          date: new Date(),
          notes: 'Initial optional contribution'
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'External member registered successfully',
      member: {
        id: member.id,
        member_code: memberCode,
        member_type: 'external',
        employee_id: dummyEmployee.id,
        first_name: firstName,
        last_name: lastName,
        email: email,
        phone: phone,
        address: address,
        join_date: member.joinDate,
        monthly_contribution: Number(member.monthlyContribution),
        one_time_contribution: member.oneTimeContribution ? Number(member.oneTimeContribution) : 0,
        optional_contribution: member.optionalContribution ? Number(member.optionalContribution) : 0,
        total_savings: Number(member.totalSavings),
        status: member.status,
        notes: member.notes
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Failed to create external koperasi member:', error);

    // Provide more detailed error information
    let errorMessage = 'Failed to create external koperasi member';
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = { stack: error.stack };
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  } finally {
    // Don't disconnect the shared Prisma client
    // The shared client is managed by the db.ts module
  }
}
