"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Navbar from "@/components/layout/Navbar";
import HeroSection from "@/components/sections/HeroSection";
import FeaturesSection from "@/components/sections/FeaturesSection";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/auth";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/lib/auth";

// Create a component that uses the auth context after it's initialized
function HomeContent() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={user?.role} />
      <main>
        <HeroSection />
        <FeaturesSection />
        <section className="py-16 bg-muted">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6 text-foreground">
              Ready to Get Started?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Employee Management System
              to streamline their HR operations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {user ? (
                <Button
                  size="lg"
                  className="px-8 py-6 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-transform hover:scale-105"
                  onClick={() => {
                    let redirectPath;

                    switch (user.role) {
                      case 'ADMIN':
                        redirectPath = '/dashboard';
                        break;
                      case 'SUPERVISOR':
                        redirectPath = '/employees';
                        break;
                      case 'HEAD':
                        redirectPath = '/employees';
                        break;
                      case 'OPERATOR_KOP':
                        redirectPath = '/koperasi';
                        break;
                      case 'EMPLOYEE':
                      default:
                        redirectPath = '/leave-management';
                        break;
                    }

                    // Removed sensitive log for security
                    router.push(redirectPath);
                  }}
                >
                  Go to Dashboard
                </Button>
              ) : (
                <Button
                  size="lg"
                  className="px-8 py-6 text-lg bg-blue-600 text-white hover:bg-blue-700 transition-transform hover:scale-105"
                  onClick={() => router.push("/login")}
                >
                  Login
                </Button>
              )}
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}

export default function Home() {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
      <AuthProvider>
        <HomeContent />
      </AuthProvider>
    </ThemeProvider>
  );
}
