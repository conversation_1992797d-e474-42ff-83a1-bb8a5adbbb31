import { useState, useEffect } from "react";
import { SalaryHonor } from "@/lib/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, MoreHorizontal, Eye, Pencil, Trash2, Download, Search, FileText } from "lucide-react";
import { Input } from "@/components/ui/input";
import { ImportSalaryDialog } from "./ImportSalaryDialog";
import { HonorSalarySlipDialog } from "./HonorSalarySlipDialog";
import { toast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { HonorSalaryForm } from "@/components/forms/honor-salary-form";
import { format } from "date-fns";
import { id } from "date-fns/locale";

interface HonorTabProps {
  data: SalaryHonor[];
  loading: boolean;
  userRole?: string;
  onSubmit: (data: any) => Promise<void>;
  onRefresh: () => Promise<void>;
}

export function HonorTab({ data, loading, userRole, onSubmit, onRefresh }: HonorTabProps) {
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isSlipOpen, setIsSlipOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<SalaryHonor | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<SalaryHonor[]>([]);
  const itemsPerPage = 18;

  // Filter data based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredData(data);
    } else {
      const filtered = data.filter(item =>
        item.nama.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredData(filtered);
    }
    setCurrentPage(1); // Reset to first page when search changes
  }, [searchTerm, data]);

  // Format currency
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return "-";
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (date: Date) => {
    return format(new Date(date), "MMMM yyyy", { locale: id });
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );



  // Handle edit
  const handleEdit = (item: SalaryHonor) => {
    setSelectedItem(item);
    setIsEditOpen(true);
  };

  // Handle view
  const handleView = (item: SalaryHonor) => {
    setSelectedItem(item);
    setIsViewOpen(true);
  };

  // Handle slip gaji
  const handleSlip = (item: SalaryHonor) => {
    setSelectedItem(item);
    setIsSlipOpen(true);
  };

  // Handle delete
  const handleDelete = (item: SalaryHonor) => {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  };

  // Handle delete confirmation
  const confirmDelete = async () => {
    if (!selectedItem) return;

    try {
      const response = await fetch(`/api/salary/honor/${selectedItem.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete honor salary data');
      }

      setIsDeleteOpen(false);
      toast({
        title: "Success",
        description: "Honor salary data deleted successfully",
      });
      onRefresh();
    } catch (error) {
      console.error('Error deleting honor salary:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to delete honor salary data',
        variant: "destructive",
      });
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const response = await fetch('/api/salary/honor/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: data,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to export honor salary data');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'honor_salary_export.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting honor salary:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Honor Salary</h2>
        <div className="flex gap-2">
          {userRole === "ADMIN" && (
            <>
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button onClick={() => setIsImportOpen(true)}>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Search bar - only for ADMIN */}
      {userRole === "ADMIN" && (
        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Cari berdasarkan nama..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {searchTerm && (
            <Button variant="ghost" onClick={() => setSearchTerm("")}>Clear</Button>
          )}
        </div>
      )}

      <div className="rounded-md border overflow-auto">
        <div className="min-w-max overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="whitespace-nowrap">Nama</TableHead>
                <TableHead className="text-right whitespace-nowrap">Actions</TableHead>
                <TableHead className="whitespace-nowrap">Periode</TableHead>
                <TableHead className="whitespace-nowrap">Gaji Netto</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center">Loading...</TableCell>
                </TableRow>
              ) : paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center">No data found</TableCell>
                </TableRow>
              ) : (
                paginatedData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.nama}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {userRole === "ADMIN" && (
                          <DropdownMenuItem onClick={() => handleView(item)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleSlip(item)}>
                          <FileText className="mr-2 h-4 w-4" />
                          Slip Gaji
                        </DropdownMenuItem>
                        {userRole === "ADMIN" && (
                          <>
                            <DropdownMenuItem onClick={() => handleEdit(item)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(item)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                  <TableCell>{formatDate(item.period)}</TableCell>
                  <TableCell>{formatCurrency(item.gaji_netto)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        </div>
      </div>

      {totalPages > 1 && (
        <Pagination className="mt-4">
          <PaginationContent>
            <PaginationItem>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
            </PaginationItem>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <PaginationItem key={page}>
                <Button
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              </PaginationItem>
            ))}
            <PaginationItem>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}

      {/* Import Dialog */}
      <ImportSalaryDialog
        open={isImportOpen}
        onOpenChange={setIsImportOpen}
        onImportSuccess={onRefresh}
        type="honor"
      />

      {/* Edit Dialog */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Honor Salary</DialogTitle>
            <DialogDescription>
              Edit honor salary data
            </DialogDescription>
          </DialogHeader>
          {selectedItem && (
            <HonorSalaryForm
              initialData={selectedItem}
              onSubmit={(data) => {
                onSubmit({ ...data, id: selectedItem.id });
                setIsEditOpen(false);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Honor Salary Details</DialogTitle>
            <DialogDescription>
              View honor salary details
            </DialogDescription>
          </DialogHeader>
          {selectedItem && (
            <div className="grid grid-cols-2 gap-4 max-h-[70vh] overflow-y-auto">
              <div className="space-y-2">
                <div>
                  <h3 className="font-medium">Nama</h3>
                  <p>{selectedItem.nama}</p>
                </div>
                <div>
                  <h3 className="font-medium">GP1</h3>
                  <p>{formatCurrency(selectedItem.gp1)}</p>
                </div>
                <div>
                  <h3 className="font-medium">PPh Dibayar Sekolah</h3>
                  <p>{formatCurrency(selectedItem.pph_dibayar_sklh)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Total Tunjangan Tetap</h3>
                  <p>{formatCurrency(selectedItem.tot_tun_tep)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Honor</h3>
                  <p>{formatCurrency(selectedItem.hnr)}</p>
                </div>
                <div>
                  <h3 className="font-medium">JP</h3>
                  <p>{formatCurrency(selectedItem.jp)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Total Honor</h3>
                  <p>{formatCurrency(selectedItem.tot_hnr)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Jumlah Hadir</h3>
                  <p>{selectedItem.jlh_hdr}</p>
                </div>
                <div>
                  <h3 className="font-medium">Honor Hadir</h3>
                  <p>{formatCurrency(selectedItem.hnr_hdr)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Total Honor Hadir</h3>
                  <p>{formatCurrency(selectedItem.tot_hnr_hdr)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Sebelum PPh</h3>
                  <p>{formatCurrency(selectedItem.sblm_pph)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Jumlah XC</h3>
                  <p>{selectedItem.jlhxc}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div>
                  <h3 className="font-medium">XC</h3>
                  <p>{formatCurrency(selectedItem.xc)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Tambahan</h3>
                  <p>{formatCurrency(selectedItem.tmbhn)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Gaji Bruto</h3>
                  <p>{formatCurrency(selectedItem.gaji_bruto)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Bruto Setelah Potongan</h3>
                  <p>{formatCurrency(selectedItem.bruto_stlh_pot)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Nett Setelah Jamsostek</h3>
                  <p>{formatCurrency(selectedItem.nettstlhjamsostek)}</p>
                </div>
                <div>
                  <h3 className="font-medium">PPh21</h3>
                  <p>{formatCurrency(selectedItem.pph21)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Pinjaman Lainnya</h3>
                  <p>{formatCurrency(selectedItem.pinj_lainnya)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Iuran Wajib</h3>
                  <p>{formatCurrency(selectedItem.iuran_wajib)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Pinjaman Koperasi</h3>
                  <p>{formatCurrency(selectedItem.pinj_kop)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Piutang</h3>
                  <p>{formatCurrency(selectedItem.piutang)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Potongan Bank</h3>
                  <p>{formatCurrency(selectedItem.pot_bank)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Gaji Netto</h3>
                  <p>{formatCurrency(selectedItem.gaji_netto)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Periode</h3>
                  <p>{formatDate(selectedItem.period)}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this honor salary data? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Slip Gaji Dialog */}
      <HonorSalarySlipDialog
        open={isSlipOpen}
        onOpenChange={setIsSlipOpen}
        data={selectedItem}
      />
    </div>
  );
}
