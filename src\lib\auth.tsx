"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { logger } from '@/lib/logger';

interface User {
  id: string;
  email: string;
  role: 'ADMIN' | 'SUPERVISOR' | 'EMPLOYEE' | 'OPERATOR_KOP' | 'HEAD';
  name: string;
  employeeId?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<{ success: boolean; role?: string }>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check both localStorage and cookies
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const userData = JSON.parse(userStr);
          // Removed sensitive logs for security
          setUser(userData);
          // Set cookie if it doesn't exist
          document.cookie = `user=${userStr}; path=/`;
        } else {
          console.log('Auth context: no user found in localStorage');
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string): Promise<{ success: boolean; role?: string }> => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Important for cookies
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (data.success) {
        // Store in both localStorage and cookies
        const userStr = JSON.stringify(data.user);
        localStorage.setItem('user', userStr);
        document.cookie = `user=${userStr}; path=/`;
        setUser(data.user);
        // Removed sensitive logs for security
        return { success: true, role: data.user.role };
      }

      return { success: false };
    } catch (error) {
      console.error('Login failed:', error);
      return { success: false };
    }
  };

  const logout = async () => {
    try {
      localStorage.removeItem('user');
      document.cookie = 'user=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      setUser(null);
      logger.debug('User logged out successfully');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

