export interface User {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  role: 'ADMIN' | 'SUPERVISOR' | 'EMPLOYEE';
  last_login?: Date;
  is_active: boolean;
  employee_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  hire_date: Date;
  department_id: number;
  position_id: number;
  manager_id?: number;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  birth_date?: Date;
  gender?: string;
  marital_status?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  status: 'active' | 'inactive';
  created_at: Date;
  updated_at: Date;
}

export interface KoperasiMember {
  id: number;
  employee_id?: number;
  employeeId?: number; // Alias for employee_id used by Prisma
  join_date: Date;
  joinDate?: Date; // Alias for join_date used by Prisma
  monthly_contribution: number;
  monthlyContribution?: number; // Alias for monthly_contribution used by Prisma
  one_time_contribution?: number;
  oneTimeContribution?: number; // Alias for one_time_contribution used by Prisma
  optional_contribution?: number;
  optionalContribution?: number; // Alias for optional_contribution used by Prisma
  total_savings: number;
  totalSavings?: number; // Alias for total_savings used by Prisma
  status: 'active' | 'inactive';
  notes?: string;
  created_at: Date;
  createdAt?: Date; // Alias for created_at used by Prisma
  updated_at: Date;
  updatedAt?: Date; // Alias for updated_at used by Prisma

  // Virtual fields (not in database)
  memberType?: 'employee' | 'external';
  memberCode?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;

  // Relations
  employee?: Employee;
}

export interface KoperasiLoan {
  id: number;
  member_id: number;
  amount: number;
  purpose: string;
  application_date: Date;
  approval_date?: Date;
  start_date?: Date;
  end_date?: Date;
  interest_rate: number;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  approved_by?: number;
  created_at: Date;
  updated_at: Date;
}

export interface KoperasiSaving {
  id: number;
  member_id: number;
  member_name: string;
  email: string;
  amount: number;
  type: 'deposit' | 'withdrawal';
  contributionType?: 'one_time' | 'monthly' | 'optional';
  date: Date;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export interface KoperasiTransaction {
  id: number;
  member_name: string;
  amount: number;
  type: 'deposit' | 'withdrawal';
  contributionType?: 'one_time' | 'monthly' | 'optional';
  date: Date;
  created_at: Date;
}

export interface KoperasiOverview {
  // Member statistics
  totalMembers: number;
  totalActiveMembers: number;
  totalInactiveMembers: number;
  memberActivePercentage: number;

  // Savings statistics
  totalSavings: number;
  totalOneTimeContributions: number;
  totalMonthlyContributions: number;
  totalOptionalContributions: number;
  averageSavingsPerMember: number;

  // Loan statistics
  activeLoanCount: number;
  completedLoanCount: number;
  totalLoanAmount: number;
  completedLoanAmount: number;
  totalLoanCount: number;

  // Recent transactions
  recentTransactions: KoperasiTransaction[];

  // Legacy fields for backward compatibility
  monthlyContributions: number;
  membershipGrowth: number;
  loanApprovalRate: number;
}

export interface SalaryStaff {
  id: number;
  nama: string;
  gp: number;
  gol_kontrak?: number;
  gol_tetap?: number;
  beban_kerja?: number;
  insentif?: number;
  tunj_keahlian?: number;
  tunj_jab?: number;
  tot_gross: number;
  bpjstku_yay?: number;
  bpjskes_yay?: number;
  pph_yay?: number;
  tot_gross_yay?: number;
  awol?: number;
  jlh_awol?: number;
  freq?: number;
  minute?: number;
  jlh_freq?: number;
  jlh_minute?: number;
  pot_abs?: number;
  gaji_stlh_pot_abs?: number;
  bpjstku?: number;
  bpjskes?: number;
  nett_stlh_bpjs?: number;
  pph?: number;
  pinj_lain?: number;
  iuran_wajib?: number;
  pinj_kop?: number;
  piutang?: number;
  pot_bank?: number;
  tot_pot?: number;
  gaji_netto: number;
  period: Date;
  created_at: Date;
  updated_at: Date;
}

export interface SalaryGuru {
  id: number;
  nama: string;
  gp: number;
  load?: number;
  hm?: number;
  nominal?: number;
  jlh_xc?: number;
  jab?: number;
  walas?: number;
  pemb_osis?: number;
  xc?: number;
  menggantikan?: number;
  tot?: number;
  sl?: boolean;
  vl?: boolean;
  awol?: boolean;
  jlh_sl?: number;
  jlh_vl?: number;
  jlh_awol?: number;
  freq?: boolean;
  minute?: boolean;
  jlh_freq?: number;
  jlh_minute?: number;
  pot_abs?: number;
  pot_ngajar?: number;
  bruto?: number;
  pot_bpjstku?: number;
  pot_bpjskes?: number;
  netto_stlh_bpjs?: number;
  pph21?: number;
  pinj_lainnya?: number;
  iuran_wajib?: number;
  pinj_kop?: number;
  piutang?: number;
  pot_bank?: number;
  tot_pot?: number;
  gaji_netto: number;
  period: Date;
  created_at: Date;
  updated_at: Date;
}

export interface SalaryHonor {
  id: number;
  nama: string;
  gp1?: number;
  pph_dibayar_sklh?: number;
  tot_tun_tep?: number;
  hnr?: number;
  jp?: number;
  tot_hnr?: number;
  jlh_hdr?: number;
  hnr_hdr?: number;
  tot_hnr_hdr?: number;
  sblm_pph?: number;
  jlhxc?: number;
  xc?: number;
  tmbhn?: number;
  gaji_bruto?: number;
  bruto_stlh_pot?: number;
  nettstlhjamsostek?: number;
  pph21?: number;
  pinj_lainnya?: number;
  iuran_wajib?: number;
  pinj_kop?: number;
  piutang?: number;
  pot_bank?: number;
  gaji_netto: number;
  period: Date;
  created_at: Date;
  updated_at: Date;
}