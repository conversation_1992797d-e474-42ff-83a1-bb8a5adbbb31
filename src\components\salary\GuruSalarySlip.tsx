import { SalaryGuru } from "@/lib/types";
import { useState } from "react";

interface GuruSalarySlipProps {
  data: SalaryGuru;
}

export function GuruSalarySlip({ data }: GuruSalarySlipProps) {
  // Format currency
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return "-";

    // Handle NaN values
    if (isNaN(Number(amount))) return "-";

    // Convert to number to ensure proper formatting
    const numAmount = Number(amount);

    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numAmount);
  };

  // Format date
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString('id-ID', { month: 'long', year: 'numeric' });
  };

  // Format absensi value
  const formatAbsensiValue = (value: any) => {
    if (value === null || value === undefined || value === 0 || value === false) {
      return "Tidak Ada";
    }
    return value;
  };

  return (
    <div className="p-1 px-8 bg-white text-xs">
      {/* Top separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5"></div>

      {/* Header */}
      <div className="mb-2">
        <div className="flex items-center w-full mb-2">
          <div className="mr-4">
            <img
              src="/images/LOGO%20Medium.jpg"
              alt="Sekolah Darma Bangsa Logo"
              className="h-8 w-auto object-contain"
            />
          </div>
          <div className="text-center flex-1">
            <h1 className="text-base font-bold">SEKOLAH DARMA BANGSA</h1>
            <h2 className="text-sm font-bold">SLIP GAJI</h2>
          </div>
        </div>
        <div className="border-t border-b border-dashed border-gray-400 py-0.5 w-full"></div>
      </div>

      {/* Employee Info */}
      <div className="mb-1">
        <div className="flex items-start">
          <div className="w-36 min-w-[9rem] flex-shrink-0">Periode</div>
          <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
          <div className="flex-1">{formatDate(data.period)}</div>
        </div>
        <div className="flex items-start">
          <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Nama Karyawan</div>
          <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
          <div className="flex-1">{data.nama}</div>
        </div>
      </div>

      {/* Separator line */}
      <div className="border-t border-b border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Two column layout for main content */}
      <div className="flex w-full">
        {/* Left Column */}
        <div className="w-1/2 pr-4 border-r border-dashed border-gray-400" style={{ borderRightWidth: '2px', minWidth: '50%' }}>
          {/* Gaji Pokok */}
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Gaji Pokok</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1 pr-2">{formatCurrency(data.gp)}</div>
          </div>

          {/* Separator line */}
          <div className="border-t border-dashed border-gray-400 py-0.5"></div>

          {/* Honor Mengajar */}
          <div>Honor Mengajar</div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Load Mengajar</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1 pr-2">{data.load || "-"}</div>
          </div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Honor Mengajar</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1 pr-2">{formatCurrency(data.hm)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Total</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1 pr-2">{formatCurrency(data.nominal)}</div>
          </div>

          {/* Honor Variabel */}
          <div>Honor Variabel</div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">XC</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.xc)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Jumlah XC</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.jlh_xc)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Honor XC</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.xc)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Wali Kelas</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.walas)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Pembina Osis</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pemb_osis)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Jabatan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.jab)}</div>
          </div>

          {/* Menggantikan */}
          <div>Menggantikan</div>
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] pl-4 flex-shrink-0">Menggantikan Mengajar</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.menggantikan)}</div>
          </div>

          {/* Separator line */}
          <div className="border-t border-dashed border-gray-400 py-0.5"></div>

          {/* Total */}
          <div className="flex items-start">
            <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Total</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.tot)}</div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-1/2 pl-4" style={{ minWidth: '50%' }}>
          {/* Potongan */}
          <div>Potongan</div>
          <div className="pl-4">Absensi</div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Tanpa Keterangan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatAbsensiValue(data.awol)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Nominal Potongan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatAbsensiValue(data.jlh_awol)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Terlambat Frequensi</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatAbsensiValue(data.freq)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Nominal Potongan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatAbsensiValue(data.jlh_freq)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Terlambat Menit</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatAbsensiValue(data.minute)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Nominal Potongan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatAbsensiValue(data.jlh_minute)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Total Potongan Absensi</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pot_abs)}</div>
          </div>

          {/* Mengajar */}
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-4 flex-shrink-0">Mengajar</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pot_ngajar)}</div>
          </div>

          {/* BPJS */}
          <div className="pl-4 mt-2">BPJS</div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">BPJS Ketenagakerjaan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pot_bpjstku)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">BPJS Kesehatan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pot_bpjskes)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Total Potongan BPJS</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(Number(data.pot_bpjstku || 0) + Number(data.pot_bpjskes || 0))}</div>
          </div>

          {/* Lain-lain */}
          <div className="pl-4 mt-2">Lain-lain</div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">PPh21</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pph21)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Pinjaman lain (bila ada)</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pinj_lainnya)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Koperasi</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-12 flex-shrink-0">Iuran Wajib</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.iuran_wajib)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-12 flex-shrink-0">Pinjaman Koperasi</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pinj_kop)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Potongan Bank</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.pot_bank)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Total Potongan Lain-lain</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(Number(data.pph21 || 0) + Number(data.pinj_lainnya || 0) + Number(data.iuran_wajib || 0) + Number(data.pinj_kop || 0) + Number(data.pot_bank || 0))}</div>
          </div>
          <div className="flex items-start">
            <div className="w-44 min-w-[11rem] pl-8 flex-shrink-0">Total Potongan</div>
            <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
            <div className="flex-1">{formatCurrency(data.tot_pot)}</div>
          </div>
        </div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-1 mt-2"></div>

      {/* Piutang */}
      <div className="mb-2">
        <div className="flex items-start">
          <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Piutang</div>
          <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
          <div className="flex-1">{formatCurrency(data.piutang)}</div>
        </div>
      </div>

      {/* Separator line */}
      <div className="border-t border-b border-dashed border-gray-400 py-0.5 mt-1">
        <div className="flex items-start font-bold">
          <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Gaji Nett</div>
          <div className="w-4 min-w-[1rem] flex-shrink-0">:</div>
          <div className="flex-1">{formatCurrency(data.gaji_netto)}</div>
        </div>
      </div>

      {/* Bottom separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mt-1"></div>

      {/* Footer */}
      <div className="text-[7px] text-right italic text-gray-500 mt-1 pr-2">
        Dicetak secara elektronik pada {new Date().getDate()}/{new Date().getMonth() + 1}/{new Date().getFullYear()} sehingga tidak membutuhkan tanda tangan. Untuk legalitas perlu ditambahkan cap basah Yayasan
      </div>
    </div>
  );
}
