/**
 * API Route: GET /api/leave-management/calendar
 *
 * Description: Fetches leave events for the calendar view
 * Usage: Leave management calendar tab
 *
 * Response:
 * - 200: List of leave events formatted for the calendar
 * - 401: Unauthorized
 * - 500: Server error
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function GET() {
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json([], { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;
    const userId = userData.id;

    let whereClause = {};

    // Filter based on user role
    if (userRole === 'EMPLOYEE') {
      // Employee can only see their own leave requests
      whereClause = {
        employeeId: parseInt(userId)
      };
    } else if (userRole === 'SUPERVISOR') {
      try {
        // Get supervisor's department
        const supervisor = await prisma.employee.findUnique({
          where: { id: parseInt(userId) },
          select: { departmentId: true }
        });

        if (supervisor && supervisor.departmentId) {
          // Get all employees in the same department
          const departmentEmployees = await prisma.employee.findMany({
            where: { departmentId: supervisor.departmentId },
            select: { id: true }
          });

          const employeeIds = departmentEmployees.map(emp => emp.id);

          // Filter leave requests by employees in the department
          whereClause = {
            employeeId: { in: employeeIds }
          };
        } else {
          // If supervisor has no department, show only their leave requests
          whereClause = {
            employeeId: parseInt(userId)
          };
        }
      } catch (error) {
        console.error('Error finding department employees:', error);
        // Fallback to showing only supervisor's leave requests
        whereClause = {
          employeeId: parseInt(userId)
        };
      }
    }
    // For ADMIN, whereClause remains empty (show all)

    // Fetch leave requests with the appropriate filter
    const leaveRequests = await prisma.leaveRequest.findMany({
      where: whereClause,
      include: {
        employee: true,
        leaveType: true
      },
      orderBy: {
        startDate: 'asc'
      }
    });

    // Format the data for the calendar
    const calendarEvents = leaveRequests.map(request => ({
      id: `LV${String(request.id).padStart(3, '0')}`,
      title: request.leaveType.name,
      employee: `${request.employee.firstName} ${request.employee.lastName}`,
      from: request.startDate,
      to: request.endDate,
      status: request.status,
      reason: request.reason,
      type: request.leaveType.name
    }));

    return NextResponse.json(calendarEvents);
  } catch (error) {
    console.error('Error fetching calendar events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch calendar events' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
