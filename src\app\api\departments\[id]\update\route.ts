/**
 * API Route: PUT /api/departments/[id]/update
 *
 * Deskripsi: Memperbarui data departemen berdasarkan ID
 * Penggunaan: Form edit departemen
 *
 * Path Parameters:
 * - id: ID departemen (number)
 *
 * Body:
 * - name: <PERSON><PERSON> departemen (string)
 * - description: <PERSON><PERSON><PERSON><PERSON> departemen (string, opsional)
 * - head: ID kepala departemen (string, 'none' jika tidak ada)
 *
 * Response:
 * - 200: Departemen berhasil diperbarui
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Departemen tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extract ID from URL path
    const id = request.nextUrl.pathname.split('/')[3] || '0';
    const data = await request.json();

    // Validasi nama departemen
    if (!data.name || data.name.trim() === '') {
      return NextResponse.json(
        { error: 'Department name is required' },
        { status: 400 }
      );
    }

    // Validate the department exists
    const existingDepartment = await prisma.department.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingDepartment) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    // Cek apakah nama departemen sudah digunakan oleh departemen lain
    const duplicateName = await prisma.department.findFirst({
      where: {
        name: data.name,
        NOT: {
          id: parseInt(id)
        }
      }
    });

    if (duplicateName) {
      return NextResponse.json(
        { error: 'Department with this name already exists' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: { name: string; description: string; headId?: number | null } = {
      name: data.name,
      description: data.description
    };

    // Only include headId if it's not "none"
    if (data.head && data.head !== 'none') {
      updateData.headId = parseInt(data.head);
    } else {
      updateData.headId = null;
    }

    const updatedDepartment = await prisma.department.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        head: {
          select: {
            firstName: true,
            lastName: true,
          }
        }
      }
    });

    return NextResponse.json(updatedDepartment);
  } catch (error) {
    console.error('Error updating department:', error);
    return NextResponse.json(
      { error: 'Failed to update department' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
