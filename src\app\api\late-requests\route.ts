import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



// GET: Mengambil semua late requests sesuai dengan role user
export async function GET() {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;
    const userId = userData.id;

    let lateRequests;

    // Admin dapat melihat semua late requests
    if (userRole === 'ADMIN') {
      lateRequests = await prisma.lateRequest.findMany({
        include: {
          employee: {
            select: {
              firstName: true,
              lastName: true,
              position: true,
            },
          },
        },
        orderBy: [
          { createdAt: 'desc' },
          { lateDate: 'desc' }
        ],
      });
    }
    // HEAD dapat melihat late requests dari karyawan dengan posisi Chief, <PERSON><PERSON><PERSON>, atau Psikolog
    else if (userRole === 'HEAD') {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const headEmployeeId = parseInt(userData.employeeId || userData.id);

      // Ambil semua late requests
      const allLateRequests = await prisma.lateRequest.findMany({
        include: {
          employee: {
            include: {
              position: true,
              department: true
            }
          }
        },
        orderBy: [
          { createdAt: 'desc' },
          { lateDate: 'desc' }
        ]
      });

      // Filter late requests berdasarkan posisi karyawan (Chief, Kepala, atau Psikolog)
      lateRequests = allLateRequests.filter(request => {
        // Tambahkan request milik HEAD sendiri
        if (request.employeeId === headEmployeeId) {
          return true;
        }

        // Filter berdasarkan posisi
        const positionTitle = request.employee?.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        return isChiefOrKepalaOrPsikolog;
      });

      console.log(`HEAD (ID: ${headEmployeeId}) - Filtered to ${lateRequests.length} late requests for Chief/Kepala/Psikolog positions`);
    }
    // Supervisor dapat melihat late requests dari bawahannya
    else if (userRole === 'SUPERVISOR') {
      try {
        // Gunakan employeeId jika ada, jika tidak gunakan id
        const employeeId = parseInt(userData.employeeId || userData.id);

        // Cari departemen yang dipimpin oleh supervisor ini
        const departments = await prisma.department.findMany({
          where: {
            headId: employeeId
          },
          select: {
            id: true,
            name: true
          }
        });

        console.log(`SUPERVISOR (ID: ${userId}) - Found ${departments.length} departments headed by supervisor`);

        let employeeIds = [];

        if (departments.length > 0) {
          // Dapatkan semua karyawan di departemen yang dipimpin oleh supervisor
          const departmentIds = departments.map(dept => dept.id);

          const departmentEmployees = await prisma.employee.findMany({
            where: {
              departmentId: { in: departmentIds }
            },
            select: { id: true }
          });

          console.log(`Found ${departmentEmployees.length} employees in departments headed by supervisor`);

          // Buat array ID karyawan
          employeeIds = departmentEmployees.map(emp => emp.id);

          // Tambahkan ID supervisor ke array employeeIds jika belum ada
          if (!employeeIds.includes(employeeId)) {
            employeeIds.push(employeeId);
          }
        } else {
          // Jika supervisor tidak memimpin departemen, coba cari departemen tempat dia bekerja
          const supervisor = await prisma.employee.findUnique({
            where: { id: employeeId },
            select: { departmentId: true }
          });

          if (supervisor && supervisor.departmentId) {
            // Dapatkan semua karyawan di departemen yang sama
            const departmentEmployees = await prisma.employee.findMany({
              where: {
                departmentId: supervisor.departmentId
              },
              select: { id: true }
            });

            console.log(`Found ${departmentEmployees.length} employees in supervisor's department`);

            // Buat array ID karyawan
            employeeIds = departmentEmployees.map(emp => emp.id);

            // Tambahkan ID supervisor ke array employeeIds jika belum ada
            if (!employeeIds.includes(employeeId)) {
              employeeIds.push(employeeId);
            }
          } else {
            // Jika supervisor tidak memiliki departemen, tampilkan hanya late request miliknya sendiri
            employeeIds = [employeeId];
          }
        }

        console.log('Employee IDs for filtering:', employeeIds);

        lateRequests = await prisma.lateRequest.findMany({
          where: {
            employeeId: { in: employeeIds }
          },
          include: {
            employee: {
              include: {
                department: {
                  include: {
                    head: {
                      select: {
                        id: true,
                        employeeId: true,
                        firstName: true,
                        lastName: true
                      }
                    }
                  }
                }
              }
            }
          },
          orderBy: [
            { createdAt: 'desc' },
            { lateDate: 'desc' }
          ]
        });
      } catch (error) {
        console.error('Error finding departments or employees:', error);
        // Jika terjadi error, tampilkan hanya late request miliknya sendiri
        lateRequests = await prisma.lateRequest.findMany({
          where: {
            employeeId: parseInt(userId)
          },
          include: {
            employee: {
              include: {
                department: {
                  include: {
                    head: {
                      select: {
                        id: true,
                        employeeId: true,
                        firstName: true,
                        lastName: true
                      }
                    }
                  }
                }
              }
            }
          },
          orderBy: [
            { createdAt: 'desc' },
            { lateDate: 'desc' }
          ]
        });
      }
    }
    // Employee hanya dapat melihat late requests miliknya sendiri
    else {
      lateRequests = await prisma.lateRequest.findMany({
        where: {
          employeeId: parseInt(userId),
        },
        include: {
          employee: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: [
          { createdAt: 'desc' },
          { lateDate: 'desc' }
        ],
      });
    }

    return NextResponse.json(lateRequests);
  } catch (error) {
    console.error('Error fetching late requests:', error);
    return NextResponse.json(
      { error: 'Failed to fetch late requests' },
      { status: 500 }
    );
  }
}
