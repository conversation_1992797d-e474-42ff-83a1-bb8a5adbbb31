/**
 * API Route: /api/leave-types
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { logger } from '@/lib/logger';



export async function GET() {
  logger.debug('GET /api/leave-types - Start fetching leave types');
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      // Jangan log error, ini normal ketika user logout
      return NextResponse.json([], { status: 401 });
    }

    logger.debug('GET /api/leave-types - User authenticated');
    logger.debug('GET /api/leave-types - Executing database query');

    const leaveTypes = await prisma.leaveType.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    logger.debug(`GET /api/leave-types - Found ${leaveTypes.length} leave types`);
    return NextResponse.json(leaveTypes);
  } catch (error) {
    logger.error('GET /api/leave-types - Failed to fetch leave types:', error);
    return NextResponse.json({ error: 'Failed to fetch leave types' }, { status: 500 });
  } finally {
    // Jangan log pesan disconnect, ini normal
    await prisma.$disconnect();
  }
}

export async function POST(request: Request) {
  try {
    // Verify admin/supervisor access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    if (!['ADMIN', 'SUPERVISOR', 'HEAD'].includes(userData.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await request.json();
    const { name, description, daysAllowed, requiresApproval } = data;

    // Validate required fields
    if (!name || !daysAllowed) {
      return NextResponse.json(
        { error: 'Name and days allowed are required' },
        { status: 400 }
      );
    }

    // Create new leave type
    const leaveType = await prisma.leaveType.create({
      data: {
        name,
        description,
        daysAllowed: parseInt(daysAllowed),
        requiresApproval: requiresApproval ?? true
      }
    });

    return NextResponse.json(leaveType);
  } catch (error) {
    console.error('Failed to create leave type:', error);
    return NextResponse.json(
      { error: 'Failed to create leave type' },
      { status: 500 }
    );
  }
}


