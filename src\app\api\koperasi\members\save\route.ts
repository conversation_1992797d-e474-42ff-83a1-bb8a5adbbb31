import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';



export async function POST(request: Request) {
  try {
    const data = await request.json();
    console.log('POST /api/koperasi/members/save - Received data:', data);

    // Validasi data yang diterima
    if (!data.employeeId) {
      return NextResponse.json(
        { error: 'Employee ID is required' },
        { status: 400 }
      );
    }

    if (!data.monthlyContribution) {
      return NextResponse.json(
        { error: 'Monthly contribution is required' },
        { status: 400 }
      );
    }

    if (!data.joinDate) {
      return NextResponse.json(
        { error: 'Join date is required' },
        { status: 400 }
      );
    }

    // Konversi employeeId ke number
    const employeeIdNumber = parseInt(data.employeeId);
    if (isNaN(employeeIdNumber)) {
      return NextResponse.json(
        { error: 'Invalid employee ID format' },
        { status: 400 }
      );
    }

    // Cek apakah employee dengan ID tersebut ada
    const employee = await prisma.employee.findUnique({
      where: { id: employeeIdNumber }
    });

    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Cek apakah employee sudah terdaftar sebagai anggota koperasi
    const existingMember = await prisma.koperasiMember.findUnique({
      where: { employeeId: employeeIdNumber }
    });

    if (existingMember) {
      return NextResponse.json(
        { error: 'Employee is already registered as a koperasi member' },
        { status: 400 }
      );
    }

    // Konversi nilai kontribusi ke Decimal
    const monthlyContribution = parseFloat(data.monthlyContribution) || 0;
    const oneTimeContribution = parseFloat(data.oneTimeContribution) || 0;
    const optionalContribution = parseFloat(data.optionalContribution) || 0;
    
    // Hitung total savings awal
    const totalSavings = oneTimeContribution + monthlyContribution + optionalContribution;

    // Buat anggota koperasi baru
    const newMember = await prisma.koperasiMember.create({
      data: {
        employeeId: employeeIdNumber,
        joinDate: new Date(data.joinDate),
        monthlyContribution: monthlyContribution,
        oneTimeContribution: oneTimeContribution > 0 ? oneTimeContribution : null,
        optionalContribution: optionalContribution > 0 ? optionalContribution : null,
        totalSavings: totalSavings,
        status: 'active',
        notes: data.notes || null
      },
      include: {
        employee: true
      }
    });

    // Jika ada kontribusi awal, buat transaksi savings
    if (oneTimeContribution > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: newMember.id,
          amount: oneTimeContribution,
          type: 'deposit',
          contributionType: 'one_time',
          date: new Date(),
          notes: 'Initial one-time contribution'
        }
      });
    }

    if (monthlyContribution > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: newMember.id,
          amount: monthlyContribution,
          type: 'deposit',
          contributionType: 'monthly',
          date: new Date(),
          notes: 'Initial monthly contribution'
        }
      });
    }

    if (optionalContribution > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: newMember.id,
          amount: optionalContribution,
          type: 'deposit',
          contributionType: 'optional',
          date: new Date(),
          notes: 'Initial optional contribution'
        }
      });
    }

    console.log('POST /api/koperasi/members/save - Created new member:', newMember);

    return NextResponse.json({
      success: true,
      message: 'Member registered successfully',
      member: {
        id: newMember.id,
        employee_id: newMember.employeeId,
        employee_name: `${newMember.employee.firstName} ${newMember.employee.lastName}`,
        join_date: newMember.joinDate,
        monthly_contribution: Number(newMember.monthlyContribution),
        one_time_contribution: newMember.oneTimeContribution ? Number(newMember.oneTimeContribution) : 0,
        optional_contribution: newMember.optionalContribution ? Number(newMember.optionalContribution) : 0,
        total_savings: Number(newMember.totalSavings),
        status: newMember.status
      }
    });
  } catch (error) {
    console.error('POST /api/koperasi/members/save - Error:', error);
    return NextResponse.json(
      { error: 'Failed to register member' },
      { status: 500 }
    );
  }
}
