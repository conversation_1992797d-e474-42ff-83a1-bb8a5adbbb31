/**
 * API Route: GET /api/leave-management/reports
 *
 * Deskripsi: Mengambil data untuk report leave management
 * Penggunaan: Halaman report leave management
 *
 * Query Parameters:
 * - type: Tipe report (leave, late, exit)
 * - departmentId: ID departemen (opsional)
 * - leaveTypeId: ID tipe leave (opsional, hanya untuk leave)
 * - lateType: <PERSON><PERSON><PERSON> k<PERSON> (opsional, hanya untuk late)
 * - exitType: Tipe keluar (opsional, hanya untuk exit)
 * - startDate: Tanggal mulai (opsional)
 * - endDate: Tanggal akhir (opsional)
 *
 * Response:
 * - 200: Data report
 * - 400: Parameter tidak valid
 * - 401: Tidak terautentikasi
 * - 500: Error server
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { logger } from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    // Hanya ADMIN, SUPERVISOR, dan HEAD yang dapat mengakses report
    if (userRole !== 'ADMIN' && userRole !== 'SUPERVISOR' && userRole !== 'HEAD') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Ambil parameter dari query
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type');
    const departmentId = searchParams.get('departmentId');
    const leaveTypeId = searchParams.get('leaveTypeId');
    const lateType = searchParams.get('lateType');
    const exitType = searchParams.get('exitType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Validasi parameter
    if (!type || !['leave', 'late', 'exit'].includes(type)) {
      return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
    }

    // Buat filter berdasarkan parameter
    let filter: any = {};
    let dateFilter: any = {};

    // Filter departemen
    if (departmentId) {
      filter.employee = {
        departmentId: parseInt(departmentId)
      };
    }

    // Filter tanggal
    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);

      if (type === 'leave') {
        dateFilter.startDate = {
          gte: start
        };
      } else if (type === 'late') {
        dateFilter.lateDate = {
          gte: start
        };
      } else if (type === 'exit') {
        dateFilter.exitDate = {
          gte: start
        };
      }
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);

      if (type === 'leave') {
        dateFilter.endDate = {
          lte: end
        };
      } else if (type === 'late') {
        dateFilter.lateDate = {
          lte: end
        };
      } else if (type === 'exit') {
        dateFilter.exitDate = {
          lte: end
        };
      }
    }

    // Gabungkan filter
    filter = { ...filter, ...dateFilter };

    // Filter spesifik berdasarkan tipe report
    if (type === 'leave' && leaveTypeId) {
      filter.leaveTypeId = parseInt(leaveTypeId);
    } else if (type === 'late' && lateType) {
      filter.lateType = lateType;
    } else if (type === 'exit' && exitType) {
      filter.exitType = exitType;
    }

    // Ambil data berdasarkan tipe report
    let data;
    let departments;
    let leaveTypes;

    // Ambil data departemen untuk filter
    departments = await prisma.department.findMany({
      select: {
        id: true,
        name: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    if (type === 'leave') {
      // Ambil data leave requests
      data = await prisma.leaveRequest.findMany({
        where: filter,
        include: {
          employee: {
            select: {
              firstName: true,
              lastName: true,
              department: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          leaveType: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Ambil data leave types untuk filter
      leaveTypes = await prisma.leaveType.findMany({
        select: {
          id: true,
          name: true
        },
        orderBy: {
          name: 'asc'
        }
      });

      // Hitung statistik
      const stats = {
        total: data.length,
        byDepartment: {} as Record<string, number>,
        byLeaveType: {} as Record<string, number>,
        byStatus: {
          pending: 0,
          approved: 0,
          rejected: 0
        }
      };

      // Hitung statistik berdasarkan departemen
      data.forEach((item: any) => {
        const departmentName = item.employee?.department?.name || 'Unknown';
        const leaveTypeName = item.leaveType?.name || 'Unknown';
        const status = item.status;

        // Hitung berdasarkan departemen
        if (!stats.byDepartment[departmentName]) {
          stats.byDepartment[departmentName] = 0;
        }
        stats.byDepartment[departmentName]++;

        // Hitung berdasarkan tipe leave
        if (!stats.byLeaveType[leaveTypeName]) {
          stats.byLeaveType[leaveTypeName] = 0;
        }
        stats.byLeaveType[leaveTypeName]++;

        // Hitung berdasarkan status
        stats.byStatus[status]++;
      });

      return NextResponse.json({
        type: 'leave',
        data,
        stats,
        filters: {
          departments,
          leaveTypes
        }
      });
    } else if (type === 'late') {
      // Ambil data late requests
      data = await prisma.lateRequest.findMany({
        where: filter,
        include: {
          employee: {
            select: {
              firstName: true,
              lastName: true,
              department: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Hitung statistik
      const stats = {
        total: data.length,
        byDepartment: {} as Record<string, number>,
        byLateType: {
          Urgent: 0,
          Work: 0
        },
        byStatus: {
          pending: 0,
          approved: 0,
          rejected: 0
        }
      };

      // Hitung statistik berdasarkan departemen dan tipe
      data.forEach((item: any) => {
        const departmentName = item.employee?.department?.name || 'Unknown';
        const lateType = item.lateType;
        const status = item.status;

        // Hitung berdasarkan departemen
        if (!stats.byDepartment[departmentName]) {
          stats.byDepartment[departmentName] = 0;
        }
        stats.byDepartment[departmentName]++;

        // Hitung berdasarkan tipe late
        stats.byLateType[lateType]++;

        // Hitung berdasarkan status
        stats.byStatus[status]++;
      });

      return NextResponse.json({
        type: 'late',
        data,
        stats,
        filters: {
          departments,
          lateTypes: [
            { id: 'Urgent', name: 'Urgent' },
            { id: 'Work', name: 'Work' }
          ]
        }
      });
    } else if (type === 'exit') {
      // Ambil data exit requests
      data = await prisma.exitRequest.findMany({
        where: filter,
        include: {
          employee: {
            select: {
              firstName: true,
              lastName: true,
              department: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Hitung statistik
      const stats = {
        total: data.length,
        byDepartment: {} as Record<string, number>,
        byExitType: {
          Work: 0,
          Personal: 0
        },
        byStatus: {
          pending: 0,
          approved: 0,
          rejected: 0
        },
        byComeback: {
          comeback: 0,
          notComeback: 0
        }
      };

      // Hitung statistik berdasarkan departemen dan tipe
      data.forEach((item: any) => {
        const departmentName = item.employee?.department?.name || 'Unknown';
        const exitType = item.exitType;
        const status = item.status;
        const notComeback = item.notComeback;

        // Hitung berdasarkan departemen
        if (!stats.byDepartment[departmentName]) {
          stats.byDepartment[departmentName] = 0;
        }
        stats.byDepartment[departmentName]++;

        // Hitung berdasarkan tipe exit
        stats.byExitType[exitType]++;

        // Hitung berdasarkan status
        stats.byStatus[status]++;

        // Hitung berdasarkan comeback
        if (notComeback) {
          stats.byComeback.notComeback++;
        } else {
          stats.byComeback.comeback++;
        }
      });

      return NextResponse.json({
        type: 'exit',
        data,
        stats,
        filters: {
          departments,
          exitTypes: [
            { id: 'Work', name: 'Work' },
            { id: 'Personal', name: 'Personal' }
          ]
        }
      });
    }

    return NextResponse.json({ error: 'Invalid report type' }, { status: 400 });
  } catch (error) {
    logger.error('Error in GET /api/leave-management/reports:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
