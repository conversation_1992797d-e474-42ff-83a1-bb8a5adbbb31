/**
 * API Route: PUT /api/koperasi/savings/[id]/update
 *
 * Deskripsi: Mengupdate transaksi simpanan koperasi
 * Penggunaan: Form edit simpanan koperasi
 *
 * Body:
 * - amount: <PERSON><PERSON><PERSON> simpanan (number)
 * - type: <PERSON><PERSON><PERSON> simpanan ('deposit' atau 'withdrawal')
 * - contributionType: <PERSON>ip<PERSON> kont<PERSON> ('one_time', 'monthly', atau 'optional')
 * - date: Tanggal simpanan (string, format: YYYY-MM-DD)
 * - notes: Catatan (string, opsional)
 *
 * Response:
 * - 200: Simpanan koperasi berhasil diupdate
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Simpanan koperasi tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { db } from '@/lib/db';

// Use the shared Prisma client instance
const prisma = db;

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    // Verify admin or operator_kop access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from params
    const id = parseInt(params.id);
    const data = await request.json();
    const { amount, type, contributionType, date, notes } = data;

    // Validate data
    if (!amount || !type || !date) {
      return NextResponse.json(
        { error: 'Amount, type, and date are required' },
        { status: 400 }
      );
    }

    if (!['deposit', 'withdrawal'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be either deposit or withdrawal' },
        { status: 400 }
      );
    }

    if (type === 'deposit' && !contributionType) {
      return NextResponse.json(
        { error: 'Contribution type is required for deposits' },
        { status: 400 }
      );
    }

    if (
      type === 'deposit' &&
      !['one_time', 'monthly', 'optional'].includes(contributionType)
    ) {
      return NextResponse.json(
        { error: 'Contribution type must be one_time, monthly, or optional' },
        { status: 400 }
      );
    }

    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      return NextResponse.json(
        { error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // Check if saving exists
    const existingSaving = await prisma.koperasiSaving.findUnique({
      where: { id },
      include: {
        member: true
      }
    });

    if (!existingSaving) {
      return NextResponse.json(
        { error: 'Koperasi saving not found' },
        { status: 404 }
      );
    }

    // Tidak perlu memperbarui total savings atau kolom kontribusi
    // Transaksi yang sudah diproses tidak seharusnya mempengaruhi data anggota ketika diupdate

    // Hanya update data transaksi tanpa mempengaruhi data anggota
    const updatedSaving = await prisma.koperasiSaving.update({
      where: { id },
      data: {
        amount: amountValue,
        type,
        contributionType: type === 'deposit' ? contributionType : null,
        date: new Date(date),
        notes: notes || null,
        updatedAt: new Date()
      },
      include: {
        member: {
          include: {
            employee: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }
      }
    });

    // Format response
    const formattedSaving = {
      id: updatedSaving.id,
      member_id: updatedSaving.memberId,
      member_name: `${updatedSaving.member.employee.firstName} ${updatedSaving.member.employee.lastName}`,
      email: updatedSaving.member.employee.email,
      amount: Number(updatedSaving.amount),
      type: updatedSaving.type,
      contributionType: updatedSaving.contributionType,
      date: updatedSaving.date,
      notes: updatedSaving.notes,
      created_at: updatedSaving.createdAt,
      updated_at: updatedSaving.updatedAt
    };

    return NextResponse.json(formattedSaving);
  } catch (error) {
    console.error('Error updating koperasi saving:', error);
    return NextResponse.json(
      { error: 'Failed to update koperasi saving' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
