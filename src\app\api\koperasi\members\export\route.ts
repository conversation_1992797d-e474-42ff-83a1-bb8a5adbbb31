/**
 * API Route: POST /api/koperasi/members/export
 *
 * Description: Export koperasi member data to Excel
 * Usage: Export button in koperasi members tab
 *
 * Response:
 * - 200: Excel file with koperasi member data
 * - 401: Unauthorized
 * - 403: Forbidden
 * - 500: Server error
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';
import { db } from '@/lib/db';
import { format } from 'date-fns';

// Use the shared Prisma client instance
const prisma = db;

export async function GET() {
  try {
    // Verify admin or operator_kop access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP') {
      return NextResponse.json({ error: 'Only administrators and koperasi operators can export member data' }, { status: 403 });
    }

    // Get all koperasi members with employee data
    const members = await prisma.koperasiMember.findMany({
      include: {
        employee: true
      },
      orderBy: {
        id: 'asc'
      }
    });

    if (members.length === 0) {
      return NextResponse.json({ error: 'No members found' }, { status: 404 });
    }

    // Format date for Excel
    const formatDate = (dateString: string | Date | null) => {
      if (!dateString) return '';
      try {
        const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
        return format(date, 'dd/MM/yyyy');
      } catch (error) {
        console.error('Error formatting date:', error);
        return '';
      }
    };

    // Format currency for Excel
    const formatCurrency = (value: any) => {
      if (value === null || value === undefined) return 0;

      // Convert from Decimal or String to Number
      let numValue;
      if (typeof value === 'string') {
        numValue = parseFloat(value);
      } else if (typeof value === 'object' && value !== null) {
        // Handle Prisma Decimal objects
        numValue = parseFloat(value.toString());
      } else {
        numValue = Number(value);
      }

      // Return 0 if NaN
      return isNaN(numValue) ? 0 : numValue;
    };

    // Format currency for display
    const formatCurrencyDisplay = (value: any) => {
      const numValue = formatCurrency(value);
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(numValue);
    };

    // Prepare data for Excel with only the specified columns
    const excelData = members.map(member => ({
      'Employee Name': `${member.employee.firstName} ${member.employee.lastName}`,
      'Join Date': formatDate(member.joinDate),
      'One Time Contribution': formatCurrencyDisplay(member.oneTimeContribution),
      'Monthly Contribution': formatCurrencyDisplay(member.monthlyContribution),
      'Optional Contribution': formatCurrencyDisplay(member.optionalContribution),
      'Total Savings': formatCurrencyDisplay(member.totalSavings),
      'Status': member.status === 'active' ? 'Active' : 'Inactive'
    }));

    // Log the first few records for debugging
    console.log('First 3 records for export:', excelData.slice(0, 3));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 30 }, // Employee Name
      { wch: 15 }, // Join Date
      { wch: 20 }, // One Time Contribution
      { wch: 20 }, // Monthly Contribution
      { wch: 20 }, // Optional Contribution
      { wch: 20 }, // Total Savings
      { wch: 10 }  // Status
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Koperasi Members');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });

    // Get current date for filename
    const today = new Date();
    const dateStr = format(today, 'yyyy-MM-dd');

    // Return Excel file as response
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="koperasi_members_${dateStr}.xlsx"`,
      },
    });
  } catch (error) {
    console.error('Error exporting koperasi members:', error);
    return NextResponse.json(
      { error: 'Failed to export koperasi members' },
      { status: 500 }
    );
  }
}
