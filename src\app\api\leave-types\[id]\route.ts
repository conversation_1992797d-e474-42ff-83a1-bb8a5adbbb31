/**
 * API Route: /api/leave-types/[id]
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



// PUT handler untuk edit
export async function PUT(request: Request) {
  // Get the ID from the URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1]; // Get the ID from the URL path

  try {
    // Verify admin access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const data = await request.json();

    // Explicitly construct the update data object
    const updateData = {
      name: data.name,
      description: data.description,
      daysAllowed: Number(data.daysAllowed), // Ensure this is a number
      requiresApproval: Boolean(data.requiresApproval) // Ensure this is a boolean
    };

    const updatedLeaveType = await prisma.leaveType.update({
      where: {
        id: parseInt(id)
      },
      data: updateData
    });

    return NextResponse.json(updatedLeaveType);
  } catch (error) {
    console.error('Failed to update leave type:', error);
    return NextResponse.json(
      { error: 'Failed to update leave type' },
      { status: 500 }
    );
  }
}

// DELETE handler
export async function DELETE(request: Request) {
  // Get the ID from the URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1]; // Get the ID from the URL path

  try {
    // Verify admin access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if there are any leave requests using this type
    const existingRequests = await prisma.leaveRequest.findFirst({
      where: { leaveTypeId: parseInt(id) }
    });

    if (existingRequests) {
      return NextResponse.json(
        { error: 'Cannot delete leave type that has associated leave requests' },
        { status: 400 }
      );
    }

    await prisma.leaveType.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete leave type:', error);
    return NextResponse.json(
      { error: 'Failed to delete leave type' },
      { status: 500 }
    );
  }
}
