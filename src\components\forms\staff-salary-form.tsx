import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { SalaryStaff } from "@/lib/types";

// Define schema for form validation
const formSchema = z.object({
  nama: z.string().min(1, "Nama is required"),
  gp: z.coerce.number().min(0, "GP must be a positive number"),
  gol_kontrak: z.coerce.number().optional(),
  gol_tetap: z.coerce.number().optional(),
  beban_kerja: z.coerce.number().optional(),
  insentif: z.coerce.number().optional(),
  tunj_keahlian: z.coerce.number().optional(),
  tunj_jab: z.coerce.number().optional(),
  tot_gross: z.coerce.number().min(0, "Total gross must be a positive number"),
  bpjstku_yay: z.coerce.number().optional(),
  bpjskes_yay: z.coerce.number().optional(),
  pph_yay: z.coerce.number().optional(),
  tot_gross_yay: z.coerce.number().optional(),
  awol: z.boolean().optional(),
  jlh_awol: z.coerce.number().optional(),
  freq: z.boolean().optional(),
  minute: z.boolean().optional(),
  jlh_freq: z.coerce.number().optional(),
  jlh_minute: z.coerce.number().optional(),
  pot_abs: z.coerce.number().optional(),
  gaji_stlh_pot_abs: z.coerce.number().optional(),
  bpjstku: z.coerce.number().optional(),
  bpjskes: z.coerce.number().optional(),
  nett_stlh_bpjs: z.coerce.number().optional(),
  pph: z.coerce.number().optional(),
  pinj_lain: z.coerce.number().optional(),
  iuran_wajib: z.coerce.number().optional(),
  pinj_kop: z.coerce.number().optional(),
  piutang: z.coerce.number().optional(),
  pot_bank: z.coerce.number().optional(),
  tot_pot: z.coerce.number().optional(),
  gaji_netto: z.coerce.number().min(0, "Gaji netto must be a positive number"),
});

type FormValues = z.infer<typeof formSchema>;

interface StaffSalaryFormProps {
  initialData?: SalaryStaff;
  onSubmit: (data: FormValues) => void;
}

export function StaffSalaryForm({ initialData, onSubmit }: StaffSalaryFormProps) {
  const [isCalculating, setIsCalculating] = useState(false);

  // Initialize form with default values or initial data
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData ? {
      nama: initialData.nama,
      gp: initialData.gp,
      gol_kontrak: initialData.gol_kontrak || 0,
      gol_tetap: initialData.gol_tetap || 0,
      beban_kerja: initialData.beban_kerja || 0,
      insentif: initialData.insentif || 0,
      tunj_keahlian: initialData.tunj_keahlian || 0,
      tunj_jab: initialData.tunj_jab || 0,
      tot_gross: initialData.tot_gross,
      bpjstku_yay: initialData.bpjstku_yay || 0,
      bpjskes_yay: initialData.bpjskes_yay || 0,
      pph_yay: initialData.pph_yay || 0,
      tot_gross_yay: initialData.tot_gross_yay || 0,
      awol: initialData.awol || false,
      jlh_awol: initialData.jlh_awol || 0,
      freq: initialData.freq || false,
      minute: initialData.minute || false,
      jlh_freq: initialData.jlh_freq || 0,
      jlh_minute: initialData.jlh_minute || 0,
      pot_abs: initialData.pot_abs || 0,
      gaji_stlh_pot_abs: initialData.gaji_stlh_pot_abs || 0,
      bpjstku: initialData.bpjstku || 0,
      bpjskes: initialData.bpjskes || 0,
      nett_stlh_bpjs: initialData.nett_stlh_bpjs || 0,
      pph: initialData.pph || 0,
      pinj_lain: initialData.pinj_lain || 0,
      iuran_wajib: initialData.iuran_wajib || 0,
      pinj_kop: initialData.pinj_kop || 0,
      piutang: initialData.piutang || 0,
      pot_bank: initialData.pot_bank || 0,
      tot_pot: initialData.tot_pot || 0,
      gaji_netto: initialData.gaji_netto,
    } : {
      nama: "",
      gp: 0,
      gol_kontrak: 0,
      gol_tetap: 0,
      beban_kerja: 0,
      insentif: 0,
      tunj_keahlian: 0,
      tunj_jab: 0,
      tot_gross: 0,
      bpjstku_yay: 0,
      bpjskes_yay: 0,
      pph_yay: 0,
      tot_gross_yay: 0,
      awol: false,
      jlh_awol: 0,
      freq: false,
      minute: false,
      jlh_freq: 0,
      jlh_minute: 0,
      pot_abs: 0,
      gaji_stlh_pot_abs: 0,
      bpjstku: 0,
      bpjskes: 0,
      nett_stlh_bpjs: 0,
      pph: 0,
      pinj_lain: 0,
      iuran_wajib: 0,
      pinj_kop: 0,
      piutang: 0,
      pot_bank: 0,
      tot_pot: 0,
      gaji_netto: 0,
    },
  });

  // Calculate totals
  const calculateTotals = () => {
    setIsCalculating(true);
    
    try {
      const values = form.getValues();
      
      // Calculate tot_gross
      const totGross = (
        (values.gp || 0) +
        (values.gol_kontrak || 0) +
        (values.gol_tetap || 0) +
        (values.beban_kerja || 0) +
        (values.insentif || 0) +
        (values.tunj_keahlian || 0) +
        (values.tunj_jab || 0)
      );
      form.setValue("tot_gross", totGross);
      
      // Calculate tot_gross_yay
      const totGrossYay = (
        totGross +
        (values.bpjstku_yay || 0) +
        (values.bpjskes_yay || 0) +
        (values.pph_yay || 0)
      );
      form.setValue("tot_gross_yay", totGrossYay);
      
      // Calculate gaji_stlh_pot_abs
      const gajiStlhPotAbs = totGross - (values.pot_abs || 0);
      form.setValue("gaji_stlh_pot_abs", gajiStlhPotAbs);
      
      // Calculate nett_stlh_bpjs
      const nettStlhBpjs = gajiStlhPotAbs - (values.bpjstku || 0) - (values.bpjskes || 0);
      form.setValue("nett_stlh_bpjs", nettStlhBpjs);
      
      // Calculate tot_pot
      const totPot = (
        (values.bpjstku || 0) +
        (values.bpjskes || 0) +
        (values.pph || 0) +
        (values.pinj_lain || 0) +
        (values.iuran_wajib || 0) +
        (values.pinj_kop || 0) +
        (values.piutang || 0) +
        (values.pot_bank || 0) +
        (values.pot_abs || 0)
      );
      form.setValue("tot_pot", totPot);
      
      // Calculate gaji_netto
      const gajiNetto = totGross - totPot;
      form.setValue("gaji_netto", gajiNetto);
    } catch (error) {
      console.error("Error calculating totals:", error);
    } finally {
      setIsCalculating(false);
    }
  };

  // Handle form submission
  const handleSubmit = (data: FormValues) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 max-h-[70vh] overflow-y-auto p-1">
        <div className="grid grid-cols-2 gap-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="nama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nama</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gp"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GP</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gol_kontrak"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gol Kontrak</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gol_tetap"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gol Tetap</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="beban_kerja"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Beban Kerja</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="insentif"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Insentif</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tunj_keahlian"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tunjangan Keahlian</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tunj_jab"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tunjangan Jabatan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tot_gross"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Gross</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bpjstku_yay"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>BPJS TK Yayasan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bpjskes_yay"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>BPJS Kesehatan Yayasan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pph_yay"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>PPh Yayasan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tot_gross_yay"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Gross Yayasan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Deductions and Final Calculations */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="awol"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>AWOL</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="jlh_awol"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah AWOL</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="freq"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Freq</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="jlh_freq"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Freq</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="minute"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Minute</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="jlh_minute"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Minute</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="pot_abs"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Potongan Absensi</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gaji_stlh_pot_abs"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gaji Setelah Potongan Absensi</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bpjstku"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>BPJS TK</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bpjskes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>BPJS Kesehatan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nett_stlh_bpjs"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nett Setelah BPJS</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pph"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>PPh</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pinj_lain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pinjaman Lain</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="iuran_wajib"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Iuran Wajib</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pinj_kop"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pinjaman Koperasi</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="piutang"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Piutang</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pot_bank"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Potongan Bank</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tot_pot"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Potongan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gaji_netto"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gaji Netto</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-between">
          <Button 
            type="button" 
            variant="outline" 
            onClick={calculateTotals}
            disabled={isCalculating}
          >
            {isCalculating ? "Calculating..." : "Calculate Totals"}
          </Button>
          <Button type="submit">Submit</Button>
        </div>
      </form>
    </Form>
  );
}
