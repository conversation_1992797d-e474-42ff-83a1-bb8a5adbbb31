/**
 * API Route: GET /api/leave-management/[id]/get
 *
 * Deskripsi: Mengambil detail permintaan cuti berdasarkan ID
 * Penggunaan: Halaman detail permintaan cuti
 *
 * Path Parameters:
 * - id: ID permintaan cuti (number)
 *
 * Response:
 * - 200: Detail permintaan cuti
 * - 401: Tidak terautentikasi
 * - 404: Permintaan cuti tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from params
    const id = params.id;
    const leaveRequest = await prisma.leaveRequest.findUnique({
      where: { id: parseInt(id) },
      include: {
        employee: {
          include: {
            department: true,
            position: true
          }
        },
        leaveType: true,
        approvedBy: true
      }
    });

    if (!leaveRequest) {
      return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
    }

    // Log the leave request data for debugging
    console.log('Leave request data from database:', JSON.stringify({
      id: leaveRequest.id,
      attachmentUrl: leaveRequest.attachmentUrl,
      // Include other fields as needed
    }, null, 2));

    // Make sure the attachment URL is properly formatted
    let formattedLeaveRequest = { ...leaveRequest };

    // If attachmentUrl exists but doesn't start with http or /, add / prefix
    if (formattedLeaveRequest.attachmentUrl &&
        !formattedLeaveRequest.attachmentUrl.startsWith('http') &&
        !formattedLeaveRequest.attachmentUrl.startsWith('/')) {
      formattedLeaveRequest.attachmentUrl = `/${formattedLeaveRequest.attachmentUrl}`;
    }

    // Log whether an attachment URL was found
    if (!formattedLeaveRequest.attachmentUrl) {
      console.log('No attachment URL found for this leave request');
    } else {
      console.log('Attachment URL found:', formattedLeaveRequest.attachmentUrl);
    }

    return NextResponse.json(formattedLeaveRequest);
  } catch (error) {
    console.error('Error fetching leave request:', error);
    return NextResponse.json(
      { error: 'Failed to fetch leave request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
