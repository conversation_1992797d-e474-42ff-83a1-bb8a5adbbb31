/**
 * API Route: GET /api/positions/get
 * 
 * Deskripsi: Mengambil daftar posisi/jabatan
 * Penggunaan: Halaman daftar posisi, dropdown pilihan posisi
 * 
 * Response:
 * - 200: Daftar posisi
 * - 401: Tidak terautentikasi
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/db';



export async function GET() {
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const positions = await prisma.position.findMany({
      include: {
        department: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        title: 'asc'
      }
    });

    return NextResponse.json(positions);
  } catch (error) {
    console.error('Error fetching positions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
