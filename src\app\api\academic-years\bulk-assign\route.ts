/**
 * API Route: POST /api/academic-years/bulk-assign
 *
 * Deskripsi: Endpoint untuk mengatur academic year untuk banyak karyawan sekaligus
 * Penggunaan: Halaman pengaturan academic year
 *
 * Body:
 * - academicYearId: ID tahun akademik (number)
 * - employeeIds: Array ID karyawan (number[])
 * - filter: Filter untuk memilih karyawan (object, opsional)
 *   - departmentId: ID departemen (number, opsional)
 *   - status: Status karyawan (string, opsional)
 *   - isDeleted: Status deleted karyawan (boolean, opsional)
 *
 * Response:
 * - 200: Sukses
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { logger } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only ADMIN can access this resource' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { academicYearId, employeeIds, filter } = body;

    // Validasi data
    if (!academicYearId && academicYearId !== null) {
      return NextResponse.json(
        { error: 'Academic year ID is required' },
        { status: 400 }
      );
    }

    // Jika academicYearId adalah string "none", set ke null
    const finalAcademicYearId = academicYearId === "none" ? null : 
                               academicYearId ? parseInt(academicYearId) : null;

    // Cek apakah tahun akademik ada jika ID tidak null
    if (finalAcademicYearId !== null) {
      const academicYear = await prisma.academicYear.findUnique({
        where: { id: finalAcademicYearId }
      });

      if (!academicYear) {
        return NextResponse.json(
          { error: 'Academic year not found' },
          { status: 404 }
        );
      }
    }

    // Jika employeeIds disediakan, gunakan itu
    if (employeeIds && Array.isArray(employeeIds) && employeeIds.length > 0) {
      // Validasi bahwa semua ID adalah angka
      const validIds = employeeIds.every(id => !isNaN(parseInt(id.toString())));
      if (!validIds) {
        return NextResponse.json(
          { error: 'All employee IDs must be numbers' },
          { status: 400 }
        );
      }

      // Update karyawan berdasarkan ID
      const updateResult = await prisma.employee.updateMany({
        where: {
          id: {
            in: employeeIds.map(id => parseInt(id.toString()))
          }
        },
        data: {
          academicYearId: finalAcademicYearId
        }
      });

      return NextResponse.json({
        message: `Updated ${updateResult.count} employees with academic year ID: ${finalAcademicYearId}`,
        count: updateResult.count
      });
    }
    // Jika filter disediakan, gunakan itu
    else if (filter) {
      // Buat where clause berdasarkan filter
      const whereClause: any = {};

      // Filter berdasarkan departemen
      if (filter.departmentId) {
        whereClause.departmentId = parseInt(filter.departmentId.toString());
      }

      // Filter berdasarkan status
      if (filter.status) {
        whereClause.status = filter.status;
      }

      // Filter berdasarkan isDeleted
      if (filter.isDeleted !== undefined) {
        whereClause.isDeleted = filter.isDeleted;
      }

      // Exclude admin web and external members
      whereClause.NOT = {
        OR: [
          { employeeId: { startsWith: "EMP" } },
          { employeeId: { startsWith: "EXT" } }
        ]
      };

      // Update karyawan berdasarkan filter
      const updateResult = await prisma.employee.updateMany({
        where: whereClause,
        data: {
          academicYearId: finalAcademicYearId
        }
      });

      return NextResponse.json({
        message: `Updated ${updateResult.count} employees with academic year ID: ${finalAcademicYearId}`,
        count: updateResult.count
      });
    } else {
      return NextResponse.json(
        { error: 'Either employeeIds or filter must be provided' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    logger.error('API - Error bulk assigning academic year:', error);
    return NextResponse.json(
      { error: 'Failed to bulk assign academic year', details: error.message },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
