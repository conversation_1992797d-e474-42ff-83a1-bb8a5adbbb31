import { useState, useEffect } from "react";
import { KoperasiMember } from "@/lib/types";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, MoreHorizontal, Eye, Pencil, Trash2, Upload, Download, ChevronLeft, ChevronRight, Search } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import { Badge } from "@/components/ui/badge";
import { MembershipForm } from "@/components/forms/membership-form";
import { format, parseISO } from "date-fns";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface MembershipsTabProps {
  members: KoperasiMember[];
  userRole?: string;
  onMemberSubmit: (data: any) => Promise<void>;
  onMemberImport: (file: File) => Promise<void>;
  onMemberEdit: (member: KoperasiMember) => void;
  onMemberDelete: (member: KoperasiMember) => void;
  onMemberView: (member: KoperasiMember) => void;
  onRefresh: () => Promise<void>;
}

export function MembershipsTab({
  members,
  userRole,
  onMemberSubmit,
  onMemberImport,
  onMemberEdit,
  onMemberDelete,
  onMemberView,
  onRefresh
}: MembershipsTabProps) {
  const [isAddMemberOpen, setIsAddMemberOpen] = useState(false);
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [isViewMemberOpen, setIsViewMemberOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<KoperasiMember | null>(null);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importLoading, setImportLoading] = useState(false);
  const [membersPage, setMembersPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredMembers, setFilteredMembers] = useState<KoperasiMember[]>([]);
  const itemsPerPage = 18;

  // Filter members based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredMembers(members);
    } else {
      const filtered = members.filter(member =>
        member.employee_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredMembers(filtered);
    }
    setMembersPage(1); // Reset to first page when search changes
  }, [searchTerm, members]);

  // Helper function untuk pagination
  const paginateData = (data: any[], page: number, itemsPerPage: number) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  // Helper function untuk format tanggal
  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return "-";
    try {
      const date = typeof dateString === "string" ? parseISO(dateString) : dateString;
      return format(date, "dd MMM yyyy");
    } catch (error) {
      console.error("Error formatting date:", error);
      return "-";
    }
  };

  const handleMemberSubmit = async (data: any) => {
    try {
      console.log('Submitting member data:', data);
      await onMemberSubmit(data);
      setIsAddMemberOpen(false);
      // Refresh data after successful submission
      await onRefresh();
    } catch (error) {
      console.error('Error submitting member data:', error);
      // Keep the dialog open so the user can correct the input
      // The error message will be shown by the parent component
    }
  };

  const handleViewMember = (member: KoperasiMember) => {
    setSelectedMember(member);
    setIsViewMemberOpen(true);
    onMemberView(member);
  };

  const handleImport = async () => {
    if (!importFile) return;

    setImportLoading(true);
    try {
      await onMemberImport(importFile);
      setIsImportOpen(false);
      setImportFile(null);
    } catch (error) {
      console.error("Error importing members:", error);
    } finally {
      setImportLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      // Open the export URL in a new window/tab
      window.open('/api/koperasi/members/export', '_blank');
    } catch (error) {
      console.error("Error exporting members:", error);
      alert("Failed to export members. Please try again.");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Memberships</h2>
        {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  Import
                </Button>
              </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Import Members</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="file">Excel File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        setImportFile(e.target.files[0]);
                      }
                    }}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsImportOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={importLoading || !importFile}
                  >
                    {importLoading ? "Memproses..." : "Import"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          <Dialog open={isAddMemberOpen} onOpenChange={setIsAddMemberOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Member
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Member</DialogTitle>
              </DialogHeader>
              <MembershipForm
                onSubmit={handleMemberSubmit}
                onCancel={() => setIsAddMemberOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
        )}
      </div>

      {/* Search bar - only for ADMIN and OPERATOR_KOP */}
      {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Cari berdasarkan nama..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {searchTerm && (
            <Button variant="ghost" onClick={() => setSearchTerm("")}>Clear</Button>
          )}
        </div>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableHead>Member ID</TableHead>}
              <TableHead>Employee Name</TableHead>
              <TableHead className="text-right">Actions</TableHead>
              {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableHead>Join Date</TableHead>}
              {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableHead>One Time Contribution</TableHead>}
              {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableHead>Monthly Contribution</TableHead>}
              {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableHead>Optional Contribution</TableHead>}
              <TableHead>Total Savings</TableHead>
              {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableHead>Status</TableHead>}
              {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableHead>Notes</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMembers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={(userRole === "ADMIN" || userRole === "OPERATOR_KOP") ? 10 : 3} className="text-center">
                  No members data available
                </TableCell>
              </TableRow>
            ) : (
              paginateData(filteredMembers, membersPage, itemsPerPage).map((member) => (
                <TableRow key={member.id}>
                  {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableCell>M{String(member.id).padStart(3, "0")}</TableCell>}
                  <TableCell>{member.employee_name}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewMember(member)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
                          <>
                            <DropdownMenuItem onClick={() => onMemberEdit(member)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => onMemberDelete(member)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                  {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && <TableCell>{formatDate(member.join_date)}</TableCell>}
                  {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
                    <TableCell>
                      {new Intl.NumberFormat("id-ID", {
                        style: "currency",
                        currency: "IDR",
                      }).format(member.one_time_contribution)}
                    </TableCell>
                  )}
                  {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
                    <TableCell>
                      {new Intl.NumberFormat("id-ID", {
                        style: "currency",
                        currency: "IDR",
                      }).format(member.monthly_contribution)}
                    </TableCell>
                  )}
                  {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
                    <TableCell>
                      {new Intl.NumberFormat("id-ID", {
                        style: "currency",
                        currency: "IDR",
                      }).format(member.optional_contribution)}
                    </TableCell>
                  )}
                  <TableCell>
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(member.total_savings)}
                  </TableCell>
                  {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
                    <TableCell>
                      <Badge
                        variant={member.status === "active" ? "success" : "secondary"}
                      >
                        {member.status}
                      </Badge>
                    </TableCell>
                  )}
                  {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
                    <TableCell>
                      {member.notes || "-"}
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <div className="flex items-center justify-center py-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setMembersPage(prev => Math.max(prev - 1, 1))}
                  disabled={membersPage <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </PaginationItem>
              {(() => {
                const totalPages = Math.ceil(filteredMembers.length / itemsPerPage) || 1;
                const maxVisiblePages = 5; // Jumlah halaman yang ditampilkan

                let startPage = Math.max(1, membersPage - Math.floor(maxVisiblePages / 2));
                let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                // Adjust startPage if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                const pages = [];

                // Add first page and ellipsis if needed
                if (startPage > 1) {
                  pages.push(
                    <PaginationItem key="first">
                      <Button
                        variant={membersPage === 1 ? "default" : "outline"}
                        size="icon"
                        onClick={() => setMembersPage(1)}
                      >
                        1
                      </Button>
                    </PaginationItem>
                  );

                  if (startPage > 2) {
                    pages.push(
                      <PaginationItem key="ellipsis-start">
                        <span className="px-2">...</span>
                      </PaginationItem>
                    );
                  }
                }

                // Add visible pages
                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <PaginationItem key={i}>
                      <Button
                        variant={membersPage === i ? "default" : "outline"}
                        size="icon"
                        onClick={() => setMembersPage(i)}
                      >
                        {i}
                      </Button>
                    </PaginationItem>
                  );
                }

                // Add last page and ellipsis if needed
                if (endPage < totalPages) {
                  if (endPage < totalPages - 1) {
                    pages.push(
                      <PaginationItem key="ellipsis-end">
                        <span className="px-2">...</span>
                      </PaginationItem>
                    );
                  }

                  pages.push(
                    <PaginationItem key="last">
                      <Button
                        variant={membersPage === totalPages ? "default" : "outline"}
                        size="icon"
                        onClick={() => setMembersPage(totalPages)}
                      >
                        {totalPages}
                      </Button>
                    </PaginationItem>
                  );
                }

                return pages;
              })()}
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setMembersPage(prev => Math.min(prev + 1, Math.ceil(filteredMembers.length / itemsPerPage) || 1))}
                  disabled={membersPage >= (Math.ceil(filteredMembers.length / itemsPerPage) || 1)}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>

      {/* View Member Details Dialog */}
      <Dialog open={isViewMemberOpen} onOpenChange={setIsViewMemberOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Member Details</DialogTitle>
          </DialogHeader>
          {selectedMember && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Member ID</Label>
                  <div className="font-medium">{selectedMember.id}</div>
                </div>
                <div>
                  <Label>Employee ID</Label>
                  <div className="font-medium">{selectedMember.employee_id}</div>
                </div>
                <div>
                  <Label>Join Date</Label>
                  <div className="font-medium">{formatDate(selectedMember.join_date)}</div>
                </div>
                <div>
                  <Label>Status</Label>
                  <div className="font-medium">
                    <Badge
                      variant={selectedMember.status === "active" ? "success" : "secondary"}
                    >
                      {selectedMember.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label>Monthly Contribution</Label>
                  <div className="font-medium">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.monthly_contribution)}
                  </div>
                </div>
                <div>
                  <Label>One Time Contribution</Label>
                  <div className="font-medium">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.one_time_contribution || 0)}
                  </div>
                </div>
                <div>
                  <Label>Optional Contribution</Label>
                  <div className="font-medium">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.optional_contribution || 0)}
                  </div>
                </div>
                <div>
                  <Label>Total Savings</Label>
                  <div className="font-medium">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(selectedMember.total_savings)}
                  </div>
                </div>
              </div>
              <div>
                <Label>Notes</Label>
                <div className="font-medium whitespace-pre-wrap">{selectedMember.notes || "-"}</div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
