import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, MoreHorizontal, Eye, Pencil, Trash2, Filter, Download, Check, X, Search, Calendar } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


interface LateRequestsTabProps {
  filteredLateRequests: any[];
  userRole?: string;
  user?: any;
  loading: boolean;
  onView: (id: number) => void;
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onApprove: (id: number) => void;
  onReject: (id: number) => void;
  onAddNew: () => void;
}

export function LateRequestsTab({
  filteredLateRequests,
  userRole,
  user,
  loading,
  onView,
  onEdit,
  onDelete,
  onApprove,
  onReject,
  onAddNew
}: LateRequestsTabProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(18);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchedRequests, setSearchedRequests] = useState<any[]>([]);

  // State untuk filter
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterOptions, setFilterOptions] = useState({
    status: "all",
    department: "all",
    startDate: "",
    endDate: "",
  });

  // Format data untuk menambahkan departemen
  const formattedLateRequests = useMemo(() => {
    return filteredLateRequests.map(request => {
      // Tambahkan properti department ke setiap request
      return {
        ...request,
        department: request.employee?.department?.name || 'Unknown'
      };
    });
  }, [filteredLateRequests]);

  // Daftar departemen unik dari data yang sudah diformat
  const departments = useMemo(() => {
    const deptSet = new Set<string>();

    // Log untuk debugging
    console.log('Extracting departments from formatted late requests:', formattedLateRequests.length);

    formattedLateRequests.forEach((request, index) => {
      if (request.department && request.department !== 'Unknown') {
        deptSet.add(request.department);
        console.log(`Request ${index}: Added department:`, request.department);
      }
    });

    console.log('Final departments list:', Array.from(deptSet));
    return Array.from(deptSet).sort();
  }, [formattedLateRequests]);

  // Filter data based on search term and filter options
  useEffect(() => {
    let filtered = formattedLateRequests;

    // Apply search filter
    if (searchTerm.trim() !== "") {
      filtered = filtered.filter(request => {
        const employeeName = request.employee ?
          `${request.employee.firstName} ${request.employee.lastName}`.toLowerCase() :
          '';
        return employeeName.includes(searchTerm.toLowerCase());
      });
    }

    // Apply status filter
    if (filterOptions.status && filterOptions.status !== "all") {
      filtered = filtered.filter(request =>
        request.status.toLowerCase() === filterOptions.status.toLowerCase()
      );
    }

    // Apply department filter
    if (filterOptions.department && filterOptions.department !== "all") {
      filtered = filtered.filter(request =>
        request.department === filterOptions.department
      );
    }

    // Apply date range filter
    if (filterOptions.startDate && filterOptions.endDate) {
      const startDate = new Date(filterOptions.startDate);
      const endDate = new Date(filterOptions.endDate);

      filtered = filtered.filter(request => {
        if (!request.lateDate) return false;

        const requestDate = new Date(request.lateDate);
        return requestDate >= startDate && requestDate <= endDate;
      });
    } else if (filterOptions.startDate) {
      const startDate = new Date(filterOptions.startDate);

      filtered = filtered.filter(request => {
        if (!request.lateDate) return false;

        const requestDate = new Date(request.lateDate);
        return requestDate >= startDate;
      });
    } else if (filterOptions.endDate) {
      const endDate = new Date(filterOptions.endDate);

      filtered = filtered.filter(request => {
        if (!request.lateDate) return false;

        const requestDate = new Date(request.lateDate);
        return requestDate <= endDate;
      });
    }

    setSearchedRequests(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchTerm, formattedLateRequests, filterOptions]);

  // Get current items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = searchedRequests.slice(indexOfFirstItem, indexOfLastItem);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  return (
    <div className="bg-card rounded-lg shadow-sm p-6">
      <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
        <div className="p-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 border-b border-border">
          <div>
            <h2 className="text-lg font-semibold">Late Requests</h2>
            {userRole === 'SUPERVISOR' && (
              <p className="text-xs text-muted-foreground">Showing late requests from your department (employees you supervise)</p>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            <Button onClick={onAddNew} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
            {userRole === "ADMIN" && (
              <>
                <Button variant="outline" size="sm" onClick={() => setIsFilterOpen(true)}>
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </>
            )}

            {/* Filter Dialog */}
            <Dialog open={isFilterOpen} onOpenChange={setIsFilterOpen}>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Filter Late Requests</DialogTitle>
                  <DialogDescription>
                    Apply filters to narrow down the late requests.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="status" className="text-right text-sm font-medium">
                      Status
                    </label>
                    <Select
                      value={filterOptions.status}
                      onValueChange={(value) => setFilterOptions({...filterOptions, status: value})}
                    >
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        <SelectItem value="approved">Approved</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="rejected">Rejected</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="department" className="text-right text-sm font-medium">
                      Department
                    </label>
                    <Select
                      value={filterOptions.department}
                      onValueChange={(value) => setFilterOptions({...filterOptions, department: value})}
                    >
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        {departments.length > 0 ? (
                          departments.map((dept) => (
                            <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled>No departments found</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="startDate" className="text-right text-sm font-medium">
                      Start Date
                    </label>
                    <div className="col-span-3">
                      <Input
                        id="startDate"
                        type="date"
                        value={filterOptions.startDate}
                        onChange={(e) => setFilterOptions({...filterOptions, startDate: e.target.value})}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="endDate" className="text-right text-sm font-medium">
                      End Date
                    </label>
                    <div className="col-span-3">
                      <Input
                        id="endDate"
                        type="date"
                        value={filterOptions.endDate}
                        onChange={(e) => setFilterOptions({...filterOptions, endDate: e.target.value})}
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setFilterOptions({
                        status: "all",
                        department: "all",
                        startDate: "",
                        endDate: "",
                      });
                    }}
                  >
                    Reset
                  </Button>
                  <Button type="submit" onClick={() => setIsFilterOpen(false)}>Apply Filters</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search bar - only for ADMIN, SUPERVISOR, and HEAD */}
        {(userRole === "ADMIN" || userRole === "SUPERVISOR" || userRole === "HEAD") && (
          <div className="p-4 border-b border-border">
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Cari berdasarkan nama..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              {searchTerm && (
                <Button variant="ghost" size="sm" onClick={() => setSearchTerm("")}>Clear</Button>
              )}
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead className="text-right px-2">Actions</TableHead>
                <TableHead>Type</TableHead>
                {userRole === "ADMIN" && (
                  <TableHead>Date</TableHead>
                )}
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={userRole === "ADMIN" ? 5 : 4} className="text-center py-4">
                    <div className="flex justify-center items-center space-x-2">
                      <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                      <span>Loading late requests...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : searchedRequests.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={userRole === "ADMIN" ? 5 : 4} className="text-center py-4">
                    No late requests found
                  </TableCell>
                </TableRow>
              ) : (
                currentItems.map((late: any) => (
                  <TableRow key={late.id}>
                    <TableCell>
                      {late.employee ?
                        `${late.employee.firstName} ${late.employee.lastName}` :
                        'Unknown'}
                    </TableCell>
                    <TableCell className="text-right px-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="min-w-[160px]">
                          <DropdownMenuItem onClick={() => onView(late.id)} className="font-medium">
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                            <span
                              className={`ml-auto px-2 py-0.5 rounded-full text-xs font-semibold ${
                                late.status === "approved"
                                  ? "bg-green-100 text-green-800"
                                  : late.status === "pending"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {late.status.charAt(0).toUpperCase() + late.status.slice(1)}
                            </span>
                          </DropdownMenuItem>

                          {/* ADMIN: Dapat melakukan semua aksi */}
                          {userRole === "ADMIN" && (
                            <>
                              <DropdownMenuItem onClick={() => onApprove(late.id)}>
                                <Check className="mr-2 h-4 w-4" />
                                Approve
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onReject(late.id)}>
                                <X className="mr-2 h-4 w-4" />
                                Reject
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onEdit(late.id)}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onDelete(late.id)} className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </>
                          )}

                          {/* SUPERVISOR & HEAD: Hanya dapat approve/reject permintaan bawahannya, tidak bisa approve/reject diri sendiri */}
                          {(userRole === "SUPERVISOR" || userRole === "HEAD") && late.status === "pending" &&
                           late.employee &&
                           `${late.employee.firstName} ${late.employee.lastName || ''}` !== user?.name && (
                            <>
                              <DropdownMenuItem onClick={() => onApprove(late.id)}>
                                <Check className="mr-2 h-4 w-4" />
                                Approve
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onReject(late.id)}>
                                <X className="mr-2 h-4 w-4" />
                                Reject
                              </DropdownMenuItem>
                            </>
                          )}

                          {/* SUPERVISOR, HEAD & EMPLOYEE: Hanya dapat edit/delete permintaan mereka sendiri dengan syarat status pending */}
                          {(userRole === "SUPERVISOR" || userRole === "HEAD" || userRole === "EMPLOYEE") &&
                           late.status === "pending" &&
                           late.employee &&
                           `${late.employee.firstName} ${late.employee.lastName || ''}` === user?.name && (
                            <>
                              <DropdownMenuItem onClick={() => onEdit(late.id)}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onDelete(late.id)} className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                    <TableCell>{late.lateType || '-'}</TableCell>
                    {userRole === "ADMIN" && (
                      <TableCell>{late.lateDate ? new Date(late.lateDate).toLocaleDateString() : '-'}</TableCell>
                    )}
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          late.status === "approved"
                            ? "bg-green-100 text-green-800"
                            : late.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {late.status.charAt(0).toUpperCase() + late.status.slice(1)}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        {/* Selalu tampilkan pagination */}
        {searchedRequests.length > 0 && (
          <div className="p-4 border-t border-border">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
                {/* Tampilkan maksimal 5 halaman dengan ellipsis */}
                {Array.from({ length: Math.ceil(searchedRequests.length / itemsPerPage) }).map((_, index) => {
                  // Selalu tampilkan halaman pertama, halaman terakhir, halaman saat ini, dan halaman di sekitar halaman saat ini
                  const totalPages = Math.ceil(searchedRequests.length / itemsPerPage);
                  const showPageNumbers = [];

                  // Selalu tampilkan halaman pertama
                  showPageNumbers.push(1);

                  // Tampilkan halaman di sekitar halaman saat ini
                  for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
                    showPageNumbers.push(i);
                  }

                  // Selalu tampilkan halaman terakhir jika ada lebih dari 1 halaman
                  if (totalPages > 1) {
                    showPageNumbers.push(totalPages);
                  }

                  // Hanya tampilkan halaman yang ada dalam showPageNumbers
                  const pageNumber = index + 1;
                  if (showPageNumbers.includes(pageNumber)) {
                    return (
                      <PaginationItem key={index}>
                        <PaginationLink
                          onClick={() => paginate(pageNumber)}
                          isActive={currentPage === pageNumber}
                        >
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  }

                  // Tambahkan ellipsis setelah halaman pertama jika halaman saat ini > 3
                  if (pageNumber === 2 && currentPage > 3) {
                    return <PaginationItem key="ellipsis-start">...</PaginationItem>;
                  }

                  // Tambahkan ellipsis sebelum halaman terakhir jika halaman saat ini < totalPages - 2
                  if (pageNumber === totalPages - 1 && currentPage < totalPages - 2) {
                    return <PaginationItem key="ellipsis-end">...</PaginationItem>;
                  }

                  return null;
                })}
                <PaginationItem>
                  <PaginationNext
                    onClick={() =>
                      paginate(
                        currentPage < Math.ceil(searchedRequests.length / itemsPerPage)
                          ? currentPage + 1
                          : currentPage
                      )
                    }
                    className={
                      currentPage === Math.ceil(searchedRequests.length / itemsPerPage)
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </div>
  );
}
