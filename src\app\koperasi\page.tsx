"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth";
import type { KoperasiOverview, KoperasiSaving, KoperasiMember, KoperasiLoan } from "@/lib/types";
import { logger } from "@/lib/logger";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import ProtectedRoute from "@/components/ProtectedRoute";
import Navbar from "@/components/layout/Navbar";
import { OverviewTab, SavingsTab, LoansTab, MembershipsTab } from "@/components/koperasi";
import { EditMemberForm } from "@/components/koperasi/EditMemberForm";

export default function KoperasiPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [overview, setOverview] = useState<KoperasiOverview>({
    totalMembers: 0,
    totalActiveMembers: 0,
    totalInactiveMembers: 0,
    memberActivePercentage: 0,
    totalSavings: 0,
    totalOneTimeContributions: 0,
    totalMonthlyContributions: 0,
    totalOptionalContributions: 0,
    averageSavingsPerMember: 0,
    activeLoanCount: 0,
    completedLoanCount: 0,
    totalLoanAmount: 0,
    completedLoanAmount: 0,
    totalLoanCount: 0,
    recentTransactions: [],
    monthlyContributions: 0,
    membershipGrowth: 0,
    loanApprovalRate: 0,
  });
  const [savings, setSavings] = useState<KoperasiSaving[]>([]);
  const [members, setMembers] = useState<KoperasiMember[]>([]);
  const [loans, setLoans] = useState<KoperasiLoan[]>([]);

  useEffect(() => {
    // Fetch user data first, then fetch other data
    const fetchUserAndData = async () => {
      try {
        // Fetch latest user data from /api/auth/me
        const userResponse = await fetch('/api/auth/me');
        if (userResponse.ok) {
          const userData = await userResponse.json();
          if (userData.success && userData.user) {
            // Update user in auth context
            logger.debug('User data updated from /api/auth/me');

            // Set default active tab based on user role
            if (userData.user.role !== "ADMIN" && userData.user.role !== "OPERATOR_KOP") {
              setActiveTab("memberships");
            }

            // Now fetch other data
            fetchData(userData.user);
          } else {
            // If we couldn't get updated user data, use the current user
            fetchData();

            // Set default active tab based on user role
            if (user?.role !== "ADMIN" && user?.role !== "OPERATOR_KOP") {
              setActiveTab("memberships");
            }
          }
        } else {
          // If API call failed, use the current user
          fetchData();

          // Set default active tab based on user role
          if (user?.role !== "ADMIN" && user?.role !== "OPERATOR_KOP") {
            setActiveTab("memberships");
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        // If there was an error, use the current user
        fetchData();

        // Set default active tab based on user role
        if (user?.role !== "ADMIN" && user?.role !== "OPERATOR_KOP") {
          setActiveTab("memberships");
        }
      }
    };

    fetchUserAndData();
  }, []);

  const fetchData = async (currentUser = user) => {
    try {
      // Log minimal user info for debugging
      logger.debug(`User authenticated for koperasi page with role: ${currentUser?.role}`);
      // Fetch overview data
      const overviewResponse = await fetch("/api/koperasi/overview");
      if (overviewResponse.ok) {
        const overviewData = await overviewResponse.json();
        setOverview(overviewData);
      }

      // Fetch savings data
      const savingsResponse = await fetch("/api/koperasi/savings");
      if (savingsResponse.ok) {
        const savingsData = await savingsResponse.json();
        setSavings(savingsData);
      }

      // Fetch members data
      // Jika user bukan ADMIN atau OPERATOR_KOP, hanya tampilkan data dirinya sendiri
      let membersUrl = "/api/koperasi/members";

      if (currentUser?.role !== "ADMIN" && currentUser?.role !== "OPERATOR_KOP") {
        logger.debug('Filtering members for non-admin user');
        // Gunakan employeeId dari user untuk filter
        if (currentUser?.employeeId) {
          membersUrl = `/api/koperasi/members?employeeId=${currentUser.employeeId}`;
          logger.debug('Filtering by employeeId');
        } else {
          logger.debug('User does not have employeeId');
        }
      }

      logger.debug('Fetching members data');

      const membersResponse = await fetch(membersUrl);
      if (membersResponse.ok) {
        const membersData = await membersResponse.json();
        logger.debug('Members data fetched successfully');
        setMembers(membersData);
      }

      // Fetch loans data
      const loansResponse = await fetch("/api/koperasi/loans");
      if (loansResponse.ok) {
        const loansData = await loansResponse.json();
        setLoans(loansData);
      }
    } catch (error) {
      logger.error("Error fetching koperasi data");
    }
  };

  const handleSavingsSubmit = async (data: any) => {
    try {
      logger.debug('Submitting new savings data');
      const response = await fetch("/api/koperasi/savings/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        logger.error('Failed to create savings transaction');
        throw new Error(responseData.error || "Failed to create savings transaction");
      }

      logger.debug('Savings transaction created successfully');
      await fetchData();
    } catch (error) {
      logger.error("Error creating savings transaction");
      alert(error instanceof Error ? error.message : 'Failed to create savings transaction');
    }
  };

  const handleLoanSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/koperasi/loans/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to create loan application");
      }

      await fetchData();
    } catch (error) {
      logger.error("Error creating loan application");
    }
  };

  const handleMemberSubmit = async (data: any) => {
    try {
      logger.debug('Submitting new member data');
      console.log('Member data being submitted:', data);

      // Validate required fields based on member type
      if (data.memberType === 'employee' && !data.employeeId) {
        throw new Error('Employee ID is required for employee members');
      }

      if (data.memberType === 'external' && (!data.firstName || !data.lastName)) {
        throw new Error('First name and last name are required for external members');
      }

      if (!data.monthlyContribution) {
        throw new Error('Monthly contribution is required');
      }

      // Format the data for the API
      const formattedData = {
        ...data,
        // Convert string numbers to actual numbers
        monthlyContribution: data.monthlyContribution ? parseFloat(data.monthlyContribution) : 0,
        oneTimeContribution: data.oneTimeContribution ? parseFloat(data.oneTimeContribution) : 0,
        optionalContribution: data.optionalContribution ? parseFloat(data.optionalContribution) : 0,
      };

      // Pilih endpoint berdasarkan tipe anggota
      const endpoint = data.memberType === 'external'
        ? "/api/koperasi/members/external"
        : "/api/koperasi/members/create";

      console.log('Using endpoint:', endpoint);
      console.log('Formatted data being sent:', formattedData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formattedData),
      });

      // Get the response text first to ensure it's valid JSON
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing response JSON:', parseError);
        throw new Error(`Invalid response from server: ${responseText}`);
      }

      console.log('Response status:', response.status);
      console.log('Response data:', responseData);

      if (!response.ok) {
        logger.error('API error response when creating member');
        console.error('API error response when creating member:', responseData);

        // Provide more specific error messages based on status code and error message
        if (response.status === 404) {
          if (responseData.error.includes('Employee not found')) {
            throw new Error(`Employee with ID "${data.employeeId}" not found. Please check the employee ID and try again.`);
          } else if (responseData.error.includes('Employee ID not properly resolved')) {
            throw new Error(`Could not properly resolve employee with ID "${data.employeeId}". Please check the employee ID and try again.`);
          } else {
            throw new Error(responseData.error || "Resource not found");
          }
        } else if (response.status === 400) {
          if (responseData.error.includes('already a koperasi member')) {
            throw new Error(`Employee with ID "${data.employeeId}" is already a koperasi member.`);
          } else {
            throw new Error(responseData.error || "Invalid request");
          }
        } else {
          // For other errors, use the server's error message or a generic one
          throw new Error(responseData.error || "Failed to create member");
        }
      }

      logger.debug('Member created successfully');
      await fetchData();
      return responseData;
    } catch (error) {
      logger.error("Error creating member");
      console.error("Error creating member:", error);
      alert(error instanceof Error ? error.message : 'Failed to create member');
      throw error;
    }
  };

  const handleMemberImport = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/koperasi/members/import", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to import members");
      }

      await fetchData();
    } catch (error) {
      logger.error("Error importing members");
    }
  };

  const [isEditMemberOpen, setIsEditMemberOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<KoperasiMember | null>(null);

  const handleMemberEdit = (member: KoperasiMember) => {
    setSelectedMember(member);
    setIsEditMemberOpen(true);
  };

  const handleEditMemberSubmit = async (data: any) => {
    try {
      if (!selectedMember) return;

      console.log("Updating member with ID:", selectedMember.id);
      console.log("Data to be sent:", {
        joinDate: data.joinDate,
        monthlyContribution: data.monthlyContribution,
        oneTimeContribution: data.oneTimeContribution,
        optionalContribution: data.optionalContribution,
        status: selectedMember.status,
        notes: selectedMember.notes
      });

      // Perbarui member - menggunakan endpoint yang benar
      const response = await fetch(`/api/koperasi/members/${selectedMember.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          joinDate: data.joinDate instanceof Date ? data.joinDate.toISOString() : data.joinDate,
          monthlyContribution: data.monthlyContribution,
          oneTimeContribution: data.oneTimeContribution,
          optionalContribution: data.optionalContribution,
          status: selectedMember.status,
          notes: selectedMember.notes
        }),
      });

      const responseData = await response.json();
      console.log("API Response:", responseData);

      if (!response.ok) {
        throw new Error(responseData.error || "Failed to update member");
      }

      alert("Member updated successfully");
      setIsEditMemberOpen(false);
      await fetchData();
    } catch (error) {
      logger.error("Error updating member:", error);
      alert("Failed to update member. Please try again.");
    }
  };

  const handleMemberDelete = async (member: KoperasiMember) => {
    try {
      if (!confirm(`Apakah Anda yakin ingin menonaktifkan anggota ${member.id}? Semua saldo (kontribusi dan total simpanan) akan diatur menjadi 0.`)) {
        return;
      }

      // Format tanggal untuk catatan penonaktifan
      const today = new Date();
      const day = String(today.getDate()).padStart(2, '0');
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const year = today.getFullYear();
      const deactivationNote = `Non aktif pada ${day}/${month}/${year}`;

      // Perbarui status member menjadi inactive, atur semua saldo menjadi 0, dan tambahkan catatan
      const response = await fetch(`/api/koperasi/members/${member.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          monthlyContribution: 0, // Atur menjadi 0 saat dinonaktifkan
          oneTimeContribution: 0, // Atur menjadi 0 saat dinonaktifkan
          optionalContribution: 0, // Atur menjadi 0 saat dinonaktifkan
          totalSavings: 0, // Atur menjadi 0 saat dinonaktifkan
          joinDate: member.join_date,
          status: "inactive",
          notes: deactivationNote
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to deactivate member");
      }

      alert(`Anggota berhasil dinonaktifkan dengan catatan: ${deactivationNote}. Semua saldo telah diatur menjadi 0.`);
      logger.debug('Member deactivated, refreshing data...');
      await fetchData();
    } catch (error) {
      logger.error("Error deactivating member");
      alert("Gagal menonaktifkan anggota. Silakan coba lagi.");
    }
  };

  const handleMemberView = (member: KoperasiMember) => {
    logger.debug(`Viewing member details for ${member.id}`);
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          <div className="flex flex-col space-y-8">
            <div className="flex flex-col space-y-2">
              <h1 className="text-3xl font-bold tracking-tight">Koperasi</h1>
              <p className="text-muted-foreground">
                Manage cooperative savings, loans, and memberships.
              </p>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                {(user?.role === "ADMIN" || user?.role === "OPERATOR_KOP") && (
                  <>
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="savings">Transaction</TabsTrigger>
                    <TabsTrigger value="loans">Loans</TabsTrigger>
                  </>
                )}
                <TabsTrigger value="memberships">Memberships</TabsTrigger>
              </TabsList>

              {(user?.role === "ADMIN" || user?.role === "OPERATOR_KOP") && (
                <>
                  <TabsContent value="overview">
                    <OverviewTab overview={overview} />
                  </TabsContent>

                  <TabsContent value="savings">
                    <SavingsTab
                      savings={savings}
                      members={members}
                      userRole={user?.role}
                      onSavingsSubmit={handleSavingsSubmit}
                      onRefresh={fetchData}
                    />
                  </TabsContent>

                  <TabsContent value="loans">
                    <LoansTab
                      loans={loans}
                      userRole={user?.role}
                      onLoanSubmit={handleLoanSubmit}
                      onRefresh={fetchData}
                    />
                  </TabsContent>
                </>
              )}

              <TabsContent value="memberships">
                <MembershipsTab
                  members={members}
                  userRole={user?.role}
                  onMemberSubmit={handleMemberSubmit}
                  onMemberImport={handleMemberImport}
                  onMemberEdit={handleMemberEdit}
                  onMemberDelete={handleMemberDelete}
                  onMemberView={handleMemberView}
                  onRefresh={fetchData}
                />
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>

      {/* Edit Member Dialog */}
      <Dialog open={isEditMemberOpen} onOpenChange={setIsEditMemberOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Member</DialogTitle>
          </DialogHeader>
          {selectedMember && (
            <EditMemberForm
              member={selectedMember}
              onSubmit={handleEditMemberSubmit}
              onCancel={() => setIsEditMemberOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </ProtectedRoute>
  );
}
