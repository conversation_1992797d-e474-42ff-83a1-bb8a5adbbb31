/**
 * API Route: POST /api/leave-management/create
 *
 * Deskripsi: Membuat permintaan leave baru
 * Penggunaan: Form pengajuan leave
 *
 * Body:
 * - employeeId: <PERSON> karyawan (number)
 * - leaveTypeId: ID tipe leave (number)
 * - startDate: <PERSON><PERSON> mulai (string, format: YYYY-MM-DD)
 * - endDate: <PERSON><PERSON> selesai (string, format: YYYY-MM-DD)
 * - durationDays: <PERSON> leave dalam hari (number, tidak termasuk Sabtu dan <PERSON>gu)
 * - reason: Alasan leave (string)
 *
 * Catatan:
 * - Untuk leave type "Sakit" dan "Ijin", validasi daysAllowed diabaikan
 * - Untuk leave type lainnya, durationDays tidak boleh melebihi daysAllowed
 *
 * Response:
 * - 201: Permintaan leave berhasil dibuat
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { employeeId, leaveTypeId, startDate, endDate, reason, attachmentUrl } = data;

    // Validate required fields
    if (!employeeId || !leaveTypeId || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Employee ID, leave type, start date, and end date are required' },
        { status: 400 }
      );
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      );
    }

    if (start > end) {
      return NextResponse.json(
        { error: 'Start date cannot be after end date' },
        { status: 400 }
      );
    }

    // Use provided durationDays or calculate if not provided
    const calculatedDuration = data.durationDays || Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Get leave type to check if it requires approval
    const leaveType = await prisma.leaveType.findUnique({
      where: { id: parseInt(leaveTypeId) }
    });

    if (!leaveType) {
      return NextResponse.json(
        { error: 'Leave type not found' },
        { status: 404 }
      );
    }

    // Check if employee has enough leave balance
    // Abaikan validasi daysAllowed untuk leave type "Sakit" dan "Ijin"
    const skipDaysAllowedCheck = leaveType.name.toLowerCase() === 'sakit' || leaveType.name.toLowerCase() === 'ijin';

    if (!skipDaysAllowedCheck && calculatedDuration > leaveType.daysAllowed) {
      return NextResponse.json(
        { error: `Requested leave duration (${calculatedDuration} days) exceeds allowed days (${leaveType.daysAllowed} days)` },
        { status: 400 }
      );
    }

    // Create leave request
    const leaveRequest = await prisma.leaveRequest.create({
      data: {
        employee: { connect: { id: parseInt(employeeId) } },
        leaveType: { connect: { id: parseInt(leaveTypeId) } },
        startDate: start,
        endDate: end,
        reason: reason || null,
        attachmentUrl: attachmentUrl || null,
        status: leaveType.requiresApproval ? 'pending' : 'approved'
        // Hapus durationDays karena belum ada di skema database
      },
      include: {
        employee: true,
        leaveType: true
      }
    });

    // Simpan durationDays sebagai metadata dalam respons
    const responseData = {
      ...leaveRequest,
      calculatedDuration
    };

    return NextResponse.json(responseData, { status: 201 });
  } catch (error) {
    console.error('Error creating leave request:', error);
    return NextResponse.json(
      { error: 'Failed to create leave request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
