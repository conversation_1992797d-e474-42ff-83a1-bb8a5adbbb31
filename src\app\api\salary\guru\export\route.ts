import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';

// POST: Export guru salary data to Excel
export async function POST(request: Request) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { data } = await request.json();

    if (!data || !Array.isArray(data) || data.length === 0) {
      return NextResponse.json({ error: 'No data to export' }, { status: 400 });
    }

    // Format currency for Excel
    const formatCurrency = (value: number | null | undefined) => {
      if (value === null || value === undefined) return 0;
      return value;
    };

    // Format date for Excel
    const formatDate = (date: Date) => {
      const d = new Date(date);
      return `${d.getMonth() + 1}/${d.getFullYear()}`;
    };

    // Prepare data for Excel
    const excelData = data.map(item => ({
      'Nama': item.nama,
      'GP': formatCurrency(item.gp),
      'Load': item.load || 0,
      'HM': formatCurrency(item.hm),
      'Nominal': formatCurrency(item.nominal),
      'Jumlah XC': formatCurrency(item.jlh_xc),
      'Jabatan': formatCurrency(item.jab),
      'Wali Kelas': formatCurrency(item.walas),
      'Pembina OSIS': formatCurrency(item.pemb_osis),
      'XC': formatCurrency(item.xc),
      'Menggantikan': formatCurrency(item.menggantikan),
      'Total': formatCurrency(item.tot),
      'SL': item.sl ? 'Ya' : 'Tidak',
      'VL': item.vl ? 'Ya' : 'Tidak',
      'AWOL': item.awol ? 'Ya' : 'Tidak',
      'Jumlah SL': item.jlh_sl || 0,
      'Jumlah VL': item.jlh_vl || 0,
      'Jumlah AWOL': item.jlh_awol || 0,
      'Freq': item.freq ? 'Ya' : 'Tidak',
      'Minute': item.minute ? 'Ya' : 'Tidak',
      'Jumlah Freq': item.jlh_freq || 0,
      'Jumlah Minute': item.jlh_minute || 0,
      'Potongan Absensi': formatCurrency(item.pot_abs),
      'Potongan Ngajar': formatCurrency(item.pot_ngajar),
      'Bruto': formatCurrency(item.bruto),
      'Potongan BPJS TK': formatCurrency(item.pot_bpjstku),
      'Potongan BPJS Kesehatan': formatCurrency(item.pot_bpjskes),
      'Netto Setelah BPJS': formatCurrency(item.netto_stlh_bpjs),
      'PPh21': formatCurrency(item.pph21),
      'Pinjaman Lainnya': formatCurrency(item.pinj_lainnya),
      'Iuran Wajib': formatCurrency(item.iuran_wajib),
      'Pinjaman Koperasi': formatCurrency(item.pinj_kop),
      'Piutang': formatCurrency(item.piutang),
      'Potongan Bank': formatCurrency(item.pot_bank),
      'Total Potongan': formatCurrency(item.tot_pot),
      'Gaji Netto': formatCurrency(item.gaji_netto),
      'Periode': formatDate(item.period),
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Guru Salary');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });

    // Return Excel file as response
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="guru_salary_export.xlsx"',
      },
    });
  } catch (error) {
    console.error('Failed to export guru salary data:', error);
    return NextResponse.json(
      {
        error: 'Failed to export guru salary data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
