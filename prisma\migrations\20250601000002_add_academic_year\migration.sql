-- CreateTable
CREATE TABLE `academic_years` (
  `id` INTEGER NOT NULL AUTO_INCREMENT,
  `ta` VARCHAR(20) NOT NULL,
  `description` TEXT,
  `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` DATETIME(3) NOT NULL,
  PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AlterTable
ALTER TABLE `employees` ADD COLUMN `academic_year_id` INTEGER;

-- AddForeignKey
ALTER TABLE `employees` ADD CONSTRAINT `employees_academic_year_id_fkey` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
