/**
 * API Route: DELETE /api/departments/[id]/delete
 *
 * Deskripsi: Menghapus departemen berdasarkan ID
 * Penggunaan: Tombol hapus di halaman daftar departemen
 *
 * Path Parameters:
 * - id: ID departemen (number)
 *
 * Response:
 * - 200: Departemen berhasil dihapus
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Departemen tidak ditemukan
 * - 409: Departemen tidak dapat dihapus (memiliki karyawan)
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  const { params } = context;
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const id = params.id;

    // Check if department has employees (excluding those with EMP and EXT prefixes)
    const departmentWithEmployees = await prisma.department.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: {
            employees: {
              where: {
                NOT: {
                  OR: [
                    { employeeId: { startsWith: 'EMP' } },
                    { employeeId: { startsWith: 'EXT' } }
                  ]
                }
              }
            }
          }
        },
        employees: {
          where: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ]
          },
          select: {
            id: true
          }
        }
      }
    });

    if (!departmentWithEmployees) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    // Jumlah karyawan sudah dikurangi yang diawali EMP dan EXT dalam query
    const employeeCount = departmentWithEmployees._count.employees;

    if (employeeCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete department with existing employees' },
        { status: 409 }
      );
    }

    await prisma.department.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Department deleted successfully' });
  } catch (error) {
    console.error('Error deleting department:', error);
    return NextResponse.json(
      { error: 'Failed to delete department' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
