/**
 * API Route: POST /api/auth/login
 *
 * Deskripsi: Endpoint untuk autentikasi pengguna
 * Penggunaan: Form login
 *
 * Body:
 * - username: <PERSON>rna<PERSON> pengguna (string)
 * - password: Password pengguna (string)
 *
 * Response:
 * - 200: Login berhasil dengan data pengguna
 * - 401: Username atau password salah
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { compare } from 'bcryptjs';
import { logger } from '@/lib/logger';



export async function POST(request: Request) {
  logger.debug('POST /api/auth/login - Start login process');
  try {
    const { username, password } = await request.json();
    logger.debug('POST /api/auth/login - Login attempt received');

    // Find user
    logger.debug('POST /api/auth/login - Searching for user in database');
    const user = await prisma.user.findUnique({
      where: { username },
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            employeeId: true,
          },
        },
      },
    });

    if (!user) {
      logger.debug('POST /api/auth/login - User not found');
      return NextResponse.json({ success: false }, { status: 401 });
    }

    // Verify password
    logger.debug('POST /api/auth/login - Verifying password');
    const isPasswordValid = await compare(password, user.password);

    if (!isPasswordValid) {
      logger.debug('POST /api/auth/login - Invalid password');
      return NextResponse.json({ success: false }, { status: 401 });
    }

    // Update last login
    logger.debug('POST /api/auth/login - Updating last login timestamp');
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    });

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    // Tambahkan informasi employee ID ke data user
    const userData = {
      ...userWithoutPassword,
      id: user.id.toString(), // Gunakan user ID
      name: user.username // Gunakan username sebagai fallback jika tidak ada nama lengkap
    };

    // Jika user memiliki data employee, tambahkan informasi tambahan
    if (user.employee) {
      userData.name = `${user.employee.firstName || ''} ${user.employee.lastName || ''}`.trim() || user.username;
    }

    logger.debug('POST /api/auth/login - Login successful');
    logger.debug('POST /api/auth/login - User data prepared for client');
    return NextResponse.json({
      success: true,
      user: userData,
    });

  } catch (error) {
    logger.error('POST /api/auth/login - Login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    logger.debug('POST /api/auth/login - Database connection closed');
    await prisma.$disconnect();
  }
}
