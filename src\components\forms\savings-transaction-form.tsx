import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface SavingsTransactionFormData {
  memberId: string;
  amount: string;
  type: 'deposit' | 'withdrawal';
  contributionType?: 'one_time' | 'monthly' | 'optional';
  date: string;
  notes?: string;
}

interface SavingsTransactionFormProps {
  initialData?: Partial<SavingsTransactionFormData>;
  onSubmit: (data: SavingsTransactionFormData) => void;
  onCancel: () => void;
  members: any[];
}

export function SavingsTransactionForm({ initialData, onSubmit, onCancel, members }: SavingsTransactionFormProps) {
  const form = useForm<SavingsTransactionFormData>({
    defaultValues: {
      memberId: initialData?.memberId || "",
      amount: initialData?.amount || "",
      type: initialData?.type || "deposit",
      contributionType: initialData?.contributionType || "optional",
      date: initialData?.date || new Date().toISOString().split('T')[0],
      notes: initialData?.notes || ""
    }
  });

  console.log('SavingsTransactionForm initialized with members:', members);

  const handleSubmit = form.handleSubmit((data) => {
    console.log('Form data submitted:', data);
    onSubmit(data);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <FormField
          control={form.control}
          name="memberId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Member</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select member" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {members.map(member => (
                    <SelectItem key={member.id} value={member.id.toString()}>
                      {member.employee_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Transaction Type</FormLabel>
              <Select onValueChange={(value) => {
                field.onChange(value);
                // Reset contributionType if type is withdrawal
                if (value === 'withdrawal') {
                  form.setValue('contributionType', undefined);
                }
              }} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select transaction type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="deposit">Deposit</SelectItem>
                  <SelectItem value="withdrawal">Withdrawal</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="contributionType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contribution Type</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value || undefined}
                disabled={form.watch('type') !== 'deposit'}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select contribution type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="one_time">One Time Contribution</SelectItem>
                  <SelectItem value="monthly">Monthly Contribution</SelectItem>
                  <SelectItem value="optional">Optional Contribution</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                {form.watch('type') === 'deposit' ? 'Select which contribution to increase' : 'Withdrawals can only be made from optional contribution'}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Amount</FormLabel>
              <FormControl>
                <Input type="number" {...field} placeholder="Enter amount" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Transaction Date</FormLabel>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder="Enter additional notes" />
              </FormControl>
              <FormDescription>Optional</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Submit</Button>
        </div>
      </form>
    </Form>
  );
}
