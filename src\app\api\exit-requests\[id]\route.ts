/**
 * API Route: /api/exit-requests/[id]
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse, NextRequest } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    // Redirect ke endpoint GET yang sebenarnya
    const response = await fetch(new URL(`/api/exit-requests/${id}/get`, request.url), {
      method: 'GET',
      headers: request.headers
    });

    return response;
  } catch (error: any) {
    console.error('Error in GET /api/exit-requests/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    // Redirect ke endpoint PUT yang sebenarnya
    const response = await fetch(new URL(`/api/exit-requests/${id}/update`, request.url), {
      method: 'PUT',
      headers: request.headers,
      body: request.body
    });

    return response;
  } catch (error: any) {
    console.error('Error in PUT /api/exit-requests/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    // Redirect ke endpoint DELETE yang sebenarnya
    const response = await fetch(new URL(`/api/exit-requests/${id}/delete`, request.url), {
      method: 'DELETE',
      headers: request.headers
    });

    return response;
  } catch (error: any) {
    console.error('Error in DELETE /api/exit-requests/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
