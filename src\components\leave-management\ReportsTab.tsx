"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from "recharts";
import {
  FileBarChart,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Clock,
  LogOut
} from "lucide-react";

interface ReportsTabProps {
  userRole?: string;
}

export function ReportsTab({ userRole }: ReportsTabProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("leave");
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<any>(null);

  // Filter states
  const [departments, setDepartments] = useState<any[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<any[]>([]);
  const [lateTypes, setLateTypes] = useState<any[]>([]);
  const [exitTypes, setExitTypes] = useState<any[]>([]);

  // Selected filters
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [selectedLeaveType, setSelectedLeaveType] = useState<string>("all");
  const [selectedLateType, setSelectedLateType] = useState<string>("all");
  const [selectedExitType, setSelectedExitType] = useState<string>("all");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");

  // COLORS for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];
  const STATUS_COLORS = {
    pending: '#FFBB28',
    approved: '#00C49F',
    rejected: '#FF8042'
  };

  // Fetch report data
  const fetchReportData = async () => {
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('type', activeTab);

      if (selectedDepartment && selectedDepartment !== 'all') {
        params.append('departmentId', selectedDepartment);
      }

      if (activeTab === 'leave' && selectedLeaveType && selectedLeaveType !== 'all') {
        params.append('leaveTypeId', selectedLeaveType);
      } else if (activeTab === 'late' && selectedLateType && selectedLateType !== 'all') {
        params.append('lateType', selectedLateType);
      } else if (activeTab === 'exit' && selectedExitType && selectedExitType !== 'all') {
        params.append('exitType', selectedExitType);
      }

      if (startDate) {
        params.append('startDate', startDate);
      }

      if (endDate) {
        params.append('endDate', endDate);
      }

      const response = await fetch(`/api/leave-management/reports?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch report data');
      }

      const data = await response.json();
      setReportData(data);

      // Set filter options
      if (data.filters) {
        setDepartments(data.filters.departments || []);

        if (activeTab === 'leave') {
          setLeaveTypes(data.filters.leaveTypes || []);
        } else if (activeTab === 'late') {
          setLateTypes(data.filters.lateTypes || []);
        } else if (activeTab === 'exit') {
          setExitTypes(data.filters.exitTypes || []);
        }
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch report data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setSelectedDepartment("all");
    setSelectedLeaveType("all");
    setSelectedLateType("all");
    setSelectedExitType("all");
    setReportData(null);
  };

  // Format data for department chart
  const formatDepartmentChartData = () => {
    if (!reportData || !reportData.stats || !reportData.stats.byDepartment) {
      return [];
    }

    return Object.entries(reportData.stats.byDepartment).map(([name, value]) => ({
      name,
      value
    }));
  };

  // Format data for type chart
  const formatTypeChartData = () => {
    if (!reportData || !reportData.stats) {
      return [];
    }

    if (activeTab === 'leave' && reportData.stats.byLeaveType) {
      return Object.entries(reportData.stats.byLeaveType).map(([name, value]) => ({
        name,
        value
      }));
    } else if (activeTab === 'late' && reportData.stats.byLateType) {
      return Object.entries(reportData.stats.byLateType).map(([name, value]) => ({
        name,
        value
      }));
    } else if (activeTab === 'exit' && reportData.stats.byExitType) {
      return Object.entries(reportData.stats.byExitType).map(([name, value]) => ({
        name,
        value
      }));
    }

    return [];
  };

  // Format data for status chart
  const formatStatusChartData = () => {
    if (!reportData || !reportData.stats || !reportData.stats.byStatus) {
      return [];
    }

    return Object.entries(reportData.stats.byStatus).map(([name, value]) => ({
      name: name.charAt(0).toUpperCase() + name.slice(1),
      value
    }));
  };

  // Effect to fetch data when tab changes
  useEffect(() => {
    fetchReportData();
  }, [activeTab]);

  return (
    <div className="bg-card rounded-lg shadow-sm p-6">
      <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
        <div className="p-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 border-b border-border">
          <div>
            <h2 className="text-lg font-semibold">Leave Management Reports</h2>
            <p className="text-xs text-muted-foreground">
              View and analyze leave management data
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchReportData}
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <div className="p-4 border-b border-border">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="leave" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Leave Requests</span>
              </TabsTrigger>
              <TabsTrigger value="late" className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>Late Requests</span>
              </TabsTrigger>
              <TabsTrigger value="exit" className="flex items-center gap-1">
                <LogOut className="h-4 w-4" />
                <span>Exit Requests</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="p-4 border-b border-border">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="department">Department</Label>
                <Select
                  value={selectedDepartment}
                  onValueChange={setSelectedDepartment}
                >
                  <SelectTrigger id="department">
                    <SelectValue placeholder="All Departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {activeTab === "leave" && (
                <div>
                  <Label htmlFor="leaveType">Leave Type</Label>
                  <Select
                    value={selectedLeaveType}
                    onValueChange={setSelectedLeaveType}
                  >
                    <SelectTrigger id="leaveType">
                      <SelectValue placeholder="All Leave Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Leave Types</SelectItem>
                      {leaveTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id.toString()}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {activeTab === "late" && (
                <div>
                  <Label htmlFor="lateType">Late Type</Label>
                  <Select
                    value={selectedLateType}
                    onValueChange={setSelectedLateType}
                  >
                    <SelectTrigger id="lateType">
                      <SelectValue placeholder="All Late Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Late Types</SelectItem>
                      {lateTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {activeTab === "exit" && (
                <div>
                  <Label htmlFor="exitType">Exit Type</Label>
                  <Select
                    value={selectedExitType}
                    onValueChange={setSelectedExitType}
                  >
                    <SelectTrigger id="exitType">
                      <SelectValue placeholder="All Exit Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Exit Types</SelectItem>
                      {exitTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="flex gap-2">
                <div className="w-1/2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div className="w-1/2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <Button onClick={fetchReportData} disabled={loading}>
                <Filter className="h-4 w-4 mr-2" />
                Apply Filters
              </Button>
            </div>
          </div>

          <div className="p-4">
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="flex flex-col items-center">
                  <div className="animate-spin h-8 w-8 border-2 border-primary rounded-full border-t-transparent"></div>
                  <p className="mt-2 text-sm text-muted-foreground">Loading report data...</p>
                </div>
              </div>
            ) : reportData ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{reportData.stats.total}</div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {activeTab === "leave" ? "Leave" : activeTab === "late" ? "Late" : "Exit"} requests
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Status Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="text-xs flex items-center">
                          <div className="h-3 w-3 rounded-full bg-yellow-400 mr-2"></div>
                          <span>Pending: {reportData.stats.byStatus.pending}</span>
                        </div>
                        <div className="text-xs flex items-center">
                          <div className="h-3 w-3 rounded-full bg-green-400 mr-2"></div>
                          <span>Approved: {reportData.stats.byStatus.approved}</span>
                        </div>
                        <div className="text-xs flex items-center">
                          <div className="h-3 w-3 rounded-full bg-red-400 mr-2"></div>
                          <span>Rejected: {reportData.stats.byStatus.rejected}</span>
                        </div>
                      </div>
                      <div className="h-16 w-16">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={formatStatusChartData()}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius={30}
                              fill="#8884d8"
                            >
                              {formatStatusChartData().map((entry, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={
                                    entry.name.toLowerCase() === 'pending'
                                      ? STATUS_COLORS.pending
                                      : entry.name.toLowerCase() === 'approved'
                                        ? STATUS_COLORS.approved
                                        : STATUS_COLORS.rejected
                                  }
                                />
                              ))}
                            </Pie>
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        {activeTab === "leave"
                          ? "Leave Types"
                          : activeTab === "late"
                            ? "Late Types"
                            : "Exit Types"}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-1">
                        {formatTypeChartData().map((item, index) => (
                          <div key={index} className="text-xs flex items-center justify-between">
                            <div className="flex items-center">
                              <div
                                className="h-3 w-3 rounded-full mr-2"
                                style={{ backgroundColor: COLORS[index % COLORS.length] }}
                              ></div>
                              <span>{item.name}</span>
                            </div>
                            <span>{item.value}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Department Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={formatDepartmentChartData()}
                            layout="vertical"
                            margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis type="number" />
                            <YAxis
                              type="category"
                              dataKey="name"
                              width={80}
                              tick={{ fontSize: 12 }}
                            />
                            <Tooltip />
                            <Bar dataKey="value" fill="#8884d8" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">
                        {activeTab === "leave"
                          ? "Leave Type Distribution"
                          : activeTab === "late"
                            ? "Late Type Distribution"
                            : "Exit Type Distribution"}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={formatTypeChartData()}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius={100}
                              fill="#8884d8"
                              label={(entry) => `${entry.name}: ${entry.value}`}
                            >
                              {formatTypeChartData().map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {activeTab === "exit" && reportData.stats.byComeback && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Comeback Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center h-64">
                        <ResponsiveContainer width="50%" height="100%">
                          <PieChart>
                            <Pie
                              data={[
                                { name: "Will Return", value: reportData.stats.byComeback.comeback },
                                { name: "Not Returning", value: reportData.stats.byComeback.notComeback }
                              ]}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius={80}
                              fill="#8884d8"
                              label
                            >
                              <Cell fill="#4ade80" />
                              <Cell fill="#f87171" />
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            ) : (
              <div className="flex justify-center items-center h-64">
                <div className="text-center">
                  <FileBarChart className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">No Report Data</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Select filters and click "Apply Filters" to generate a report
                  </p>
                  <Button onClick={fetchReportData} className="mt-4">
                    Generate Report
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Tabs>
      </div>
    </div>
  );
}
