"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import Navbar from "@/components/layout/Navbar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Search } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";

type ExitedEmployee = {
  id: number;
  employeeId: string;
  firstName: string;
  lastName: string;
  department?: {
    id: number;
    name: string;
  };
  position?: {
    id: number;
    title: string;
  };
  manager?: {
    id: number;
    firstName: string;
    lastName: string;
  };
  academicYear?: {
    id: number;
    ta: string;
    description: string | null;
  };
  isDeleted: boolean;
  deletedAt: string;
};

export default function ExitedEmployeesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [exitedEmployees, setExitedEmployees] = useState<ExitedEmployee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const employeesPerPage = 10;

  // Redirect jika bukan ADMIN
  useEffect(() => {
    if (user && user.role !== "ADMIN") {
      router.push("/");
    }
  }, [user, router]);

  // Fetch data karyawan yang sudah keluar
  const fetchExitedEmployees = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/employees/exited", {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch exited employees: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      logger.debug(`Fetched ${Array.isArray(data) ? data.length : 0} exited employees`);

      // Ensure data is always an array to prevent runtime errors
      const safeData = Array.isArray(data) ? data : [];
      setExitedEmployees(safeData);
    } catch (error) {
      logger.error('Exited Employees Page - Failed to fetch exited employees:', error);
      // Set empty array as fallback to prevent undefined errors
      setExitedEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExitedEmployees();
  }, []);

  // Filter berdasarkan pencarian - optimized to avoid dependency loop
  const filteredEmployees = useMemo(() => {
    // Safety check to ensure exitedEmployees is always an array
    const safeEmployees = Array.isArray(exitedEmployees) ? exitedEmployees : [];

    if (!searchQuery.trim()) return safeEmployees;

    return safeEmployees.filter((emp) => {
      // Safety checks for employee properties
      if (!emp) return false;

      const fullName = `${emp.firstName || ''} ${emp.lastName || ''}`.toLowerCase();
      const departmentName = emp.department?.name?.toLowerCase() || '';
      const positionTitle = emp.position?.title?.toLowerCase() || '';
      const academicYear = emp.academicYear?.ta?.toLowerCase() || '';
      const employeeId = emp.employeeId?.toLowerCase() || '';
      const query = searchQuery.toLowerCase();

      return (
        fullName.includes(query) ||
        departmentName.includes(query) ||
        positionTitle.includes(query) ||
        academicYear.includes(query) ||
        employeeId.includes(query)
      );
    });
  }, [exitedEmployees, searchQuery]);

  // Hitung total halaman
  const totalPages = useMemo(() => {
    const safeLength = Array.isArray(filteredEmployees) ? filteredEmployees.length : 0;
    return Math.max(1, Math.ceil(safeLength / employeesPerPage));
  }, [filteredEmployees, employeesPerPage]);

  // Pagination data
  const paginatedData = useMemo(() => {
    const safeEmployees = Array.isArray(filteredEmployees) ? filteredEmployees : [];
    const startIndex = (currentPage - 1) * employeesPerPage;
    const endIndex = startIndex + employeesPerPage;
    return safeEmployees.slice(startIndex, endIndex);
  }, [filteredEmployees, currentPage, employeesPerPage]);

  // Format tanggal
  const formatDate = (dateString: string) => {
    if (!dateString) return "-";
    return format(new Date(dateString), "dd MMMM yyyy", { locale: id });
  };

  if (user && user.role !== "ADMIN") {
    return null; // Tidak render apapun jika bukan ADMIN
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={user?.role} />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Exited Employees</h1>
          <p className="text-muted-foreground mt-2">
            View employees who have left the organization
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Exited Employees List</CardTitle>
            <CardDescription>
              Employees who have been soft-deleted from the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
              <div className="relative w-full sm:w-auto">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search employees..."
                  className="pl-8 w-full sm:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Academic Year</TableHead>
                    <TableHead>Exit Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : paginatedData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center">
                        No exited employees found
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedData.map((emp) => (
                      <TableRow key={emp.id}>
                        <TableCell>{emp.employeeId}</TableCell>
                        <TableCell>
                          {`${emp.firstName} ${emp.lastName || ''}`}
                        </TableCell>
                        <TableCell>{emp.department?.name || '-'}</TableCell>
                        <TableCell>{emp.position?.title || '-'}</TableCell>
                        <TableCell>{emp.academicYear?.ta || '-'}</TableCell>
                        <TableCell>{formatDate(emp.deletedAt)}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
