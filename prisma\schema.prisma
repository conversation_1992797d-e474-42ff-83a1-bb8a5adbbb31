generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id         Int       @id @default(autoincrement())
  username   String    @unique @db.VarChar(50)
  email      String?   @unique @db.VarChar(100)
  password   String    @db.VarChar(255)
  role       Role      @default(EMPLOYEE)
  lastLogin  DateTime? @map("last_login")
  isActive   Boolean   @default(true) @map("is_active")
  employeeId Int       @unique @map("employee_id")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  employee   Employee  @relation(fields: [employeeId], references: [id])

  @@map("users")
}

model Employee {
  id                    Int             @id @default(autoincrement())
  employeeId            String          @unique @map("employee_id") @db.VarChar(10)
  firstName             String          @map("first_name") @db.VarChar(50)
  lastName              String?         @map("last_name") @db.VarChar(50)
  email                 String          @unique @db.VarChar(100)
  phone                 String?         @db.VarChar(20)
  hireDate              DateTime        @map("hire_date")
  departmentId          Int             @map("department_id")
  positionId            Int             @map("position_id")
  managerId             Int?            @map("manager_id")
  academicYearId        Int?            @map("academic_year_id")
  address               String?
  city                  String?         @db.VarChar(50)
  state                 String?         @db.VarChar(50)
  postalCode            String?         @map("postal_code") @db.VarChar(20)
  country               String?         @db.VarChar(50)
  birthDate             DateTime?       @map("birth_date")
  gender                String?         @db.VarChar(10)
  maritalStatus         String?         @map("marital_status") @db.VarChar(20)
  emergencyContactName  String?         @map("emergency_contact_name") @db.VarChar(100)
  emergencyContactPhone String?         @map("emergency_contact_phone") @db.VarChar(20)
  status                EmployeeStatus  @default(Tetap)
  isDeleted             Boolean         @default(false) @map("is_deleted")
  deletedAt             DateTime?       @map("deleted_at")
  createdAt             DateTime        @default(now()) @map("created_at")
  updatedAt             DateTime        @updatedAt @map("updated_at")
  birthPlace            String?         @map("birth_place") @db.VarChar(100)
  religion              String?         @db.VarChar(50)
  contractType          String?         @map("contract_type") @db.VarChar(50)
  identityType          String?         @map("identity_type") @db.VarChar(20)
  identityNumber        String?         @map("identity_number") @db.VarChar(50)
  npwp                  String?         @db.VarChar(25)
  bpjsKesehatan         String?         @map("bpjs_kesehatan") @db.VarChar(20)
  bpjsKetenagakerjaan   String?         @map("bpjs_ketenagakerjaan") @db.VarChar(20)
  educationLevel        String?         @map("education_level") @db.VarChar(50)
  educationMajor        String?         @map("education_major") @db.VarChar(100)
  educationInstitution  String?         @map("education_institution") @db.VarChar(100)
  bankName              String?         @map("bank_name") @db.VarChar(50)
  bankAccount           String?         @map("bank_account") @db.VarChar(50)
  academicYear          AcademicYear?   @relation(fields: [academicYearId], references: [id])
  departmentHead        Department[]    @relation("DepartmentHead")
  department            Department      @relation(fields: [departmentId], references: [id])
  manager               Employee?       @relation("EmployeeToManager", fields: [managerId], references: [id])
  subordinates          Employee[]      @relation("EmployeeToManager")
  position              Position        @relation(fields: [positionId], references: [id])
  approvedExits         ExitRequest[]   @relation("ExitApprover")
  exitRequests          ExitRequest[]
  approvedLoans         KoperasiLoan[]  @relation("LoanApprover")
  koperasiMembers       KoperasiMember?
  approvedLates         LateRequest[]   @relation("LateApprover")
  lateRequests          LateRequest[]
  approvedLeaves        LeaveRequest[]  @relation("LeaveApprover")
  leaveRequests         LeaveRequest[]
  salaryGuru            SalaryGuru[]
  salaryHonor           SalaryHonor[]
  salaryStaff           SalaryStaff[]
  user                  User?

  @@index([departmentId], map: "employees_department_id_fkey")
  @@index([managerId], map: "employees_manager_id_fkey")
  @@index([positionId], map: "employees_position_id_fkey")
  @@index([academicYearId], map: "employees_academic_year_id_fkey")
  @@map("employees")
}

model Department {
  id          Int        @id @default(autoincrement())
  name        String     @unique @db.VarChar(100)
  description String?    @db.Text
  headId      Int?       @map("head_id")
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")
  head        Employee?  @relation("DepartmentHead", fields: [headId], references: [id])
  employees   Employee[]
  positions   Position[]

  @@index([headId], map: "departments_head_id_fkey")
  @@map("departments")
}

model Position {
  id           Int        @id @default(autoincrement())
  title        String     @db.VarChar(100)
  departmentId Int        @map("department_id")
  description  String?    @db.Text
  minSalary    Decimal?   @map("min_salary") @db.Decimal(12, 2)
  maxSalary    Decimal?   @map("max_salary") @db.Decimal(12, 2)
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")
  employees    Employee[]
  department   Department @relation(fields: [departmentId], references: [id])

  @@index([departmentId], map: "positions_department_id_fkey")
  @@map("positions")
}

model LeaveType {
  id               Int            @id @default(autoincrement())
  name             String         @unique @db.VarChar(50)
  description      String?        @db.Text
  daysAllowed      Int            @map("days_allowed")
  requiresApproval Boolean        @default(true) @map("requires_approval")
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")
  leaveRequests    LeaveRequest[]

  @@map("leave_types")
}

model LeaveRequest {
  id            Int                @id @default(autoincrement())
  employeeId    Int                @map("employee_id")
  leaveTypeId   Int                @map("leave_type_id")
  startDate     DateTime           @map("start_date")
  endDate       DateTime           @map("end_date")
  reason        String?            @db.Text
  status        LeaveRequestStatus @default(pending)
  approvedById  Int?               @map("approved_by")
  approvedAt    DateTime?          @map("approved_at")
  createdAt     DateTime           @default(now()) @map("created_at")
  updatedAt     DateTime           @updatedAt @map("updated_at")
  attachmentUrl String?            @map("attachment_url") @db.VarChar(255)
  approvedBy    Employee?          @relation("LeaveApprover", fields: [approvedById], references: [id])
  employee      Employee           @relation(fields: [employeeId], references: [id])
  leaveType     LeaveType          @relation(fields: [leaveTypeId], references: [id])

  @@index([approvedById], map: "leave_requests_approved_by_fkey")
  @@index([employeeId], map: "leave_requests_employee_id_fkey")
  @@index([leaveTypeId], map: "leave_requests_leave_type_id_fkey")
  @@map("leave_requests")
}

model LateRequest {
  id            Int               @id @default(autoincrement())
  employeeId    Int               @map("employee_id")
  lateType      LateType          @map("late_type")
  lateDate      DateTime          @map("late_date")
  estimatedTime String            @map("estimated_time") @db.VarChar(10)
  reason        String?           @db.Text
  status        LateRequestStatus @default(pending)
  approvedById  Int?              @map("approved_by")
  approvedAt    DateTime?         @map("approved_at")
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")
  attachmentUrl String?           @map("attachment_url") @db.VarChar(255)
  approvedBy    Employee?         @relation("LateApprover", fields: [approvedById], references: [id])
  employee      Employee          @relation(fields: [employeeId], references: [id])

  @@index([approvedById], map: "late_requests_approved_by_fkey")
  @@index([employeeId], map: "late_requests_employee_id_fkey")
  @@map("late_requests")
}

model ExitRequest {
  id           Int               @id @default(autoincrement())
  employeeId   Int               @map("employee_id")
  exitType     ExitType          @map("exit_type")
  exitDate     DateTime          @map("exit_date")
  exitTime     String            @map("exit_time") @db.VarChar(10)
  comebackTime String?           @map("comeback_time") @db.VarChar(10)
  notComeback  Boolean           @default(false) @map("not_comeback")
  reason       String?           @db.Text
  status       ExitRequestStatus @default(pending)
  approvedById Int?              @map("approved_by")
  approvedAt   DateTime?         @map("approved_at")
  createdAt    DateTime          @default(now()) @map("created_at")
  updatedAt    DateTime          @updatedAt @map("updated_at")
  approvedBy   Employee?         @relation("ExitApprover", fields: [approvedById], references: [id])
  employee     Employee          @relation(fields: [employeeId], references: [id])

  @@index([approvedById], map: "exit_requests_approved_by_fkey")
  @@index([employeeId], map: "exit_requests_employee_id_fkey")
  @@map("exit_requests")
}

model KoperasiMember {
  id                   Int              @id @default(autoincrement())
  employeeId           Int              @unique @map("employee_id")
  joinDate             DateTime         @map("join_date")
  monthlyContribution  Decimal          @map("monthly_contribution") @db.Decimal(12, 2)
  totalSavings         Decimal          @default(0.00) @map("total_savings") @db.Decimal(12, 2)
  status               MemberStatus     @default(active)
  createdAt            DateTime         @default(now()) @map("created_at")
  updatedAt            DateTime         @updatedAt @map("updated_at")
  oneTimeContribution  Decimal?         @map("one_time_contribution") @db.Decimal(12, 2)
  optionalContribution Decimal?         @map("optional_contribution") @db.Decimal(12, 2)
  notes                String?          @db.Text
  loans                KoperasiLoan[]
  employee             Employee         @relation(fields: [employeeId], references: [id])
  savings              KoperasiSaving[]

  @@index([employeeId], map: "koperasi_members_employee_id_fkey")
  @@map("koperasi_members")
}

model KoperasiSaving {
  id               Int            @id @default(autoincrement())
  memberId         Int            @map("member_id")
  amount           Decimal        @db.Decimal(12, 2)
  type             String         @db.VarChar(20)
  date             DateTime
  notes            String?        @db.Text
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")
  contributionType String?        @map("contribution_type") @db.VarChar(20)
  member           KoperasiMember @relation(fields: [memberId], references: [id])

  @@index([memberId], map: "koperasi_savings_member_id_fkey")
  @@map("koperasi_savings")
}

model KoperasiLoan {
  id              Int                   @id @default(autoincrement())
  memberId        Int                   @map("member_id")
  amount          Decimal               @db.Decimal(12, 2)
  purpose         String                @db.Text
  applicationDate DateTime              @map("application_date")
  approvalDate    DateTime?             @map("approval_date")
  startDate       DateTime?             @map("start_date")
  endDate         DateTime?             @map("end_date")
  interestRate    Decimal               @map("interest_rate") @db.Decimal(5, 2)
  status          LoanStatus            @default(pending)
  approvedById    Int?                  @map("approved_by")
  createdAt       DateTime              @default(now()) @map("created_at")
  updatedAt       DateTime              @updatedAt @map("updated_at")
  payments        KoperasiLoanPayment[]
  approvedBy      Employee?             @relation("LoanApprover", fields: [approvedById], references: [id])
  member          KoperasiMember        @relation(fields: [memberId], references: [id])

  @@index([approvedById], map: "koperasi_loans_approved_by_fkey")
  @@index([memberId], map: "koperasi_loans_member_id_fkey")
  @@map("koperasi_loans")
}

model KoperasiLoanPayment {
  id            Int          @id @default(autoincrement())
  loanId        Int          @map("loan_id")
  paymentDate   DateTime     @map("payment_date")
  amount        Decimal      @db.Decimal(12, 2)
  paymentMethod String?      @map("payment_method") @db.VarChar(50)
  notes         String?      @db.Text
  createdAt     DateTime     @default(now()) @map("created_at")
  updatedAt     DateTime     @updatedAt @map("updated_at")
  loan          KoperasiLoan @relation(fields: [loanId], references: [id])

  @@index([loanId], map: "koperasi_loan_payments_loan_id_fkey")
  @@map("koperasi_loan_payments")
}

model SalaryStaff {
  id                Int       @id @default(autoincrement())
  employeeId        Int?      @map("employee_id")
  nama              String    @db.VarChar(100)
  gp                Decimal   @db.Decimal(12, 2)
  gol_kontrak       Decimal?  @db.Decimal(12, 2)
  gol_tetap         Decimal?  @db.Decimal(12, 2)
  beban_kerja       Decimal?  @db.Decimal(12, 2)
  insentif          Decimal?  @db.Decimal(12, 2)
  tunj_keahlian     Decimal?  @db.Decimal(12, 2)
  tunj_jab          Decimal?  @db.Decimal(12, 2)
  tot_gross         Decimal   @db.Decimal(12, 2)
  bpjstku_yay       Decimal?  @db.Decimal(12, 2)
  bpjskes_yay       Decimal?  @db.Decimal(12, 2)
  pph_yay           Decimal?  @db.Decimal(12, 2)
  tot_gross_yay     Decimal?  @db.Decimal(12, 2)
  awol              Int?      @default(0) @db.TinyInt
  jlh_awol          Int?      @default(0)
  freq              Int?      @default(0) @db.TinyInt
  minute            Int?      @default(0)
  jlh_freq          Int?      @default(0)
  jlh_minute        Int?      @default(0)
  pot_abs           Decimal?  @db.Decimal(12, 2)
  gaji_stlh_pot_abs Decimal?  @db.Decimal(12, 2)
  bpjstku           Decimal?  @db.Decimal(12, 2)
  bpjskes           Decimal?  @db.Decimal(12, 2)
  nett_stlh_bpjs    Decimal?  @db.Decimal(12, 2)
  pph               Decimal?  @db.Decimal(12, 2)
  pinj_lain         Decimal?  @db.Decimal(12, 2)
  iuran_wajib       Decimal?  @db.Decimal(12, 2)
  pinj_kop          Decimal?  @db.Decimal(12, 2)
  piutang           Decimal?  @db.Decimal(12, 2)
  pot_bank          Decimal?  @db.Decimal(12, 2)
  tot_pot           Decimal?  @db.Decimal(12, 2)
  gaji_netto        Decimal   @db.Decimal(12, 2)
  period            DateTime
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  employee          Employee? @relation(fields: [employeeId], references: [id])

  @@index([employeeId], map: "salary_staff_employee_id_fkey")
  @@map("salary_staff")
}

model SalaryGuru {
  id              Int       @id @default(autoincrement())
  employeeId      Int?      @map("employee_id")
  nama            String    @db.VarChar(100)
  gp              Decimal   @db.Decimal(12, 2)
  load            Int?      @default(0)
  hm              Decimal?  @db.Decimal(12, 2)
  nominal         Decimal?  @db.Decimal(12, 2)
  jlh_xc          Decimal?  @db.Decimal(12, 2)
  jab             Decimal?  @db.Decimal(12, 2)
  walas           Decimal?  @db.Decimal(12, 2)
  pemb_osis       Decimal?  @db.Decimal(12, 2)
  xc              Decimal?  @db.Decimal(12, 2)
  menggantikan    Decimal?  @db.Decimal(12, 2)
  tot             Decimal?  @db.Decimal(12, 2)
  sl              Int?      @default(0) @db.TinyInt
  vl              Int?      @default(0) @db.TinyInt
  awol            Int?      @default(0) @db.TinyInt
  jlh_sl          Int?      @default(0)
  jlh_vl          Int?      @default(0)
  jlh_awol        Int?      @default(0)
  freq            Int?      @default(0) @db.TinyInt
  minute          Int?      @default(0)
  jlh_freq        Int?      @default(0)
  jlh_minute      Int?      @default(0)
  pot_abs         Decimal?  @db.Decimal(12, 2)
  pot_ngajar      Decimal?  @db.Decimal(12, 2)
  bruto           Decimal?  @db.Decimal(12, 2)
  pot_bpjstku     Decimal?  @db.Decimal(12, 2)
  pot_bpjskes     Decimal?  @db.Decimal(12, 2)
  netto_stlh_bpjs Decimal?  @db.Decimal(12, 2)
  pph21           Decimal?  @db.Decimal(12, 2)
  pinj_lainnya    Decimal?  @db.Decimal(12, 2)
  iuran_wajib     Decimal?  @db.Decimal(12, 2)
  pinj_kop        Decimal?  @db.Decimal(12, 2)
  piutang         Decimal?  @db.Decimal(12, 2)
  pot_bank        Decimal?  @db.Decimal(12, 2)
  tot_pot         Decimal?  @db.Decimal(12, 2)
  gaji_netto      Decimal   @db.Decimal(12, 2)
  period          DateTime
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  employee        Employee? @relation(fields: [employeeId], references: [id])

  @@index([employeeId], map: "salary_guru_employee_id_fkey")
  @@map("salary_guru")
}

model SalaryHonor {
  id                Int       @id @default(autoincrement())
  employeeId        Int?      @map("employee_id")
  nama              String    @db.VarChar(100)
  gp1               Decimal?  @db.Decimal(12, 2)
  pph_dibayar_sklh  Decimal?  @db.Decimal(12, 2)
  tot_tun_tep       Decimal?  @db.Decimal(12, 2)
  hnr               Decimal?  @db.Decimal(12, 2)
  jp                Decimal?  @db.Decimal(12, 2)
  tot_hnr           Decimal?  @db.Decimal(12, 2)
  jlh_hdr           Int?      @default(0)
  hnr_hdr           Decimal?  @db.Decimal(12, 2)
  tot_hnr_hdr       Decimal?  @db.Decimal(12, 2)
  sblm_pph          Decimal?  @db.Decimal(12, 2)
  jlhxc             Int?      @default(0)
  xc                Decimal?  @db.Decimal(12, 2)
  tmbhn             Decimal?  @db.Decimal(12, 2)
  gaji_bruto        Decimal?  @db.Decimal(12, 2)
  bruto_stlh_pot    Decimal?  @db.Decimal(12, 2)
  nettstlhjamsostek Decimal?  @db.Decimal(12, 2)
  pph21             Decimal?  @db.Decimal(12, 2)
  pinj_lainnya      Decimal?  @db.Decimal(12, 2)
  iuran_wajib       Decimal?  @db.Decimal(12, 2)
  pinj_kop          Decimal?  @db.Decimal(12, 2)
  piutang           Decimal?  @db.Decimal(12, 2)
  pot_bank          Decimal?  @db.Decimal(12, 2)
  gaji_netto        Decimal   @db.Decimal(12, 2)
  period            DateTime
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  employee          Employee? @relation(fields: [employeeId], references: [id])

  @@index([employeeId], map: "salary_honor_employee_id_fkey")
  @@map("salary_honor")
}

enum Role {
  ADMIN
  SUPERVISOR
  EMPLOYEE
  OPERATOR_KOP
  HEAD
}

enum EmployeeStatus {
  Tetap
  Kontrak
  Honor
}

enum LeaveRequestStatus {
  pending
  approved
  rejected
}

enum MemberStatus {
  active
  inactive
}

enum LoanStatus {
  pending
  approved
  rejected
  completed
}

enum LateType {
  Urgent
  Work
}

enum LateRequestStatus {
  pending
  approved
  rejected
}

enum ExitType {
  Work
  Personal
}

enum ExitRequestStatus {
  pending
  approved
  rejected
}

model AcademicYear {
  id                     Int        @id @default(autoincrement())
  ta                     String     @db.VarChar(20)
  description            String?    @db.Text
  currentEmployeeCount   Int?       @default(0) @map("current_employee_count")
  historicalActiveCount  Int?       @default(0) @map("historical_active_count")
  historicalExitedCount  Int?       @default(0) @map("historical_exited_count")
  createdAt              DateTime   @default(now()) @map("created_at")
  updatedAt              DateTime   @updatedAt @map("updated_at")
  employees              Employee[]

  @@map("academic_years")
}
