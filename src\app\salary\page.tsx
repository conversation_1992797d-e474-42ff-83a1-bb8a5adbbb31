'use client';

import { useState, useEffect } from 'react';
import { useAuth } from "@/lib/auth";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import ProtectedRoute from "@/components/ProtectedRoute";
import Navbar from "@/components/layout/Navbar";
import { StaffTab, GuruTab, HonorTab } from "@/components/salary";
import type { SalaryStaff, SalaryGuru, SalaryHonor } from "@/lib/types";

export default function SalaryPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("staff");
  const [selectedMonth, setSelectedMonth] = useState<string>(new Date().getMonth().toString());
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  const [staffData, setStaffData] = useState<SalaryStaff[]>([]);
  const [guruData, setGuruData] = useState<SalaryGuru[]>([]);
  const [honorData, setHonorData] = useState<SalaryHonor[]>([]);
  const [loading, setLoading] = useState(false);

  const months = [
    { value: "0", label: "Januari" },
    { value: "1", label: "Februari" },
    { value: "2", label: "Maret" },
    { value: "3", label: "April" },
    { value: "4", label: "Mei" },
    { value: "5", label: "Juni" },
    { value: "6", label: "Juli" },
    { value: "7", label: "Agustus" },
    { value: "8", label: "September" },
    { value: "9", label: "Oktober" },
    { value: "10", label: "November" },
    { value: "11", label: "Desember" },
  ];

  const years = Array.from({ length: 5 }, (_, i) => {
    const year = new Date().getFullYear() - 2 + i;
    return { value: year.toString(), label: year.toString() };
  });

  const fetchData = async () => {
    // For ADMIN, we need month and year
    if (user?.role === "ADMIN" && (!selectedMonth || !selectedYear)) return;
    if (!user) return; // Make sure user is loaded

    setLoading(true);
    try {
      // For ADMIN, fetch data with month and year filters
      // For SUPERVISOR and EMPLOYEE, fetch all data but filter by username
      const isAdmin = user.role === "ADMIN";
      let queryParams = isAdmin ? `?month=${selectedMonth}&year=${selectedYear}` : "";

      // Add username and role to query params for all roles
      // This allows the API to filter data based on the user's role and username
      queryParams += `${queryParams ? "&" : "?"}username=${user.username}&role=${user.role}`;

      // Fetch staff data
      const staffResponse = await fetch(`/api/salary/staff${queryParams}`);

      if (staffResponse.ok) {
        const staffData = await staffResponse.json();
        setStaffData(staffData);
      }

      // Fetch guru data
      const guruResponse = await fetch(`/api/salary/guru${queryParams}`);

      if (guruResponse.ok) {
        const guruData = await guruResponse.json();
        setGuruData(guruData);
      }

      // Fetch honor data
      const honorResponse = await fetch(`/api/salary/honor${queryParams}`);

      if (honorResponse.ok) {
        const honorData = await honorResponse.json();
        setHonorData(honorData);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to fetch salary data',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when month/year changes or on initial load
  useEffect(() => {
    // For ADMIN, fetch data when month/year changes
    // For SUPERVISOR and EMPLOYEE, fetch data on initial load
    if (user?.role === "ADMIN") {
      fetchData();
    }
  }, [selectedMonth, selectedYear, user?.role]);

  // Fetch data on initial load for SUPERVISOR and EMPLOYEE
  useEffect(() => {
    if (user?.role !== "ADMIN") {
      fetchData();
    }
  }, [user?.role]);

  // Handle staff data submission
  const handleStaffSubmit = async (data: any) => {
    try {
      const response = await fetch('/api/salary/staff', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          month: parseInt(selectedMonth),
          year: parseInt(selectedYear),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save staff salary data');
      }

      toast({
        title: "Success",
        description: "Staff salary data saved successfully",
      });

      fetchData();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to save staff salary data',
        variant: "destructive",
      });
    }
  };

  // Handle guru data submission
  const handleGuruSubmit = async (data: any) => {
    try {
      const response = await fetch('/api/salary/guru', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          month: parseInt(selectedMonth),
          year: parseInt(selectedYear),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save guru salary data');
      }

      toast({
        title: "Success",
        description: "Guru salary data saved successfully",
      });

      fetchData();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to save guru salary data',
        variant: "destructive",
      });
    }
  };

  // Handle honor data submission
  const handleHonorSubmit = async (data: any) => {
    try {
      const response = await fetch('/api/salary/honor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          month: parseInt(selectedMonth),
          year: parseInt(selectedYear),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save honor salary data');
      }

      toast({
        title: "Success",
        description: "Honor salary data saved successfully",
      });

      fetchData();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to save honor salary data',
        variant: "destructive",
      });
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          <div className="flex flex-col space-y-8">
            <div className="flex flex-col space-y-2">
              <h1 className="text-3xl font-bold tracking-tight">Salary</h1>
              <p className="text-muted-foreground">
                Manage salary data for staff, guru, and honor
              </p>
            </div>

            {user?.role === "ADMIN" && (
              <div className="flex items-center gap-4 p-4 bg-card rounded-lg shadow">
                <Select
                  value={selectedMonth}
                  onValueChange={setSelectedMonth}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Pilih bulan" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedYear}
                  onValueChange={setSelectedYear}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Pilih tahun" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year.value} value={year.value}>
                        {year.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="staff">Staff</TabsTrigger>
                <TabsTrigger value="guru">Guru</TabsTrigger>
                <TabsTrigger value="honor">Honor</TabsTrigger>
              </TabsList>

              <TabsContent value="staff">
                <StaffTab
                  data={staffData}
                  loading={loading}
                  userRole={user?.role}
                  onSubmit={handleStaffSubmit}
                  onRefresh={fetchData}
                />
              </TabsContent>

              <TabsContent value="guru">
                <GuruTab
                  data={guruData}
                  loading={loading}
                  userRole={user?.role}
                  onSubmit={handleGuruSubmit}
                  onRefresh={fetchData}
                />
              </TabsContent>

              <TabsContent value="honor">
                <HonorTab
                  data={honorData}
                  loading={loading}
                  userRole={user?.role}
                  onSubmit={handleHonorSubmit}
                  onRefresh={fetchData}
                />
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
