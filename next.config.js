/** @type {import('next').NextConfig} */
const nextConfig = {
  // Optimasi compiler
  compiler: {
    // Menghapus semua console.* di production, tetapi mempertahankan console.error
    removeConsole:
      process.env.NODE_ENV === 'production'
        ? {
            exclude: ['error'],
          }
        : false,
    reactRemoveProperties: process.env.NODE_ENV === 'production',
  },

  typescript: {
    ignoreBuildErrors: true,
  },

  // Konfigurasi Turbopack yang dioptimalkan
  experimental: {
    turbo: {
      rules: {
        '*.svg': ['@svgr/webpack'],
      },
      resolveAlias: {
        '@': './src',
      },
    },
    // Fitur experimental yang optimal
    optimizeCss: true,
    scrollRestoration: true,
    serverActions: {
      bodySizeLimit: '2mb',
    },
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
    webVitalsAttribution: ['CLS', 'LCP', 'FID', 'INP'],
  },

  // Optimasi Image yang lebih baik
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
    ],
    minimumCacheTTL: 60,
    deviceSizes: [640, 768, 1024, 1280, 1536],
    imageSizes: [16, 32, 48, 64, 96, 128, 256],
    formats: ['image/webp', 'image/avif'],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Optimasi performa
  httpAgentOptions: {
    keepAlive: true,
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: true,
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // Cache dan watching yang dioptimalkan
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },

  // Optimasi output
  output: 'standalone',
  webpack: (config, { isServer }) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      stream: false,
      crypto: false,
      fs: false,
    };
    return config;
  },
};

module.exports = nextConfig;
