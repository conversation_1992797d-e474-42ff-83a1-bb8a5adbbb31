import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function GET(_request: Request) {
  try {
    // Verify user is authenticated and has ADMIN role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);

    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Get all users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        password: true, // Including password for admin view
        employee: {
          select: {
            firstName: true,
            lastName: true,
            employeeId: true,
            department: true,
            position: true,
          }
        }
      },
      orderBy: {
        username: 'asc'
      }
    });

    // We'll just return the users as they are, with the hashed passwords
    // Bcrypt is a one-way hash function, so we can't decrypt the passwords

    return NextResponse.json({ success: true, data: users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch users' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
