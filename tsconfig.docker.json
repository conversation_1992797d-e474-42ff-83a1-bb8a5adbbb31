{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "importsNotUsedAsValues": "remove", "preserveValueImports": false, "verbatimModuleSyntax": true, "removeComments": true, "noUnusedLocals": false, "noUnusedParameters": false, "allowUnreachableCode": true, "noImplicitAny": false, "noImplicitThis": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}