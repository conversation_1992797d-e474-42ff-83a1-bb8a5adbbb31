/**
 * API Route: GET /api/employees
 *
 * Deskripsi: Mengambil daftar karyawan
 * Penggunaan: Halaman daftar karyawan, dropdown pilihan karyawan
 *
 * Query Parameters:
 * - supervisorId: Filter karyawan berdasarkan supervisor (opsional)
 *
 * Response:
 * - 200: Daftar karyawan
 * - 500: Error server
 */

import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';

export async function GET(_request: NextRequest) {
  try {
    const baseWhereClause = {
      NOT: {
        OR: [
          { employeeId: { startsWith: 'EMP' } }, // Mengecualikan admin web dan semua yang diawali EMP
          { employeeId: { startsWith: 'EXT' } } // Mengecualikan anggota eksternal
        ]
      },
      isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
    };

    // Menggunakan where clause dasar untuk semua query
    const whereClause = baseWhereClause;

    const employees = await prisma.employee.findMany({
      where: whereClause,
      select: {
        id: true,
        employeeId: true,
        firstName: true,
        lastName: true,
        status: true,
        hireDate: true,
        email: true,
        phone: true,
        address: true,
        birthDate: true,
        birthPlace: true,
        gender: true,
        religion: true,
        maritalStatus: true,
        educationLevel: true,
        bankName: true,
        bankAccount: true,
        npwp: true,
        bpjsKesehatan: true,
        bpjsKetenagakerjaan: true,
        contractType: true,
        identityType: true,
        identityNumber: true,
        educationMajor: true,
        educationInstitution: true,
        department: {
          select: {
            id: true,
            name: true,
            head: {
              select: {
                id: true,
                employeeId: true, // Tambahkan employeeId
                firstName: true,
                lastName: true,
                position: {
                  select: {
                    title: true
                  }
                }
              }
            }
          }
        },
        position: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: {
        firstName: 'asc'
      }
    });

    return NextResponse.json(employees);
  } catch (error: any) {
    console.error('Error fetching employees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch employees' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
