/**
 * API Route: GET /api/departments/[id]/get
 *
 * Deskripsi: Mengambil detail departemen berdasarkan ID
 * Penggunaan: Halaman detail departemen, form edit departemen
 *
 * Path Parameters:
 * - id: ID departemen (number)
 *
 * Response:
 * - 200: Detail departemen
 * - 404: Departemen tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Extract ID from URL path
    const id = parseInt(request.nextUrl.pathname.split('/')[3] || '0');

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid department ID' },
        { status: 400 }
      );
    }

    const department = await prisma.department.findUnique({
      where: { id },
      include: {
        head: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            position: {
              select: {
                title: true
              }
            }
          }
        },
        employees: {
          where: {
            // Exclude admin web and external members
            NOT: {
              OR: [
                { employeeId: { startsWith: "EMP" } }, // Mengecualikan admin web dan semua yang diawali EMP
                { employeeId: { startsWith: "EXT" } } // Mengecualikan anggota eksternal
              ]
            },
            isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
          },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            position: {
              select: {
                title: true
              }
            }
          }
        }
      }
    });

    if (!department) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(department);
  } catch (error) {
    console.error('Error fetching department:', error);
    return NextResponse.json(
      { error: 'Failed to fetch department' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
