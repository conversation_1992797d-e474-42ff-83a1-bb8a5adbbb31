-- Seed data for EMS database

-- Insert Departments
INSERT INTO departments (id, name, description, created_at, updated_at)
VALUES 
  (1, 'Information Technology', 'IT Department', NOW(), NOW()),
  (2, 'Human Resources', 'HR Department', NOW(), NOW())
ON DUPLICATE KEY UPDATE
  name = VALUES(name),
  description = VALUES(description),
  updated_at = NOW();

-- Insert Positions
INSERT INTO positions (id, title, description, department_id, created_at, updated_at)
VALUES 
  (1, 'Manager', 'Department Manager', 1, NOW(), NOW()),
  (2, 'Staff', 'Regular Staff', 2, NOW(), NOW())
ON DUPLICATE KEY UPDATE
  title = VALUES(title),
  description = VALUES(description),
  department_id = VALUES(department_id),
  updated_at = NOW();

-- Insert Employees
INSERT INTO employees (id, employee_id, first_name, last_name, email, phone, hire_date, department_id, position_id, status, created_at, updated_at)
VALUES 
  (1, 'EMP001', '<PERSON>', NULL, '<EMAIL>', '08123456789', '2024-01-01', 1, 1, 'Tetap', NOW(), NOW()),
  (2, 'EMP002', 'Sarah', NULL, '<EMAIL>', '08234567890', '2024-01-02', 2, 2, 'Kontrak', NOW(), NOW())
ON DUPLICATE KEY UPDATE
  first_name = VALUES(first_name),
  last_name = VALUES(last_name),
  email = VALUES(email),
  phone = VALUES(phone),
  hire_date = VALUES(hire_date),
  department_id = VALUES(department_id),
  position_id = VALUES(position_id),
  status = VALUES(status),
  updated_at = NOW();

-- Insert Users (password for admin123 is $2a$10$JqJhYfB.8TSU/CaLgRdQOeXE5MccqiG9AIUmWEyU8zcJcq9.Yvpoy)
-- password for super123 is $2a$10$JqJhYfB.8TSU/CaLgRdQOeXE5MccqiG9AIUmWEyU8zcJcq9.Yvpoy
INSERT INTO users (id, username, email, password, role, employee_id, last_login, is_active, created_at, updated_at)
VALUES 
  (1, 'admin', '<EMAIL>', '$2a$10$JqJhYfB.8TSU/CaLgRdQOeXE5MccqiG9AIUmWEyU8zcJcq9.Yvpoy', 'ADMIN', 1, NOW(), 1, NOW(), NOW()),
  (2, 'supervisor', '<EMAIL>', '$2a$10$JqJhYfB.8TSU/CaLgRdQOeXE5MccqiG9AIUmWEyU8zcJcq9.Yvpoy', 'SUPERVISOR', 2, NOW(), 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE
  email = VALUES(email),
  role = VALUES(role),
  is_active = VALUES(is_active),
  updated_at = NOW();
