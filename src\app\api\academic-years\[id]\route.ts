/**
 * API Route: /api/academic-years/[id]
 *
 * Deskripsi: Endpoint untuk mengelola tahun akademik berdasarkan ID
 * Penggunaan: Halaman pengaturan tahun akademik
 *
 * GET: Mengambil detail tahun akademik
 * PUT: Memperbarui tahun akademik
 * DELETE: Menghapus tahun akademik
 *
 * Response:
 * - 200: Sukses
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Tahun akademik tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { logger } from '@/lib/logger';

// GET: Mengambil detail tahun akademik
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only ADMIN can access this resource' },
        { status: 403 }
      );
    }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      );
    }

    // Ambil tahun akademik berdasarkan ID
    const academicYear = await prisma.academicYear.findUnique({
      where: { id },
      include: {
        employees: {
          select: {
            id: true,
            employeeId: true,
            firstName: true,
            lastName: true,
            isDeleted: true
          }
        }
      }
    });

    if (!academicYear) {
      return NextResponse.json(
        { error: 'Academic year not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(academicYear);
  } catch (error: any) {
    logger.error('API - Error fetching academic year:', error);
    return NextResponse.json(
      { error: 'Failed to fetch academic year' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// PUT: Memperbarui tahun akademik
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only ADMIN can access this resource' },
        { status: 403 }
      );
    }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      );
    }

    // Cek apakah tahun akademik ada
    const existingAcademicYear = await prisma.academicYear.findUnique({
      where: { id }
    });

    if (!existingAcademicYear) {
      return NextResponse.json(
        { error: 'Academic year not found' },
        { status: 404 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { ta, description } = body;

    // Validasi data
    if (!ta) {
      return NextResponse.json(
        { error: 'Academic year (TA) is required' },
        { status: 400 }
      );
    }

    // Cek apakah tahun akademik dengan TA yang sama sudah ada (kecuali untuk ID yang sama)
    const duplicateTA = await prisma.academicYear.findFirst({
      where: {
        ta,
        NOT: {
          id
        }
      }
    });

    if (duplicateTA) {
      return NextResponse.json(
        { error: 'Academic year with this TA already exists' },
        { status: 400 }
      );
    }

    // Update tahun akademik
    const updatedAcademicYear = await prisma.academicYear.update({
      where: { id },
      data: {
        ta,
        description
      }
    });

    return NextResponse.json(updatedAcademicYear);
  } catch (error: any) {
    logger.error('API - Error updating academic year:', error);
    return NextResponse.json(
      { error: 'Failed to update academic year' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// DELETE: Menghapus tahun akademik
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only ADMIN can access this resource' },
        { status: 403 }
      );
    }

    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      );
    }

    // Cek apakah tahun akademik ada
    const academicYear = await prisma.academicYear.findUnique({
      where: { id },
      include: {
        employees: true
      }
    });

    if (!academicYear) {
      return NextResponse.json(
        { error: 'Academic year not found' },
        { status: 404 }
      );
    }

    // Cek apakah tahun akademik memiliki karyawan terkait
    if (academicYear.employees.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete academic year with associated employees' },
        { status: 400 }
      );
    }

    // Hapus tahun akademik
    await prisma.academicYear.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Academic year deleted successfully' });
  } catch (error: any) {
    logger.error('API - Error deleting academic year:', error);
    return NextResponse.json(
      { error: 'Failed to delete academic year' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
