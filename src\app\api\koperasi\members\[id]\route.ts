/**
 * API Route: /api/koperasi/members/[id]
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { NextRequest } from 'next/server';

// GET handler for retrieving a specific koperasi member
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Extract ID from URL params
    const id = parseInt(params.id);

    const member = await prisma.koperasiMember.findUnique({
      where: { id },
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            department: {
              select: {
                name: true
              }
            },
            position: {
              select: {
                title: true
              }
            }
          }
        }
      }
    });

    if (!member) {
      return NextResponse.json({ error: 'Koperasi member not found' }, { status: 404 });
    }

    return NextResponse.json(member);
  } catch (error) {
    console.error('Error retrieving koperasi member:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve koperasi member' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// PUT handler for updating a koperasi member
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Extract ID from URL params
    const id = parseInt(params.id);
    const data = await request.json();
    console.log('Received data for update:', data);

    const {
      joinDate,
      monthlyContribution,
      oneTimeContribution,
      optionalContribution,
      status,
      notes
    } = data;

    // Check if member exists
    const member = await prisma.koperasiMember.findUnique({
      where: { id }
    });

    if (!member) {
      return NextResponse.json({ error: 'Koperasi member not found' }, { status: 404 });
    }

    // Update member
    const updateData: Record<string, any> = {};

    // Perbarui join date jika ada
    if (joinDate !== undefined) {
      try {
        // Konversi string ke Date object jika perlu
        const parsedDate = typeof joinDate === 'string' ? new Date(joinDate) : joinDate;
        if (!isNaN(parsedDate.getTime())) {
          updateData.joinDate = parsedDate;
          console.log('Updating join date to:', parsedDate);
        } else {
          console.error('Invalid join date format:', joinDate);
        }
      } catch (error) {
        console.error('Error parsing join date:', error);
      }
    }

    if (monthlyContribution !== undefined) {
      updateData.monthlyContribution = parseFloat(monthlyContribution);
    }

    if (oneTimeContribution !== undefined) {
      updateData.oneTimeContribution = parseFloat(oneTimeContribution);
    }

    if (optionalContribution !== undefined) {
      updateData.optionalContribution = parseFloat(optionalContribution);
    }

    if (status !== undefined) {
      updateData.status = status;
    }

    if (notes !== undefined) {
      updateData.notes = notes;
    }

    updateData.updatedAt = new Date();

    // Hitung total savings berdasarkan kontribusi yang diperbarui
    const currentMember = await prisma.koperasiMember.findUnique({
      where: { id }
    });

    if (currentMember) {
      // Gunakan nilai yang diperbarui jika ada, jika tidak gunakan nilai yang ada
      const newMonthlyContribution = monthlyContribution !== undefined
        ? parseFloat(monthlyContribution)
        : Number(currentMember.monthlyContribution);

      const newOneTimeContribution = oneTimeContribution !== undefined
        ? parseFloat(oneTimeContribution)
        : (currentMember.oneTimeContribution ? Number(currentMember.oneTimeContribution) : 0);

      const newOptionalContribution = optionalContribution !== undefined
        ? parseFloat(optionalContribution)
        : (currentMember.optionalContribution ? Number(currentMember.optionalContribution) : 0);

      // Hitung total savings baru
      updateData.totalSavings = newMonthlyContribution + newOneTimeContribution + newOptionalContribution;

      console.log('Total savings calculation:');
      console.log('- Monthly Contribution:', newMonthlyContribution);
      console.log('- One Time Contribution:', newOneTimeContribution);
      console.log('- Optional Contribution:', newOptionalContribution);
      console.log('= Total Savings:', updateData.totalSavings);
    }

    const updatedMember = await prisma.koperasiMember.update({
      where: { id },
      data: updateData,
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            department: {
              select: {
                name: true
              }
            },
            position: {
              select: {
                title: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json(updatedMember);
  } catch (error) {
    console.error('Error updating koperasi member:', error);
    console.log('Request URL:', request.url);
    console.log('Member ID:', params.id);
    return NextResponse.json(
      { error: 'Failed to update koperasi member', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// DELETE handler for deleting a koperasi member
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Extract ID from URL params
    const id = parseInt(params.id);

    // Check if member exists
    const member = await prisma.koperasiMember.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            savings: true,
            loans: true
          }
        }
      }
    });

    if (!member) {
      return NextResponse.json({ error: 'Koperasi member not found' }, { status: 404 });
    }

    // Check if member has savings or loans
    if (member._count.savings > 0 || member._count.loans > 0) {
      return NextResponse.json(
        { error: 'Cannot delete member with savings or loans' },
        { status: 409 }
      );
    }

    // Delete member
    await prisma.koperasiMember.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Koperasi member deleted successfully' });
  } catch (error) {
    console.error('Error deleting koperasi member:', error);
    return NextResponse.json(
      { error: 'Failed to delete koperasi member' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
