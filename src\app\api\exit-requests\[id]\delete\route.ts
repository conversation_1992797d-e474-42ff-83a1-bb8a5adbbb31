/**
 * API Route: DELETE /api/exit-requests/[id]/delete
 *
 * Deskripsi: Menghapus permintaan keluar berdasarkan ID
 * Penggunaan: Tombol hapus di halaman daftar permintaan keluar
 *
 * Path Parameters:
 * - id: ID permintaan keluar (number)
 *
 * Response:
 * - 200: Permintaan keluar berhasil dihapus
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Permintaan keluar tidak ditemukan
 * - 409: Permintaan keluar tidak dapat dihapus (status bukan pending)
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

export async function DELETE(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const isAdmin = userData.role === 'ADMIN';

    // Get ID from params
    const id = params.id;

    // Check if exit request exists
    const exitRequest = await prisma.exitRequest.findUnique({
      where: { id: parseInt(id) },
    });

    if (!exitRequest) {
      return NextResponse.json({ error: 'Exit request not found' }, { status: 404 });
    }

    // Only admin can delete any exit request
    // Regular users can only delete their own pending requests
    if (!isAdmin) {
      if (exitRequest.employeeId !== parseInt(userData.id)) {
        return NextResponse.json(
          { error: 'You can only delete your own exit requests' },
          { status: 403 }
        );
      }

      if (exitRequest.status !== 'pending') {
        return NextResponse.json(
          { error: 'You can only delete pending exit requests' },
          { status: 409 }
        );
      }
    }

    // Delete exit request
    await prisma.exitRequest.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Exit request deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting exit request:', error);
    return NextResponse.json(
      { error: 'Failed to delete exit request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
