/**
 * API Route: POST /api/departments/create
 *
 * Deskripsi: Membuat departemen baru
 * Penggunaan: Form tambah departemen
 *
 * Body:
 * - name: <PERSON><PERSON> departemen (string)
 * - description: <PERSON><PERSON><PERSON><PERSON> departemen (string, opsional)
 * - head: ID kepala departemen (string, 'none' jika tidak ada)
 *
 * Response:
 * - 201: Departemen berhasil dibuat
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = JSON.parse(userCookie.value);
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validasi nama departemen
    if (!data.name || data.name.trim() === '') {
      return NextResponse.json(
        { error: 'Department name is required' },
        { status: 400 }
      );
    }

    // Cek apakah departemen dengan nama yang sama sudah ada
    const existingDepartment = await prisma.department.findFirst({
      where: { name: data.name }
    });

    if (existingDepartment) {
      return NextResponse.json(
        { error: 'Department with this name already exists' },
        { status: 400 }
      );
    }

    const department = await prisma.department.create({
      data: {
        name: data.name,
        description: data.description,
        headId: data.head === 'none' ? null : parseInt(data.head)
      }
    });

    return NextResponse.json(department, { status: 201 });
  } catch (error) {
    console.error('Error creating department:', error);
    return NextResponse.json(
      { error: 'Failed to create department' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
