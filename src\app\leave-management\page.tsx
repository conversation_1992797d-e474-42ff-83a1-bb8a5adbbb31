"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useAuth } from "@/lib/auth";
import Navbar from "@/components/layout/Navbar";
import { logger } from "@/lib/logger";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  CalendarIcon,
  Clock,
  FileText,
  BarChart,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { LeaveRequestsTab, LateRequestsTab, ExitRequestsTab, CalendarTab, LeaveTypesTab, ReportsTab } from "@/components/leave-management";

// Helper function to convert time string (HH:MM) to minutes
function convertTimeToMinutes(timeStr: string): number {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

// Helper function to calculate working days between two dates (excluding weekends)
function calculateWorkingDays(startDate: Date, endDate: Date): number {
  let count = 0;
  const curDate = new Date(startDate.getTime());

  while (curDate <= endDate) {
    const dayOfWeek = curDate.getDay();
    // Skip Saturday (6) and Sunday (0)
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      count++;
    }
    curDate.setDate(curDate.getDate() + 1);
  }

  return count;
}

export default function LeaveManagementPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const userRole = user?.role;

  // State untuk data
  const [leaveRequests, setLeaveRequests] = useState<any[]>([]);
  const [lateRequests, setLateRequests] = useState<any[]>([]);
  const [exitRequests, setExitRequests] = useState<any[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<any[]>([]);
  const [leaveEvents, setLeaveEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // State untuk statistik
  const [stats, setStats] = useState({
    pendingLeaveRequests: 0,
    pendingLateRequests: 0,
    pendingExitRequests: 0,
  });

  // State untuk dialog
  const [isViewLeaveRequestOpen, setIsViewLeaveRequestOpen] = useState(false);
  const [isEditLeaveRequestOpen, setIsEditLeaveRequestOpen] = useState(false);
  const [isAddLeaveRequestOpen, setIsAddLeaveRequestOpen] = useState(false);
  const [selectedLeaveRequest, setSelectedLeaveRequest] = useState<any>(null);
  const [selectedEditFile, setSelectedEditFile] = useState<File | null>(null);

  const [isViewLateRequestOpen, setIsViewLateRequestOpen] = useState(false);
  const [isEditLateRequestOpen, setIsEditLateRequestOpen] = useState(false);
  const [isAddLateRequestOpen, setIsAddLateRequestOpen] = useState(false);
  const [selectedLateRequest, setSelectedLateRequest] = useState<any>(null);

  const [isViewExitRequestOpen, setIsViewExitRequestOpen] = useState(false);
  const [isEditExitRequestOpen, setIsEditExitRequestOpen] = useState(false);
  const [isAddExitRequestOpen, setIsAddExitRequestOpen] = useState(false);
  const [selectedExitRequest, setSelectedExitRequest] = useState<any>(null);

  // State untuk form
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedLateFile, setSelectedLateFile] = useState<File | null>(null);
  const [selectedEditLateFile, setSelectedEditLateFile] = useState<File | null>(null);
  const [leaveRequestForm, setLeaveRequestForm] = useState({
    employeeId: '',
    leaveTypeId: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    reason: '',
    attachment: null as File | null,
  });

  // Set employeeId default untuk non-ADMIN users
  useEffect(() => {
    if (user) {
      // Menghapus log sensitif user object dan role
      logger.debug('User authenticated for leave management');

      if (userRole !== 'ADMIN') {
        // Cek struktur user object untuk menemukan employeeId
        const employeeId = user.employeeId || user.id;
        if (employeeId) {
          logger.debug('Setting default employeeId for non-ADMIN user');
          // Set employeeId untuk leave request form
          setLeaveRequestForm(prev => ({
            ...prev,
            employeeId: employeeId.toString()
          }));

          // Set employeeId untuk late request form
          setLateRequestForm(prev => ({
            ...prev,
            employeeId: employeeId.toString()
          }));

          // Set employeeId untuk exit request form
          setExitRequestForm(prev => ({
            ...prev,
            employeeId: employeeId.toString()
          }));
        } else {
          console.warn('No employeeId found for user:', user);
        }
      }
    }
  }, [user, userRole]);

  const [editLeaveForm, setEditLeaveForm] = useState({
    id: 0,
    leaveTypeId: '',
    startDate: '',
    endDate: '',
    reason: '',
    attachmentUrl: '',
  });

  const [lateRequestForm, setLateRequestForm] = useState({
    employeeId: '',
    lateType: '',
    lateDate: new Date().toISOString().split('T')[0],
    estimatedTime: '08:00',
    reason: '',
    attachment: null as File | null,
  });

  const [editLateForm, setEditLateForm] = useState({
    id: 0,
    lateType: '',
    lateDate: '',
    estimatedTime: '',
    reason: '',
    attachmentUrl: '',
    attachment: null as File | null,
  });

  const [exitRequestForm, setExitRequestForm] = useState({
    employeeId: '',
    exitType: '',
    exitDate: new Date().toISOString().split('T')[0],
    exitTime: '08:00',
    comebackTime: '16:00',
    notComeback: false,
    reason: ''
  });

  const [editExitForm, setEditExitForm] = useState({
    id: 0,
    exitType: '',
    exitDate: '',
    exitTime: '',
    comebackTime: '',
    notComeback: false,
    reason: '',
  });

  const [employees, setEmployees] = useState<any[]>([]);

  // State untuk leave type sudah diimplementasikan di LeaveTypesTab.tsx

  // Filter late requests berdasarkan departemen yang dikepalai oleh supervisor atau posisi Chief/Kepala untuk HEAD
  const filteredLateRequests = useMemo(() => {
    let filtered = lateRequests;

    if (userRole === 'SUPERVISOR' && user) {

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      filtered = lateRequests.filter(request => {
        // Cek apakah request.employee.department.head.employeeId === user.id atau user.employeeId
        const isEmployeeUnderSupervisor =
          request.employee?.department?.head?.employeeId === user?.id ||
          request.employee?.department?.head?.employeeId === user?.employeeId ||
          request.employee?.department?.head?.id === parseInt(user?.id || '0') ||
          request.employee?.department?.head?.id === parseInt(user?.employeeId || '0');

        if (isEmployeeUnderSupervisor) {
          console.log('Found late request under supervisor:',
            `${request.employee.firstName} ${request.employee.lastName} in ${request.employee.department?.name}`);
        }

        return isEmployeeUnderSupervisor ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name);
      });

      console.log('Late requests after filter:', filtered.length);
    } else if (userRole === 'HEAD' && user) {
      // Filter berdasarkan posisi Chief atau Kepala untuk HEAD
      filtered = lateRequests.filter(request => {
        // Cek apakah posisi karyawan mengandung "Chief", "Kepala", atau "Psikolog"
        const positionTitle = request.employee?.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        if (isChiefOrKepalaOrPsikolog) {
          console.log('Found late request for Chief/Kepala/Psikolog:',
            `${request.employee.firstName} ${request.employee.lastName} with position ${positionTitle}`);
        }

        return isChiefOrKepalaOrPsikolog ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name);
      });

      console.log('Late requests after filter for HEAD:', filtered.length);
    }

    return filtered;
  }, [lateRequests, userRole, user]);

  // Filter exit requests berdasarkan departemen yang dikepalai oleh supervisor atau posisi Chief/Kepala untuk HEAD
  const filteredExitRequests = useMemo(() => {
    let filtered = exitRequests;

    if (userRole === 'SUPERVISOR' && user) {
      console.log('Filtering exit requests for SUPERVISOR:', user.id);
      console.log('Available exit requests before filter:', exitRequests.length);

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      filtered = exitRequests.filter(request => {
        // Cek apakah request.employee.department.head.employeeId === user.id atau user.employeeId
        const isEmployeeUnderSupervisor =
          request.employee?.department?.head?.employeeId === user?.id ||
          request.employee?.department?.head?.employeeId === user?.employeeId ||
          request.employee?.department?.head?.id === parseInt(user?.id || '0') ||
          request.employee?.department?.head?.id === parseInt(user?.employeeId || '0');

        if (isEmployeeUnderSupervisor) {
          console.log('Found exit request under supervisor:',
            `${request.employee.firstName} ${request.employee.lastName} in ${request.employee.department?.name}`);
        }

        return isEmployeeUnderSupervisor ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name);
      });

      console.log('Exit requests after filter:', filtered.length);
    } else if (userRole === 'HEAD' && user) {
      console.log('Filtering exit requests for HEAD:', user.id);
      console.log('Available exit requests before filter:', exitRequests.length);

      // Filter berdasarkan posisi Chief atau Kepala untuk HEAD
      filtered = exitRequests.filter(request => {
        // Cek apakah posisi karyawan mengandung "Chief", "Kepala", atau "Psikolog"
        const positionTitle = request.employee?.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        if (isChiefOrKepalaOrPsikolog) {
          console.log('Found exit request for Chief/Kepala/Psikolog:',
            `${request.employee.firstName} ${request.employee.lastName} with position ${positionTitle}`);
        }

        return isChiefOrKepalaOrPsikolog ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name);
      });

      console.log('Exit requests after filter for HEAD:', filtered.length);
    }

    return filtered;
  }, [exitRequests, userRole, user]);

  // Format leave requests untuk tampilan tabel
  const formattedLeaveRequests = useMemo(() => {
    // Filter leave requests berdasarkan departemen yang dikepalai oleh supervisor atau posisi Chief/Kepala untuk HEAD
    let filteredRequests = leaveRequests;

    if (userRole === 'SUPERVISOR' && user) {
      console.log('Filtering leave requests for SUPERVISOR:', user.id);
      console.log('Available leave requests before filter:', leaveRequests.length);

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      filteredRequests = leaveRequests.filter(request => {
        // Cek apakah request.employee.department.head.employeeId === user.id atau user.employeeId
        const isEmployeeUnderSupervisor =
          request.employee?.department?.head?.employeeId === user?.id ||
          request.employee?.department?.head?.employeeId === user?.employeeId ||
          request.employee?.department?.head?.id === parseInt(user?.id || '0') ||
          request.employee?.department?.head?.id === parseInt(user?.employeeId || '0');

        if (isEmployeeUnderSupervisor) {
          console.log('Found leave request under supervisor:',
            `${request.employee.firstName} ${request.employee.lastName} in ${request.employee.department?.name}`);
        }

        return isEmployeeUnderSupervisor ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name);
      });

      console.log('Leave requests after filter:', filteredRequests.length);
    } else if (userRole === 'HEAD' && user) {
      console.log('Filtering leave requests for HEAD:', user.id);
      console.log('Available leave requests before filter:', leaveRequests.length);

      // Filter berdasarkan posisi Chief atau Kepala untuk HEAD
      filteredRequests = leaveRequests.filter(request => {
        // Cek apakah posisi karyawan mengandung "Chief", "Kepala", atau "Psikolog"
        const positionTitle = request.employee?.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        if (isChiefOrKepalaOrPsikolog) {
          console.log('Found leave request for Chief/Kepala/Psikolog:',
            `${request.employee.firstName} ${request.employee.lastName} with position ${positionTitle}`);
        }

        return isChiefOrKepalaOrPsikolog ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name);
      });

      console.log('Leave requests after filter for HEAD:', filteredRequests.length);
    }

    return filteredRequests.map(request => {
      // Pastikan tanggal diformat dengan benar
      const fromDate = new Date(request.startDate);
      fromDate.setHours(0, 0, 0, 0);

      const toDate = new Date(request.endDate);
      toDate.setHours(0, 0, 0, 0);

      return {
        id: `LV${String(request.id).padStart(3, '0')}`,
        employee: request.employee ? `${request.employee.firstName} ${request.employee.lastName}` : 'Unknown',
        type: request.leaveType?.name || 'Unknown',
        from: fromDate,
        to: toDate,
        status: request.status,
        originalId: request.id,
        department: request.employee?.department?.name || 'Unknown'
      };
    });
  }, [leaveRequests, userRole, user]);

  // Function to check if a date has events with specific status
  const isDayWithApprovedEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'approved';
    });
  };

  const isDayWithPendingEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'pending';
    });
  };

  const isDayWithRejectedEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'rejected';
    });
  };

  // Custom modifiers for the calendar
  const modifiers = {
    hasApprovedEvent: (date: Date) => isDayWithApprovedEvent(date),
    hasPendingEvent: (date: Date) => isDayWithPendingEvent(date),
    hasRejectedEvent: (date: Date) => isDayWithRejectedEvent(date),
  };

  // Custom modifiers styles
  const modifiersStyles = {
    hasApprovedEvent: {
      backgroundColor: "rgba(34, 197, 94, 0.2)", // Green for approved
      borderRadius: "0",
    },
    hasPendingEvent: {
      backgroundColor: "rgba(234, 179, 8, 0.2)", // Yellow for pending
      borderRadius: "0",
    },
    hasRejectedEvent: {
      backgroundColor: "rgba(239, 68, 68, 0.2)", // Red for rejected
      borderRadius: "0",
    }
  };

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchLeaveRequests(),
          fetchLateRequests(),
          fetchExitRequests(),
          fetchLeaveTypes(),
          fetchEmployees()
        ]);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: "Error",
          description: "Failed to fetch data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch leave requests
  const fetchLeaveRequests = async () => {
    try {
      const response = await fetch('/api/leave-management');
      if (!response.ok) throw new Error('Failed to fetch leave requests');

      const data = await response.json();
      console.log('Leave requests data:', data);
      console.log(`Received ${data.length} leave requests from API`);

      // Periksa apakah ada leave requests dengan status pending (kode ini sudah tidak digunakan)
      // Statistik akan dihitung di bagian bawah

      if (userRole === 'SUPERVISOR') {
        console.log('SUPERVISOR: Checking if leave requests are filtered correctly');
        const uniqueEmployees = new Set(data.map((req: any) =>
          req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : 'Unknown'
        ));
        console.log('Unique employees in leave requests:', Array.from(uniqueEmployees));
      }

      setLeaveRequests(data);

      // Format data untuk calendar
      // Filter data untuk supervisor
      let filteredData = data;

      if (userRole === 'SUPERVISOR' && user) {
        console.log('Filtering calendar events for SUPERVISOR:', user.id);

        // Filter berdasarkan departemen yang dikepalai oleh supervisor
        filteredData = data.filter((request: any) => {
          // Cek apakah request.employee.department.head.employeeId === user.id atau user.employeeId
          const isEmployeeUnderSupervisor =
            request.employee?.department?.head?.employeeId === user?.id ||
            request.employee?.department?.head?.employeeId === user?.employeeId ||
            request.employee?.department?.head?.id === parseInt(user?.id || '0') ||
            request.employee?.department?.head?.id === parseInt(user?.employeeId || '0');

          // Juga tampilkan permintaan dari supervisor sendiri
          const isOwnRequest =
            request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name;

          return isEmployeeUnderSupervisor || isOwnRequest;
        });

        console.log('Calendar events after filter:', filteredData.length);
      } else if (userRole === 'HEAD' && user) {
        console.log('Filtering calendar events for HEAD:', user.id);

        // Filter berdasarkan posisi Chief atau Kepala untuk HEAD
        filteredData = data.filter((request: any) => {
          // Cek apakah posisi karyawan mengandung "Chief", "Kepala", atau "Psikolog"
          const positionTitle = request.employee?.position?.title || '';
          const isChiefOrKepalaOrPsikolog =
            positionTitle.toLowerCase().includes('chief') ||
            positionTitle.toLowerCase().includes('kepala') ||
            positionTitle.toLowerCase().includes('psikolog');

          // Juga tampilkan permintaan dari HEAD sendiri
          const isOwnRequest =
            request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user?.name;

          return isChiefOrKepalaOrPsikolog || isOwnRequest;
        });

        console.log('Calendar events after filter for HEAD:', filteredData.length);
      }

      // Format data untuk calendar
      console.log('Raw leave requests data:', JSON.stringify(data, null, 2));

      // Format data leave requests untuk calendar
      const formattedEvents = data.map((request: any) => {
        // Pastikan tanggal diformat dengan benar
        console.log('Processing request for calendar:', request);

        // Gunakan tanggal saat ini jika startDate atau endDate tidak ada
        const startDate = request.startDate || new Date().toISOString();
        const endDate = request.endDate || new Date().toISOString();

        const fromDate = new Date(startDate);
        fromDate.setHours(0, 0, 0, 0);

        const toDate = new Date(endDate);
        toDate.setHours(0, 0, 0, 0);

        const formattedEvent = {
          id: `LV${String(request.id).padStart(3, '0')}`,
          employee: request.employee ? `${request.employee.firstName} ${request.employee.lastName}` : 'Unknown',
          type: request.leaveType?.name || 'Unknown',
          from: fromDate,
          to: toDate,
          status: request.status || 'pending'
        };

        console.log('Formatted event:', formattedEvent);
        return formattedEvent;
      });

      console.log('Using real events for calendar:', formattedEvents);
      setLeaveEvents(formattedEvents);

      // Gunakan data langsung tanpa filtering untuk calendar (commented out for now)
      /*
      // Kode yang dikomentari
      */

      // Hitung jumlah leave requests yang pending
      console.log('Raw leave requests data for stats:', data);
      console.log('Filtered leave requests data for stats:', filteredData);

      // Hitung jumlah leave requests dengan status pending
      const pendingLeaveRequestsCount = data.filter((req: any) => {
        console.log('Leave request status:', req.status, typeof req.status);
        return req.status === 'Pending' || req.status === 'pending';
      }).length;
      console.log(`Found ${pendingLeaveRequestsCount} pending leave requests in raw data`);

      // Set statistik untuk leave requests
      setStats(prev => {
        const newStats = { ...prev, pendingLeaveRequests: pendingLeaveRequestsCount };
        console.log('Updated stats for leave requests:', newStats);
        return newStats;
      });
    } catch (error) {
      console.error('Error fetching leave requests:', error);
      toast({
        title: "Error",
        description: "Failed to fetch leave requests",
        variant: "destructive",
      });
    }
  };

  // Fetch late requests
  const fetchLateRequests = async () => {
    try {
      const response = await fetch('/api/late-requests');
      if (!response.ok) throw new Error('Failed to fetch late requests');

      const data = await response.json();
      console.log('Late requests data:', data);
      console.log(`Received ${data.length} late requests from API`);

      // Periksa apakah ada late requests dengan status pending
      const pendingLateRequestsCount = data.filter((req: any) => {
        console.log('Late request status:', req.status, typeof req.status);
        return req.status === 'Pending' || req.status === 'pending';
      }).length;
      console.log(`Found ${pendingLateRequestsCount} pending late requests in raw data`);

      // Set statistik untuk late requests
      setStats(prev => {
        const newStats = { ...prev, pendingLateRequests: pendingLateRequestsCount };
        console.log('Updated stats for late requests:', newStats);
        return newStats;
      });

      if (userRole === 'SUPERVISOR') {
        console.log('SUPERVISOR: Checking if late requests are filtered correctly');
        const uniqueEmployees = new Set(data.map((req: any) =>
          req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : 'Unknown'
        ));
        console.log('Unique employees in late requests:', Array.from(uniqueEmployees));
      }

      setLateRequests(data);

      // Hitung jumlah late requests yang pending
      let pendingLateRequests;
      if (userRole === 'ADMIN') {
        // Admin melihat semua permintaan pending
        pendingLateRequests = data.filter((req: any) => req.status === 'Pending' || req.status === 'pending');
      } else if (userRole === 'SUPERVISOR') {
        // Filter data berdasarkan departemen yang dikepalai oleh supervisor
        const filteredData = data.filter((req: any) => {
          // Cek apakah req.employee.department.head.employeeId === user.id atau user.employeeId
          const isEmployeeUnderSupervisor =
            req.employee?.department?.head?.employeeId === user?.id ||
            req.employee?.department?.head?.employeeId === user?.employeeId ||
            req.employee?.department?.head?.id === parseInt(user?.id || '0') ||
            req.employee?.department?.head?.id === parseInt(user?.employeeId || '0');

          // Juga tampilkan permintaan dari supervisor sendiri
          const isOwnRequest =
            req.employee && `${req.employee.firstName} ${req.employee.lastName}` === user?.name;

          return isEmployeeUnderSupervisor || isOwnRequest;
        });

        // Hitung jumlah permintaan pending dari data yang sudah difilter
        pendingLateRequests = filteredData.filter((req: any) =>
          (req.status === 'pending' || req.status === 'Pending') &&
          req.employee &&
          `${req.employee.firstName} ${req.employee.lastName}` !== user?.name
        );
      } else {
        // Employee tidak melihat statistik (set ke 0)
        pendingLateRequests = [];
      }

      setStats(prev => ({ ...prev, pendingLateRequests: pendingLateRequests.length }));
      console.log('Pending late requests:', pendingLateRequests.length);
    } catch (error) {
      console.error('Error fetching late requests:', error);
      toast({
        title: "Error",
        description: "Failed to fetch late requests",
        variant: "destructive",
      });
    }
  };

  // Fetch exit requests
  const fetchExitRequests = async () => {
    try {
      const response = await fetch('/api/exit-requests');
      if (!response.ok) throw new Error('Failed to fetch exit requests');

      const data = await response.json();
      console.log('Exit requests data:', data);

      // Pastikan data adalah array
      const exitRequestsArray = Array.isArray(data) ? data : [];
      console.log(`Received ${exitRequestsArray.length} exit requests from API`);

      // Periksa apakah ada exit requests dengan status pending
      const pendingExitRequestsCount = exitRequestsArray.filter((req: any) => {
        console.log('Exit request status:', req.status, typeof req.status);
        return req.status === 'Pending' || req.status === 'pending';
      }).length;
      console.log(`Found ${pendingExitRequestsCount} pending exit requests in raw data`);

      // Set statistik untuk exit requests
      setStats(prev => {
        const newStats = { ...prev, pendingExitRequests: pendingExitRequestsCount };
        console.log('Updated stats for exit requests:', newStats);
        return newStats;
      });

      if (userRole === 'SUPERVISOR') {
        console.log('SUPERVISOR: Checking if exit requests are filtered correctly');
        const uniqueEmployees = new Set(exitRequestsArray.map((req: any) =>
          req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : 'Unknown'
        ));
        console.log('Unique employees in exit requests:', Array.from(uniqueEmployees));
      }

      setExitRequests(exitRequestsArray);

      // Hitung jumlah exit requests yang pending
      let pendingExitRequests;
      if (userRole === 'ADMIN') {
        // Admin melihat semua permintaan pending
        pendingExitRequests = exitRequestsArray.filter((req: any) => req.status === 'pending' || req.status === 'Pending');
      } else if (userRole === 'SUPERVISOR') {
        // Filter data berdasarkan departemen yang dikepalai oleh supervisor
        const filteredData = exitRequestsArray.filter((req: any) => {
          // Cek apakah req.employee.department.head.employeeId === user.id atau user.employeeId
          const isEmployeeUnderSupervisor =
            req.employee?.department?.head?.employeeId === user?.id ||
            req.employee?.department?.head?.employeeId === user?.employeeId ||
            req.employee?.department?.head?.id === parseInt(user?.id || '0') ||
            req.employee?.department?.head?.id === parseInt(user?.employeeId || '0');

          // Juga tampilkan permintaan dari supervisor sendiri
          const isOwnRequest =
            req.employee && `${req.employee.firstName} ${req.employee.lastName}` === user?.name;

          return isEmployeeUnderSupervisor || isOwnRequest;
        });

        // Hitung jumlah permintaan pending dari data yang sudah difilter
        pendingExitRequests = filteredData.filter((req: any) =>
          (req.status === 'pending' || req.status === 'Pending') &&
          req.employee &&
          `${req.employee.firstName} ${req.employee.lastName}` !== user?.name
        );
      } else {
        // Employee tidak melihat statistik (set ke 0)
        pendingExitRequests = [];
      }

      setStats(prev => ({ ...prev, pendingExitRequests: pendingExitRequests.length }));
      console.log('Pending exit requests:', pendingExitRequests.length);
    } catch (error) {
      console.error('Error fetching exit requests:', error);
      toast({
        title: "Error",
        description: "Failed to fetch exit requests",
        variant: "destructive",
      });
    }
  };

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      const response = await fetch('/api/leave-types');
      if (!response.ok) throw new Error('Failed to fetch leave types');
      const data = await response.json();
      setLeaveTypes(data);
    } catch (error) {
      console.error('Error fetching leave types:', error);
      toast({
        title: "Error",
        description: "Failed to fetch leave types",
        variant: "destructive",
      });
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees');
      if (!response.ok) throw new Error('Failed to fetch employees');
      const data = await response.json();
      setEmployees(data);
    } catch (error) {
      console.error('Error fetching employees:', error);
      toast({
        title: "Error",
        description: "Failed to fetch employees",
        variant: "destructive",
      });
    }
  };

  // Event handlers
  const handleViewLeaveRequest = async (id: number) => {
    try {
      // First find the basic leave request info from the local state
      const leaveRequest = leaveRequests.find(req => req.id === id);

      if (!leaveRequest) {
        console.error(`Leave request with ID ${id} not found in local state`);
        toast({
          title: "Error",
          description: "Leave request not found",
          variant: "destructive",
        });
        return;
      }

      console.log('Basic leave request details from local state:', leaveRequest);

      // Then fetch the detailed information from the API to get the latest data including attachment
      const response = await fetch(`/api/leave-management/${id}/get`);

      if (!response.ok) {
        throw new Error(`Failed to fetch leave request details: ${response.statusText}`);
      }

      const detailedLeaveRequest = await response.json();
      console.log('Detailed leave request from API:', detailedLeaveRequest);

      // Log attachment information specifically
      console.log('Attachment information:', {
        fromLocalState: {
          attachmentUrl: leaveRequest.attachmentUrl,
          attachment: leaveRequest.attachment,
          attachmentPath: leaveRequest.attachmentPath
        },
        fromAPI: {
          attachmentUrl: detailedLeaveRequest.attachmentUrl,
          attachment: detailedLeaveRequest.attachment,
          attachmentPath: detailedLeaveRequest.attachmentPath
        }
      });

      // Merge the data from both sources, prioritizing the API data
      const mergedLeaveRequest = {
        ...leaveRequest,
        ...detailedLeaveRequest,
        // Make sure we preserve the employee information if it exists in either source
        employee: detailedLeaveRequest.employee || leaveRequest.employee
      };

      // If no attachment is found in the API response but exists in local state, use the local state value
      if (!detailedLeaveRequest.attachmentUrl && !detailedLeaveRequest.attachment && !detailedLeaveRequest.attachmentPath) {
        if (leaveRequest.attachmentUrl) {
          mergedLeaveRequest.attachmentUrl = leaveRequest.attachmentUrl;
        } else if (leaveRequest.attachment) {
          mergedLeaveRequest.attachment = leaveRequest.attachment;
        } else if (leaveRequest.attachmentPath) {
          mergedLeaveRequest.attachmentPath = leaveRequest.attachmentPath;
        }
      }

      // No need to add a dummy attachment URL for testing anymore

      console.log('Merged leave request details:', mergedLeaveRequest);
      setSelectedLeaveRequest(mergedLeaveRequest);
      setIsViewLeaveRequestOpen(true);
    } catch (error) {
      console.error('Error fetching leave request details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch leave request details",
        variant: "destructive",
      });
    }
  };

  const handleEditLeaveRequest = async (id: number) => {
    const leaveRequest = leaveRequests.find(req => req.id === id);
    if (leaveRequest) {
      // Check if leave request is already approved or rejected
      if (leaveRequest.status !== 'pending') {
        toast({
          title: "Cannot Edit",
          description: `This leave request has already been ${leaveRequest.status}. Only pending requests can be edited.`,
          variant: "destructive",
        });
        return;
      }

      // Format date to YYYY-MM-DD for input type="date"
      const startDate = leaveRequest.startDate ? new Date(leaveRequest.startDate).toISOString().split('T')[0] : '';
      const endDate = leaveRequest.endDate ? new Date(leaveRequest.endDate).toISOString().split('T')[0] : '';

      console.log('Editing leave request:', leaveRequest);
      console.log('Formatted dates:', { startDate, endDate });

      // Fetch detailed leave request to get the latest attachment URL
      try {
        const response = await fetch(`/api/leave-management/${id}/get`);
        if (!response.ok) {
          throw new Error(`Failed to fetch leave request details: ${response.statusText}`);
        }

        const detailedLeaveRequest = await response.json();
        console.log('Detailed leave request for edit:', detailedLeaveRequest);

        // Use attachment URL from detailed request if available
        const attachmentUrl = detailedLeaveRequest.attachmentUrl || leaveRequest.attachmentUrl || '';
        console.log('Using attachment URL for edit form:', attachmentUrl);

        setEditLeaveForm({
          id: leaveRequest.id,
          leaveTypeId: leaveRequest.leaveTypeId.toString(),
          startDate: startDate,
          endDate: endDate,
          reason: leaveRequest.reason || '',
          attachmentUrl: attachmentUrl,
        });
        setIsEditLeaveRequestOpen(true);
      } catch (error) {
        console.error('Error fetching leave request details for edit:', error);
        // Fallback to using data from the local state
        setEditLeaveForm({
          id: leaveRequest.id,
          leaveTypeId: leaveRequest.leaveTypeId.toString(),
          startDate: startDate,
          endDate: endDate,
          reason: leaveRequest.reason || '',
          attachmentUrl: leaveRequest.attachmentUrl || '',
        });
        setIsEditLeaveRequestOpen(true);
      }
    }
  };

  const handleDeleteLeaveRequest = async (id: number) => {
    if (confirm('Are you sure you want to delete this leave request?')) {
      try {
        // Gunakan endpoint delete secara langsung untuk menghindari masalah pengalihan
        const response = await fetch(`/api/leave-management/${id}/delete`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
          console.error('Error response:', errorData);
          throw new Error(errorData.error || 'Failed to delete leave request');
        }

        // Refresh data
        await fetchLeaveRequests();

        toast({
          title: "Success",
          description: "Leave request deleted successfully",
        });
      } catch (error) {
        console.error('Error deleting leave request:', error);
        toast({
          title: "Error",
          description: "Failed to delete leave request",
          variant: "destructive",
        });
      }
    }
  };

  const handleApproveLeaveRequest = async (id: number) => {
    try {
      const response = await fetch(`/api/leave-management/approve/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('Error response:', errorData);
        throw new Error(errorData.error || 'Failed to approve leave request');
      }

      // Refresh data
      await fetchLeaveRequests();

      toast({
        title: "Success",
        description: "Leave request approved successfully",
      });
    } catch (error) {
      console.error('Error approving leave request:', error);
      toast({
        title: "Error",
        description: "Failed to approve leave request",
        variant: "destructive",
      });
    }
  };

  const handleRejectLeaveRequest = async (id: number) => {
    try {
      const response = await fetch(`/api/leave-management/reject/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('Error response:', errorData);
        throw new Error(errorData.error || 'Failed to reject leave request');
      }

      // Refresh data
      await fetchLeaveRequests();

      toast({
        title: "Success",
        description: "Leave request rejected successfully",
      });
    } catch (error) {
      console.error('Error rejecting leave request:', error);
      toast({
        title: "Error",
        description: "Failed to reject leave request",
        variant: "destructive",
      });
    }
  };

  const handleViewLateRequest = (id: number) => {
    const lateRequest = lateRequests.find(req => req.id === id);
    if (lateRequest) {
      setSelectedLateRequest(lateRequest);
      setIsViewLateRequestOpen(true);
    }
  };

  const handleEditLateRequest = (id: number) => {
    const lateRequest = lateRequests.find(req => req.id === id);
    if (lateRequest) {
      // Check if late request is already approved or rejected
      if (lateRequest.status !== 'pending') {
        toast({
          title: "Cannot Edit",
          description: `This late request has already been ${lateRequest.status}. Only pending requests can be edited.`,
          variant: "destructive",
        });
        return;
      }

      // Format date to YYYY-MM-DD for input type="date"
      const lateDate = lateRequest.lateDate ? new Date(lateRequest.lateDate).toISOString().split('T')[0] : '';

      console.log('Editing late request:', lateRequest);
      console.log('Formatted date:', { lateDate });

      setEditLateForm({
        id: lateRequest.id,
        lateType: lateRequest.lateType || '',
        lateDate: lateDate,
        estimatedTime: lateRequest.estimatedTime || '',
        reason: lateRequest.reason || '',
        attachmentUrl: lateRequest.attachmentUrl || '',
        attachment: null,
      });
      setIsEditLateRequestOpen(true);
    }
  };

  const handleDeleteLateRequest = async (id: number) => {
    if (confirm('Are you sure you want to delete this late request?')) {
      try {
        // Gunakan endpoint delete secara langsung untuk menghindari masalah pengalihan
        const response = await fetch(`/api/late-requests/${id}/delete`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
          console.error('Error response:', errorData);
          throw new Error(errorData.error || 'Failed to delete late request');
        }

        // Refresh data
        await fetchLateRequests();

        toast({
          title: "Success",
          description: "Late request deleted successfully",
        });
      } catch (error) {
        console.error('Error deleting late request:', error);
        toast({
          title: "Error",
          description: "Failed to delete late request",
          variant: "destructive",
        });
      }
    }
  };

  const handleApproveLateRequest = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/late-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve late request');
      }

      // Refresh data
      await fetchLateRequests();

      toast({
        title: "Success",
        description: "Late request approved successfully",
      });
    } catch (error) {
      console.error('Error approving late request:', error);
      toast({
        title: "Error",
        description: "Failed to approve late request",
        variant: "destructive",
      });
    }
  };

  const handleRejectLateRequest = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/late-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject late request');
      }

      // Refresh data
      await fetchLateRequests();

      toast({
        title: "Success",
        description: "Late request rejected successfully",
      });
    } catch (error) {
      console.error('Error rejecting late request:', error);
      toast({
        title: "Error",
        description: "Failed to reject late request",
        variant: "destructive",
      });
    }
  };

  const handleViewExitRequest = (id: number) => {
    const exitRequest = exitRequests.find(req => req.id === id);
    if (exitRequest) {
      setSelectedExitRequest(exitRequest);
      setIsViewExitRequestOpen(true);
    }
  };

  const handleEditExitRequest = (id: number) => {
    const exitRequest = exitRequests.find(req => req.id === id);
    if (exitRequest) {
      // Check if exit request is already approved or rejected
      if (exitRequest.status !== 'pending') {
        toast({
          title: "Cannot Edit",
          description: `This exit request has already been ${exitRequest.status}. Only pending requests can be edited.`,
          variant: "destructive",
        });
        return;
      }

      // Format date to YYYY-MM-DD for input type="date"
      const exitDate = exitRequest.exitDate ? new Date(exitRequest.exitDate).toISOString().split('T')[0] : '';

      console.log('Editing exit request:', exitRequest);
      console.log('Formatted date:', { exitDate });

      setEditExitForm({
        id: exitRequest.id,
        exitType: exitRequest.exitType || '',
        exitDate: exitDate,
        exitTime: exitRequest.exitTime || '',
        comebackTime: exitRequest.comebackTime || '',
        notComeback: exitRequest.notComeback || false,
        reason: exitRequest.reason || '',
      });
      setIsEditExitRequestOpen(true);
    }
  };

  const handleDeleteExitRequest = async (id: number) => {
    if (confirm('Are you sure you want to delete this exit request?')) {
      try {
        // Gunakan endpoint delete secara langsung untuk menghindari masalah pengalihan
        const response = await fetch(`/api/exit-requests/${id}/delete`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
          console.error('Error response:', errorData);
          throw new Error(errorData.error || 'Failed to delete exit request');
        }

        // Refresh data
        await fetchExitRequests();

        toast({
          title: "Success",
          description: "Exit request deleted successfully",
        });
      } catch (error) {
        console.error('Error deleting exit request:', error);
        toast({
          title: "Error",
          description: "Failed to delete exit request",
          variant: "destructive",
        });
      }
    }
  };

  const handleApproveExitRequest = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/exit-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to approve exit request');
      }

      // Refresh data
      await fetchExitRequests();

      toast({
        title: "Success",
        description: "Exit request approved successfully",
      });
    } catch (error) {
      console.error('Error approving exit request:', error);
      toast({
        title: "Error",
        description: "Failed to approve exit request",
        variant: "destructive",
      });
    }
  };

  const handleRejectExitRequest = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/exit-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject exit request');
      }

      // Refresh data
      await fetchExitRequests();

      toast({
        title: "Success",
        description: "Exit request rejected successfully",
      });
    } catch (error) {
      console.error('Error rejecting exit request:', error);
      toast({
        title: "Error",
        description: "Failed to reject exit request",
        variant: "destructive",
      });
    }
  };

  // Handler untuk menambah leave type baru
  const handleAddLeaveType = () => {
    // Placeholder function to satisfy the component props
    console.log('Add leave type function called');
    toast({
      title: "Info",
      description: "This feature is not implemented in this version",
    });
  };

  // Handler untuk mengedit leave type
  const handleEditLeaveType = () => {
    // Placeholder function to satisfy the component props
    console.log('Edit leave type function called');
    toast({
      title: "Info",
      description: "This feature is not implemented in this version",
    });
  };

  // Handler untuk menghapus leave type
  const handleDeleteLeaveType = () => {
    // Placeholder function to satisfy the component props
    console.log('Delete leave type function called');
    toast({
      title: "Info",
      description: "This feature is not implemented in this version",
    });
  };

  // Log stats sebelum render
  console.log('Stats before render:', stats);

  // Render
  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={userRole} />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Leave Management</h1>
          <p className="text-muted-foreground mt-2">
            Track and manage employee leave requests
          </p>
          {/* Tampilkan statistik untuk semua pengguna */}
          <div className="flex flex-wrap gap-4 mt-4 bg-card p-4 rounded-lg shadow-sm">
            <div className="text-sm">
              <span className="font-medium">Pending Leave Requests: </span>
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                {stats.pendingLeaveRequests}
              </span>
            </div>
            <div className="text-sm">
              <span className="font-medium">Pending Late Requests: </span>
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                {stats.pendingLateRequests}
              </span>
            </div>
            <div className="text-sm">
              <span className="font-medium">Pending Exit Requests: </span>
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                {stats.pendingExitRequests}
              </span>
            </div>
          </div>
        </div>

        <Tabs defaultValue="leave-requests" className="w-full">
          <TabsList className="flex flex-wrap w-full mb-6 overflow-visible p-1">
            <TabsTrigger value="leave-requests" className="flex items-center justify-center gap-1">
              <FileText className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">Leave Requests</span>
            </TabsTrigger>

            <TabsTrigger value="calendar" className="flex items-center justify-center gap-1">
              <CalendarIcon className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">Calendar View</span>
            </TabsTrigger>

            <TabsTrigger value="late" className="flex items-center justify-center gap-1">
              <Clock className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">Late</span>
            </TabsTrigger>

            <TabsTrigger value="exit" className="flex items-center justify-center gap-1">
              <Clock className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">Exit From School</span>
            </TabsTrigger>

            {userRole === "ADMIN" && (
              <TabsTrigger value="leave-types" className="flex items-center justify-center gap-1">
                <Clock className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">Leave Types</span>
              </TabsTrigger>
            )}

            {(userRole === "ADMIN" || userRole === "SUPERVISOR" || userRole === "HEAD") && (
              <TabsTrigger value="reports" className="flex items-center justify-center gap-1">
                <BarChart className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">Reports</span>
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="leave-requests">
            <LeaveRequestsTab
              formattedLeaveRequests={formattedLeaveRequests}
              userRole={userRole}
              user={user}
              loading={loading}
              onView={handleViewLeaveRequest}
              onEdit={handleEditLeaveRequest}
              onDelete={handleDeleteLeaveRequest}
              onApprove={handleApproveLeaveRequest}
              onReject={handleRejectLeaveRequest}
              onAddNew={() => setIsAddLeaveRequestOpen(true)}
            />
          </TabsContent>

          <TabsContent value="calendar">
            {/* Rendering CalendarTab with leaveEvents */}
            <CalendarTab
              leaveEvents={leaveEvents}
              loading={loading}
              userRole={userRole}
              modifiers={modifiers}
              modifiersStyles={modifiersStyles}
            />
          </TabsContent>

          <TabsContent value="late">
            <LateRequestsTab
              filteredLateRequests={filteredLateRequests}
              userRole={userRole}
              user={user}
              loading={loading}
              onView={handleViewLateRequest}
              onEdit={handleEditLateRequest}
              onDelete={handleDeleteLateRequest}
              onApprove={handleApproveLateRequest}
              onReject={handleRejectLateRequest}
              onAddNew={() => setIsAddLateRequestOpen(true)}
            />
          </TabsContent>

          <TabsContent value="exit">
            <ExitRequestsTab
              filteredExitRequests={filteredExitRequests}
              userRole={userRole}
              user={user}
              loading={loading}
              onView={handleViewExitRequest}
              onEdit={handleEditExitRequest}
              onDelete={handleDeleteExitRequest}
              onApprove={handleApproveExitRequest}
              onReject={handleRejectExitRequest}
              onAddNew={() => {
                // Always set the date to today when opening the form
                setExitRequestForm({
                  ...exitRequestForm,
                  exitDate: new Date().toISOString().split('T')[0]
                });
                setIsAddExitRequestOpen(true);
              }}
            />
          </TabsContent>

          <TabsContent value="leave-types">
            {userRole === "ADMIN" ? (
              <LeaveTypesTab
                leaveTypes={leaveTypes}
                loading={loading}
                onAdd={handleAddLeaveType}
                onEdit={handleEditLeaveType}
                onDelete={handleDeleteLeaveType}
              />
            ) : (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="text-center py-8">
                  <h3 className="text-lg font-medium">Access Restricted</h3>
                  <p className="text-muted-foreground mt-2">
                    Only administrators can access leave types management.
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="reports">
            {(userRole === "ADMIN" || userRole === "SUPERVISOR" || userRole === "HEAD") ? (
              <ReportsTab userRole={userRole} />
            ) : (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="text-center py-8">
                  <h3 className="text-lg font-medium">Access Restricted</h3>
                  <p className="text-muted-foreground mt-2">
                    Only administrators and supervisors can access reports.
                  </p>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>

      {/* Dialog untuk menambah leave request baru */}
      <Dialog open={isAddLeaveRequestOpen} onOpenChange={setIsAddLeaveRequestOpen}>
        <DialogContent className="w-full max-w-full sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add New Leave Request</DialogTitle>
            <DialogDescription>
              Fill in the details to create a new leave request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3 sm:gap-4 py-2 sm:py-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="leaveTypeId">Leave Type</Label>
                <Select
                  value={leaveRequestForm.leaveTypeId}
                  onValueChange={(value) => {
                    // Cari leave type yang dipilih
                    const selectedLeaveType = leaveTypes.find(type => type.id.toString() === value);

                    // Update form dengan leave type yang dipilih
                    const updatedForm = {...leaveRequestForm, leaveTypeId: value};

                    if (selectedLeaveType) {
                      const isIjinOrSakit = selectedLeaveType.name.toLowerCase() === 'ijin' ||
                                            selectedLeaveType.name.toLowerCase() === 'sakit';

                      // Jika bukan tipe Ijin atau Sakit, hitung end date berdasarkan allowed days
                      if (!isIjinOrSakit) {
                        // Hitung end date berdasarkan start date dan allowed days
                        const startDate = new Date(leaveRequestForm.startDate);
                        const endDate = new Date(startDate);
                        endDate.setDate(startDate.getDate() + selectedLeaveType.daysAllowed - 1);

                        // Update end date di form
                        updatedForm.endDate = endDate.toISOString().split('T')[0];
                      }
                    }

                    // Update state form
                    setLeaveRequestForm(updatedForm);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select leave type" />
                  </SelectTrigger>
                  <SelectContent>
                    {leaveTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {userRole === "ADMIN" && (
                <div>
                  <Label htmlFor="employeeId">Employee</Label>
                  <Select
                    value={leaveRequestForm.employeeId}
                    onValueChange={(value) => setLeaveRequestForm(prev => ({...prev, employeeId: value}))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select employee" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          {employee.firstName} {employee.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  type="date"
                  id="startDate"
                  value={leaveRequestForm.startDate}
                  onChange={(e) => {
                    const newStartDate = e.target.value;
                    let updatedForm = {...leaveRequestForm, startDate: newStartDate};
                    const startDate = new Date(newStartDate);
                    const currentEndDate = new Date(leaveRequestForm.endDate);

                    // Jika start date baru lebih besar dari end date, update end date juga
                    if (startDate > currentEndDate) {
                      updatedForm.endDate = newStartDate; // Set end date sama dengan start date

                      toast({
                        title: "Info",
                        description: "Tanggal selesai telah disesuaikan karena tanggal mulai baru lebih akhir",
                      });
                    }

                    // Jika sudah ada leave type yang dipilih
                    else if (leaveRequestForm.leaveTypeId) {
                      const selectedLeaveType = leaveTypes.find(type => type.id.toString() === leaveRequestForm.leaveTypeId);

                      if (selectedLeaveType) {
                        const isIjinOrSakit = selectedLeaveType.name.toLowerCase() === 'ijin' ||
                                              selectedLeaveType.name.toLowerCase() === 'sakit';

                        // Jika bukan tipe Ijin atau Sakit, hitung end date berdasarkan allowed days
                        if (!isIjinOrSakit) {
                          // Hitung end date berdasarkan start date dan allowed days
                          const endDate = new Date(startDate);
                          endDate.setDate(startDate.getDate() + selectedLeaveType.daysAllowed - 1);

                          // Update end date di form
                          updatedForm.endDate = endDate.toISOString().split('T')[0];
                        }
                      }
                    }

                    // Update state form
                    setLeaveRequestForm(prev => {
                      // Gunakan nilai terbaru dari prev untuk memastikan konsistensi
                      return {...prev, ...updatedForm};
                    });
                  }}
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  type="date"
                  id="endDate"
                  value={leaveRequestForm.endDate}
                  onChange={(e) => {
                    const newEndDate = e.target.value;
                    const startDate = new Date(leaveRequestForm.startDate);
                    const endDate = new Date(newEndDate);

                    // Validasi end date tidak boleh lebih kecil dari start date
                    if (endDate < startDate) {
                      toast({
                        title: "Error",
                        description: "Tanggal selesai tidak boleh lebih awal dari tanggal mulai",
                        variant: "destructive",
                      });
                      return;
                    }

                    // Validasi durasi tidak melebihi daysAllowed untuk leave type selain Ijin dan Sakit
                    if (leaveRequestForm.leaveTypeId) {
                      const selectedLeaveType = leaveTypes.find(type => type.id.toString() === leaveRequestForm.leaveTypeId);

                      if (selectedLeaveType) {
                        const isIjinOrSakit = selectedLeaveType.name.toLowerCase() === 'ijin' ||
                                              selectedLeaveType.name.toLowerCase() === 'sakit';

                        if (isIjinOrSakit) {
                          // Untuk Ijin dan Sakit, tidak perlu validasi durasi
                          // Tapi kita bisa menampilkan informasi durasi hari kerja
                          const workingDays = calculateWorkingDays(startDate, endDate);
                          logger.debug(`Leave request with ${workingDays} working days`);
                        } else {
                          // Untuk tipe cuti lainnya, validasi total hari
                          const totalDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                          // Periksa apakah durasi melebihi allowed days
                          if (totalDays > selectedLeaveType.daysAllowed) {
                            toast({
                              title: "Error",
                              description: `Durasi cuti (${totalDays} hari) melebihi batas maksimum (${selectedLeaveType.daysAllowed} hari)`,
                              variant: "destructive",
                            });
                            return;
                          }
                        }
                      }
                    }

                    setLeaveRequestForm(prev => ({...prev, endDate: newEndDate}));
                  }}
                />
                {leaveRequestForm.leaveTypeId && (() => {
                  const selectedLeaveType = leaveTypes.find(type => type.id.toString() === leaveRequestForm.leaveTypeId);
                  if (selectedLeaveType) {
                    // Hitung durasi dalam hari
                    const startDate = new Date(leaveRequestForm.startDate);
                    const endDate = new Date(leaveRequestForm.endDate);

                    // Cek apakah leave type adalah Ijin atau Sakit
                    const isIjinOrSakit = selectedLeaveType.name.toLowerCase() === 'ijin' ||
                                          selectedLeaveType.name.toLowerCase() === 'sakit';

                    if (isIjinOrSakit) {
                      // Untuk Ijin dan Sakit, hitung hari kerja (tanpa Sabtu dan Minggu)
                      const workingDays = calculateWorkingDays(startDate, endDate);
                      const totalDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                      return (
                        <p className="text-xs text-muted-foreground mt-1">
                          Durasi: {workingDays} hari kerja (total {totalDays} hari termasuk akhir pekan)
                        </p>
                      );
                    } else {
                      // Untuk tipe cuti lainnya, hitung total hari
                      const totalDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                      return (
                        <p className="text-xs text-muted-foreground mt-1">
                          Durasi: {totalDays} hari (maksimum {selectedLeaveType.daysAllowed} hari)
                        </p>
                      );
                    }
                  }
                  return null;
                })()}
              </div>
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                value={leaveRequestForm.reason}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setLeaveRequestForm(prev => ({
                    ...prev,
                    reason: newValue
                  }));
                }}
                placeholder="Enter reason for leave"
              />
            </div>
            <div>
              <Label htmlFor="attachment">Attachment (Optional)</Label>
              <Input
                type="file"
                id="attachment"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    setSelectedFile(e.target.files[0]);
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddLeaveRequestOpen(false)}>Cancel</Button>
            <Button onClick={async () => {
              try {
                // Validate required fields
                if (!leaveRequestForm.leaveTypeId) {
                  toast({
                    title: "Error",
                    description: "Please select a leave type",
                    variant: "destructive",
                  });
                  return;
                }

                if (!leaveRequestForm.startDate) {
                  toast({
                    title: "Error",
                    description: "Please select a start date",
                    variant: "destructive",
                  });
                  return;
                }

                if (!leaveRequestForm.endDate) {
                  toast({
                    title: "Error",
                    description: "Please select an end date",
                    variant: "destructive",
                  });
                  return;
                }

                if (!leaveRequestForm.reason) {
                  toast({
                    title: "Error",
                    description: "Please enter a reason for leave",
                    variant: "destructive",
                  });
                  return;
                }

                // Validate that end date is not before start date
                const startDate = new Date(leaveRequestForm.startDate);
                const endDate = new Date(leaveRequestForm.endDate);
                if (endDate < startDate) {
                  toast({
                    title: "Error",
                    description: "End date cannot be before start date",
                    variant: "destructive",
                  });
                  return;
                }

                // Validasi durasi leave berdasarkan allowed days (kecuali untuk Ijin dan Sakit)
                if (leaveRequestForm.leaveTypeId) {
                  const selectedLeaveType = leaveTypes.find(type => type.id.toString() === leaveRequestForm.leaveTypeId);

                  if (selectedLeaveType) {
                    const isIjinOrSakit = selectedLeaveType.name.toLowerCase() === 'ijin' ||
                                          selectedLeaveType.name.toLowerCase() === 'sakit';

                    if (isIjinOrSakit) {
                      // Untuk Ijin dan Sakit, tidak perlu validasi durasi
                      // Tapi kita bisa menampilkan informasi durasi hari kerja
                      const workingDays = calculateWorkingDays(startDate, endDate);
                      console.log(`Leave request for ${selectedLeaveType.name} with ${workingDays} working days`);
                    } else {
                      // Untuk tipe cuti lainnya, validasi total hari
                      const totalDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                      // Periksa apakah durasi melebihi allowed days
                      if (totalDays > selectedLeaveType.daysAllowed) {
                        toast({
                          title: "Error",
                          description: `Durasi cuti (${totalDays} hari) melebihi batas maksimum (${selectedLeaveType.daysAllowed} hari)`,
                          variant: "destructive",
                        });
                        return;
                      }
                    }
                  }
                }

                // Create JSON data for API request
                let employeeId = leaveRequestForm.employeeId;

                // Jika user bukan ADMIN, pastikan employeeId diambil dari user yang login
                if (userRole !== 'ADMIN') {
                  console.log('Submit as non-ADMIN user:', user);

                  // Cek struktur user object untuk menemukan employeeId
                  const userEmployeeId = user?.employeeId || user?.id;

                  if (!userEmployeeId) {
                    toast({
                      title: "Error",
                      description: "Employee ID tidak ditemukan. Silakan hubungi administrator.",
                      variant: "destructive",
                    });
                    console.error('No employeeId found for user:', user);
                    return;
                  }

                  employeeId = userEmployeeId.toString();
                  console.log('Using employeeId for submission:', employeeId);
                }

                // Validasi employeeId
                if (!employeeId) {
                  toast({
                    title: "Error",
                    description: "Employee ID diperlukan",
                    variant: "destructive",
                  });
                  return;
                }

                // First, create the leave request
                const requestData = {
                  employeeId: employeeId,
                  leaveTypeId: parseInt(leaveRequestForm.leaveTypeId),
                  startDate: leaveRequestForm.startDate,
                  endDate: leaveRequestForm.endDate,
                  reason: leaveRequestForm.reason,
                  attachmentUrl: null // We'll handle file upload separately
                };

                console.log('Sending leave request data:', requestData);

                const response = await fetch('/api/leave-management/create', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(requestData),
                });

                if (!response.ok) {
                  const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                  console.error('Error response:', errorData);
                  throw new Error(errorData.error || 'Failed to create leave request');
                }

                const responseData = await response.json();
                console.log('Leave request created successfully:', responseData);

                // If there's a file selected, upload it
                if (selectedFile) {
                  console.log('Uploading attachment for leave request ID:', responseData.id);

                  const formData = new FormData();
                  formData.append('file', selectedFile);

                  const uploadResponse = await fetch(`/api/leave-management/${responseData.id}/upload`, {
                    method: 'POST',
                    body: formData,
                  });

                  if (!uploadResponse.ok) {
                    const uploadErrorData = await uploadResponse.json().catch(() => ({ error: 'Unknown error' }));
                    console.error('Error uploading attachment:', uploadErrorData);
                    toast({
                      title: "Warning",
                      description: "Leave request created but failed to upload attachment",
                      variant: "destructive",
                    });
                  } else {
                    const uploadResponseData = await uploadResponse.json();
                    console.log('Attachment uploaded successfully:', uploadResponseData);
                  }
                }

                // Reset form and close dialog
                setLeaveRequestForm({
                  employeeId: '',
                  leaveTypeId: '',
                  startDate: new Date().toISOString().split('T')[0],
                  endDate: new Date().toISOString().split('T')[0],
                  reason: '',
                  attachment: null,
                });
                setSelectedFile(null);
                setIsAddLeaveRequestOpen(false);

                // Refresh data
                await fetchLeaveRequests();
                toast({
                  title: "Success",
                  description: "Leave request created successfully",
                });
              } catch (error) {
                console.error('Error creating leave request:', error);
                toast({
                  title: "Error",
                  description: "Failed to create leave request",
                  variant: "destructive",
                });
              }
            }}>Submit</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk menambah late request baru */}
      <Dialog open={isAddLateRequestOpen} onOpenChange={(open) => {
        setIsAddLateRequestOpen(open);
        if (!open) {
          setSelectedLateFile(null);
        }
      }}>
        <DialogContent className="w-full max-w-full sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add New Late Request</DialogTitle>
            <DialogDescription>
              Fill in the details to create a new late request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3 sm:gap-4 py-2 sm:py-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="lateType">Late Type</Label>
                <Select
                  value={lateRequestForm.lateType}
                  onValueChange={(value) => setLateRequestForm({...lateRequestForm, lateType: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select late type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Urgent">Urgent</SelectItem>
                    <SelectItem value="Work">Work</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {userRole === "ADMIN" && (
                <div>
                  <Label htmlFor="employeeId">Employee</Label>
                  <Select
                    value={lateRequestForm.employeeId}
                    onValueChange={(value) => setLateRequestForm({...lateRequestForm, employeeId: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select employee" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          {employee.firstName} {employee.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="lateDate">Date</Label>
                <Input
                  type="date"
                  id="lateDate"
                  value={lateRequestForm.lateDate}
                  onChange={(e) => setLateRequestForm({...lateRequestForm, lateDate: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="estimatedTime">Estimated Time</Label>
                <Input
                  type="time"
                  id="estimatedTime"
                  value={lateRequestForm.estimatedTime}
                  onChange={(e) => setLateRequestForm({...lateRequestForm, estimatedTime: e.target.value})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                value={lateRequestForm.reason}
                onChange={(e) => setLateRequestForm({...lateRequestForm, reason: e.target.value})}
                placeholder="Enter reason for late arrival"
              />
            </div>
            <div>
              <Label htmlFor="attachment">Attachment (Optional)</Label>
              <Input
                type="file"
                id="attachment"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    setSelectedLateFile(e.target.files[0]);
                  }
                }}
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              />
              <p className="text-xs text-muted-foreground mt-1">
                *Optional. Upload supporting documents (PDF, JPG, PNG, DOC, DOCX). Max 5MB.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddLateRequestOpen(false)}>Cancel</Button>
            <Button onClick={async () => {
              try {
                // Jika user bukan ADMIN, pastikan employeeId diambil dari user yang login
                let employeeId = lateRequestForm.employeeId;

                if (userRole !== 'ADMIN') {
                  console.log('Submit late request as non-ADMIN user:', user);

                  // Cek struktur user object untuk menemukan employeeId
                  const userEmployeeId = user?.employeeId || user?.id;

                  if (!userEmployeeId) {
                    toast({
                      title: "Error",
                      description: "Employee ID tidak ditemukan. Silakan hubungi administrator.",
                      variant: "destructive",
                    });
                    console.error('No employeeId found for user:', user);
                    return;
                  }

                  employeeId = userEmployeeId.toString();
                  console.log('Using employeeId for late request submission:', employeeId);
                }

                // Validasi semua field yang diperlukan
                if (!employeeId) {
                  toast({
                    title: "Error",
                    description: "Employee ID diperlukan",
                    variant: "destructive",
                  });
                  return;
                }

                if (!lateRequestForm.lateType) {
                  toast({
                    title: "Error",
                    description: "Silakan pilih tipe keterlambatan",
                    variant: "destructive",
                  });
                  return;
                }

                if (!lateRequestForm.lateDate) {
                  toast({
                    title: "Error",
                    description: "Tanggal keterlambatan harus diisi",
                    variant: "destructive",
                  });
                  return;
                }

                if (!lateRequestForm.estimatedTime) {
                  toast({
                    title: "Error",
                    description: "Estimasi waktu kedatangan harus diisi",
                    variant: "destructive",
                  });
                  return;
                }

                const response = await fetch('/api/late-requests/create', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    employeeId: employeeId,
                    lateType: lateRequestForm.lateType,
                    lateDate: lateRequestForm.lateDate,
                    estimatedTime: lateRequestForm.estimatedTime,
                    reason: lateRequestForm.reason,
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                  console.error('Error response:', errorData);
                  throw new Error(errorData.error || 'Failed to create late request');
                }

                const responseData = await response.json();
                console.log('Late request created successfully:', responseData);

                // If there's a file selected, upload it
                if (selectedLateFile) {
                  console.log('Uploading attachment for late request ID:', responseData.lateRequest.id);

                  const formData = new FormData();
                  formData.append('file', selectedLateFile);

                  const uploadResponse = await fetch(`/api/late-requests/${responseData.lateRequest.id}/upload`, {
                    method: 'POST',
                    body: formData,
                  });

                  if (!uploadResponse.ok) {
                    const errorText = await uploadResponse.text();
                    console.error('Error uploading attachment:', errorText);
                    toast({
                      title: "Warning",
                      description: "Late request created but failed to upload attachment",
                      variant: "destructive",
                    });
                  } else {
                    const uploadResult = await uploadResponse.json();
                    console.log('Attachment uploaded successfully:', uploadResult);
                    toast({
                      title: "Success",
                      description: "Late request created and attachment uploaded successfully",
                    });
                  }
                } else {
                  toast({
                    title: "Success",
                    description: "Late request created successfully",
                  });
                }

                // Reset form and close dialog
                setLateRequestForm({
                  employeeId: '',
                  lateType: '',
                  lateDate: new Date().toISOString().split('T')[0],
                  estimatedTime: '08:00',
                  reason: '',
                  attachment: null,
                });
                setSelectedLateFile(null);
                setIsAddLateRequestOpen(false);

                // Refresh data
                await fetchLateRequests();
              } catch (error) {
                console.error('Error creating late request:', error);
                toast({
                  title: "Error",
                  description: "Failed to create late request",
                  variant: "destructive",
                });
              }
            }}>Submit</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk menambah exit request baru */}
      <Dialog open={isAddExitRequestOpen} onOpenChange={setIsAddExitRequestOpen}>
        <DialogContent className="w-full max-w-full sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add New Exit Request</DialogTitle>
            <DialogDescription>
              Fill in the details to create a new exit from school request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3 sm:gap-4 py-2 sm:py-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="exitType">Exit Type</Label>
                <Select
                  value={exitRequestForm.exitType}
                  onValueChange={(value) => setExitRequestForm({...exitRequestForm, exitType: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select exit type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Work">Work</SelectItem>
                    <SelectItem value="Personal">Personal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {userRole === "ADMIN" && (
                <div>
                  <Label htmlFor="employeeId">Employee</Label>
                  <Select
                    value={exitRequestForm.employeeId}
                    onValueChange={(value) => setExitRequestForm({...exitRequestForm, employeeId: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select employee" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          {employee.firstName} {employee.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="exitDate">Date</Label>
                <Input
                  type="date"
                  id="exitDate"
                  value={exitRequestForm.exitDate}
                  onChange={(e) => setExitRequestForm({...exitRequestForm, exitDate: e.target.value})}
                  disabled={true}
                />
              </div>
              <div>
                <Label htmlFor="exitTime">Exit Time</Label>
                <Input
                  type="time"
                  id="exitTime"
                  value={exitRequestForm.exitTime}
                  onChange={(e) => setExitRequestForm({...exitRequestForm, exitTime: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="comebackTime">Comeback Time</Label>
                <Input
                  type="time"
                  id="comebackTime"
                  value={exitRequestForm.comebackTime}
                  onChange={(e) => setExitRequestForm({...exitRequestForm, comebackTime: e.target.value})}
                  disabled={exitRequestForm.notComeback}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="notComeback"
                checked={exitRequestForm.notComeback}
                onCheckedChange={(checked) => setExitRequestForm({...exitRequestForm, notComeback: checked as boolean})}
              />
              <Label htmlFor="notComeback">Not coming back to school</Label>
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                value={exitRequestForm.reason}
                onChange={(e) => setExitRequestForm({...exitRequestForm, reason: e.target.value})}
                placeholder="Enter reason for exit from school"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddExitRequestOpen(false)}>Cancel</Button>
            <Button onClick={async () => {
              try {
                // Jika user bukan ADMIN, pastikan employeeId diambil dari user yang login
                let employeeId = exitRequestForm.employeeId;

                if (userRole !== 'ADMIN') {
                  console.log('Submit exit request as non-ADMIN user:', user);

                  // Cek struktur user object untuk menemukan employeeId
                  const userEmployeeId = user?.employeeId || user?.id;

                  if (!userEmployeeId) {
                    toast({
                      title: "Error",
                      description: "Employee ID tidak ditemukan. Silakan hubungi administrator.",
                      variant: "destructive",
                    });
                    console.error('No employeeId found for user:', user);
                    return;
                  }

                  employeeId = userEmployeeId.toString();
                  console.log('Using employeeId for exit request submission:', employeeId);
                }

                // Validasi semua field yang diperlukan
                if (!employeeId) {
                  toast({
                    title: "Error",
                    description: "Employee ID diperlukan",
                    variant: "destructive",
                  });
                  return;
                }

                if (!exitRequestForm.exitType) {
                  toast({
                    title: "Error",
                    description: "Silakan pilih tipe exit",
                    variant: "destructive",
                  });
                  return;
                }

                if (!exitRequestForm.exitTime) {
                  toast({
                    title: "Error",
                    description: "Waktu exit harus diisi",
                    variant: "destructive",
                  });
                  return;
                }

                if (!exitRequestForm.notComeback && !exitRequestForm.comebackTime) {
                  toast({
                    title: "Error",
                    description: "Waktu kembali harus diisi atau pilih Not Comeback",
                    variant: "destructive",
                  });
                  return;
                }

                // Validasi exit time dan comeback time
                if (!exitRequestForm.notComeback && exitRequestForm.comebackTime) {
                  const exitTimeMinutes = convertTimeToMinutes(exitRequestForm.exitTime);
                  const comebackTimeMinutes = convertTimeToMinutes(exitRequestForm.comebackTime);

                  if (comebackTimeMinutes <= exitTimeMinutes) {
                    toast({
                      title: "Error",
                      description: "Waktu kembali harus lebih besar dari waktu keluar",
                      variant: "destructive",
                    });
                    return;
                  }
                }

                const response = await fetch('/api/exit-requests/create', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    employeeId: employeeId,
                    exitType: exitRequestForm.exitType,
                    exitDate: new Date().toISOString().split('T')[0], // Always use current date
                    exitTime: exitRequestForm.exitTime,
                    comebackTime: exitRequestForm.notComeback ? null : exitRequestForm.comebackTime,
                    notComeback: exitRequestForm.notComeback,
                    reason: exitRequestForm.reason,
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                  console.error('Error response:', errorData);
                  throw new Error(errorData.error || 'Failed to create exit request');
                }

                // Reset form and close dialog
                setExitRequestForm({
                  employeeId: '',
                  exitType: '',
                  exitDate: new Date().toISOString().split('T')[0],
                  exitTime: '08:00',
                  comebackTime: '16:00',
                  notComeback: false,
                  reason: ''
                });
                setIsAddExitRequestOpen(false);

                // Refresh data
                await fetchExitRequests();

                toast({
                  title: "Success",
                  description: "Exit request created successfully",
                });
              } catch (error) {
                console.error('Error creating exit request:', error);
                toast({
                  title: "Error",
                  description: "Failed to create exit request",
                  variant: "destructive",
                });
              }
            }}>Submit</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk menampilkan detail leave request */}
      <Dialog open={isViewLeaveRequestOpen} onOpenChange={setIsViewLeaveRequestOpen}>
        <DialogContent className="w-full max-w-full sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Leave Request Details</DialogTitle>
            <DialogDescription>
              View details of the selected leave request.
            </DialogDescription>
          </DialogHeader>
          {selectedLeaveRequest && (
            <div className="grid gap-3 sm:gap-4 py-2 sm:py-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Employee</Label>
                  <div className="font-medium">
                    {typeof selectedLeaveRequest.employee === 'object' && selectedLeaveRequest.employee ?
                      `${selectedLeaveRequest.employee.firstName || ''} ${selectedLeaveRequest.employee.lastName || ''}` :
                      selectedLeaveRequest.employee || 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Leave Type</Label>
                  <div className="font-medium">
                    {typeof selectedLeaveRequest.leaveType === 'object' && selectedLeaveRequest.leaveType ?
                      selectedLeaveRequest.leaveType.name || 'N/A' :
                      selectedLeaveRequest.leaveType || 'N/A'}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Start Date</Label>
                  <div className="font-medium">
                    {selectedLeaveRequest.startDate ? new Date(selectedLeaveRequest.startDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) : 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>End Date</Label>
                  <div className="font-medium">
                    {selectedLeaveRequest.endDate ? new Date(selectedLeaveRequest.endDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) : 'N/A'}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Duration</Label>
                  <div className="font-medium">
                    {selectedLeaveRequest.duration ? `${selectedLeaveRequest.duration} days` :
                     // Hitung durasi dari startDate dan endDate jika duration tidak ada
                     selectedLeaveRequest.startDate && selectedLeaveRequest.endDate ?
                     `${Math.ceil((new Date(selectedLeaveRequest.endDate).getTime() - new Date(selectedLeaveRequest.startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1} days` :
                     'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Status</Label>
                  <div className="font-medium">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-semibold ${
                        selectedLeaveRequest.status === "approved"
                          ? "bg-green-100 text-green-800"
                          : selectedLeaveRequest.status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {selectedLeaveRequest.status.charAt(0).toUpperCase() + selectedLeaveRequest.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <Label>Reason</Label>
                <div className="font-medium">{selectedLeaveRequest.reason}</div>
              </div>
              <div>
                <Label>Attachment</Label>
                <div className="font-medium">
                  {(() => {
                    // Log attachment information for debugging
                    console.log('Attachment info in render:', {
                      attachmentUrl: selectedLeaveRequest.attachmentUrl,
                      attachment: selectedLeaveRequest.attachment,
                      attachmentPath: selectedLeaveRequest.attachmentPath,
                      rawData: selectedLeaveRequest
                    });

                    // Check for any attachment field
                    let attachmentUrl = null;

                    // Try all possible attachment field names
                    if (selectedLeaveRequest.attachmentUrl) {
                      attachmentUrl = selectedLeaveRequest.attachmentUrl;
                      console.log('Using attachmentUrl:', attachmentUrl);
                    } else if (selectedLeaveRequest.attachment) {
                      attachmentUrl = selectedLeaveRequest.attachment;
                      console.log('Using attachment:', attachmentUrl);
                    } else if (selectedLeaveRequest.attachmentPath) {
                      attachmentUrl = selectedLeaveRequest.attachmentPath;
                      console.log('Using attachmentPath:', attachmentUrl);
                    }

                    // If we found an attachment URL
                    if (attachmentUrl) {
                      // Make sure the URL is properly formatted
                      const formattedUrl = attachmentUrl.startsWith('http')
                        ? attachmentUrl
                        : attachmentUrl.startsWith('/')
                          ? `${window.location.origin}${attachmentUrl}`
                          : `${window.location.origin}/${attachmentUrl}`;

                      console.log('Formatted attachment URL:', formattedUrl);

                      return (
                        <div>
                          <a
                            href={attachmentUrl} target="_blank" rel="noopener noreferrer" download
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                              <polyline points="7 10 12 15 17 10"></polyline>
                              <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Download Attachment
                          </a>
                        </div>
                      );
                    }
                    // No attachment found
                    else {
                      return <span className="text-gray-500 italic">No attachment</span>;
                    }
                  })()}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewLeaveRequestOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk mengedit leave request */}
      <Dialog open={isEditLeaveRequestOpen} onOpenChange={(open) => {
        setIsEditLeaveRequestOpen(open);
        if (!open) {
          setSelectedEditFile(null);
        }
      }}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Leave Request</DialogTitle>
            <DialogDescription>
              Update the details of the leave request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-leaveTypeId">Leave Type</Label>
                <Select
                  value={editLeaveForm.leaveTypeId}
                  onValueChange={(value) => setEditLeaveForm({...editLeaveForm, leaveTypeId: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select leave type" />
                  </SelectTrigger>
                  <SelectContent>
                    {leaveTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-startDate">Start Date</Label>
                <Input
                  type="date"
                  id="edit-startDate"
                  value={editLeaveForm.startDate}
                  onChange={(e) => {
                    const newStartDate = e.target.value;
                    const startDate = new Date(newStartDate);
                    const endDate = new Date(editLeaveForm.endDate);

                    // Jika start date baru lebih besar dari end date, update end date juga
                    if (startDate > endDate) {
                      setEditLeaveForm({
                        ...editLeaveForm,
                        startDate: newStartDate,
                        endDate: newStartDate // Set end date sama dengan start date
                      });

                      toast({
                        title: "Info",
                        description: "Tanggal selesai telah disesuaikan karena tanggal mulai baru lebih akhir",
                      });
                    } else {
                      setEditLeaveForm({...editLeaveForm, startDate: newStartDate});
                    }
                  }}
                />
              </div>
              <div>
                <Label htmlFor="edit-endDate">End Date</Label>
                <Input
                  type="date"
                  id="edit-endDate"
                  value={editLeaveForm.endDate}
                  onChange={(e) => {
                    const newEndDate = e.target.value;
                    const startDate = new Date(editLeaveForm.startDate);
                    const endDate = new Date(newEndDate);

                    // Validasi end date tidak boleh lebih kecil dari start date
                    if (endDate < startDate) {
                      toast({
                        title: "Error",
                        description: "Tanggal selesai tidak boleh lebih awal dari tanggal mulai",
                        variant: "destructive",
                      });
                      return;
                    }

                    // Validasi durasi tidak melebihi daysAllowed untuk leave type selain Ijin dan Sakit
                    if (editLeaveForm.leaveTypeId) {
                      const selectedLeaveType = leaveTypes.find(type => type.id.toString() === editLeaveForm.leaveTypeId);

                      if (selectedLeaveType &&
                          selectedLeaveType.name.toLowerCase() !== 'ijin' &&
                          selectedLeaveType.name.toLowerCase() !== 'sakit') {
                        // Hitung durasi dalam hari
                        const durationDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                        // Periksa apakah durasi melebihi allowed days
                        if (durationDays > selectedLeaveType.daysAllowed) {
                          toast({
                            title: "Error",
                            description: `Durasi cuti (${durationDays} hari) melebihi batas maksimum (${selectedLeaveType.daysAllowed} hari)`,
                            variant: "destructive",
                          });
                          return;
                        }
                      }
                    }

                    setEditLeaveForm({...editLeaveForm, endDate: newEndDate});
                  }}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="edit-reason">Reason</Label>
              <Textarea
                id="edit-reason"
                value={editLeaveForm.reason}
                onChange={(e) => setEditLeaveForm({...editLeaveForm, reason: e.target.value})}
                placeholder="Enter reason for leave"
              />
            </div>
            <div>
              <Label htmlFor="edit-attachment">Attachment (Optional)</Label>
              {editLeaveForm.attachmentUrl ? (
                <div className="mb-2">
                  <div className="flex items-center gap-2 mb-2">
                    <a
                      href={editLeaveForm.attachmentUrl.startsWith('http')
                        ? editLeaveForm.attachmentUrl
                        : `${window.location.origin}${editLeaveForm.attachmentUrl}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline flex items-center gap-2"
                      download
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                      Current Attachment
                    </a>
                  </div>
                  <Input
                    type="file"
                    id="edit-attachment"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        setSelectedEditFile(e.target.files[0]);
                      }
                    }}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Upload a new file to replace the current attachment, or leave empty to keep the current one.
                  </p>
                </div>
              ) : (
                <div>
                  <Input
                    type="file"
                    id="edit-attachment"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        setSelectedEditFile(e.target.files[0]);
                      }
                    }}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    *Optional. Upload supporting document (PDF, JPG, PNG, DOC).
                  </p>
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditLeaveRequestOpen(false)}>Cancel</Button>
            <Button onClick={async () => {
              try {
                console.log('Updating leave request with ID:', editLeaveForm.id);

                // Double-check if the request is still in pending status
                const currentRequest = leaveRequests.find(req => req.id === editLeaveForm.id);
                if (currentRequest && currentRequest.status !== 'pending') {
                  toast({
                    title: "Cannot Edit",
                    description: `This leave request has already been ${currentRequest.status}. Only pending requests can be edited.`,
                    variant: "destructive",
                  });
                  setIsEditLeaveRequestOpen(false);
                  return;
                }

                // Validate that end date is not before start date
                const startDate = new Date(editLeaveForm.startDate);
                const endDate = new Date(editLeaveForm.endDate);
                if (endDate < startDate) {
                  toast({
                    title: "Error",
                    description: "Tanggal selesai tidak boleh lebih awal dari tanggal mulai",
                    variant: "destructive",
                  });
                  return;
                }

                // Validasi durasi tidak melebihi daysAllowed untuk leave type selain Ijin dan Sakit
                if (editLeaveForm.leaveTypeId) {
                  const selectedLeaveType = leaveTypes.find(type => type.id.toString() === editLeaveForm.leaveTypeId);

                  if (selectedLeaveType &&
                      selectedLeaveType.name.toLowerCase() !== 'ijin' &&
                      selectedLeaveType.name.toLowerCase() !== 'sakit') {
                    // Hitung durasi dalam hari
                    const durationDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

                    // Periksa apakah durasi melebihi allowed days
                    if (durationDays > selectedLeaveType.daysAllowed) {
                      toast({
                        title: "Error",
                        description: `Durasi cuti (${durationDays} hari) melebihi batas maksimum (${selectedLeaveType.daysAllowed} hari)`,
                        variant: "destructive",
                      });
                      return;
                    }
                  }
                }

                // Prepare request data
                const requestData = {
                  leaveTypeId: parseInt(editLeaveForm.leaveTypeId),
                  startDate: editLeaveForm.startDate,
                  endDate: editLeaveForm.endDate,
                  reason: editLeaveForm.reason,
                  attachmentUrl: editLeaveForm.attachmentUrl // Preserve existing attachment URL
                };

                console.log('Updating leave request with data:', requestData);

                const response = await fetch(`/api/leave-management/${editLeaveForm.id}/edit`, {
                  method: 'PUT',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(requestData),
                });

                if (!response.ok) {
                  const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                  console.error('Error response:', errorData);
                  throw new Error(errorData.error || 'Failed to update leave request');
                }

                const responseData = await response.json();
                console.log('Response data:', responseData);

                // Handle file upload if a file was selected
                if (selectedEditFile) {
                  console.log('Uploading attachment for leave request ID:', editLeaveForm.id);

                  const formData = new FormData();
                  formData.append('file', selectedEditFile);

                  try {
                    const uploadResponse = await fetch(`/api/leave-management/${editLeaveForm.id}/upload`, {
                      method: 'POST',
                      body: formData,
                    });

                    if (!uploadResponse.ok) {
                      const errorText = await uploadResponse.text();
                      console.error('Error uploading attachment:', errorText);
                      toast({
                        title: "Warning",
                        description: "Leave request updated but failed to upload attachment",
                        variant: "destructive",
                      });
                    } else {
                      const uploadResult = await uploadResponse.json();
                      console.log('Attachment uploaded successfully:', uploadResult);

                      // Update the local state with the new attachment URL
                      if (uploadResult.url) {
                        setEditLeaveForm(prev => ({
                          ...prev,
                          attachmentUrl: uploadResult.url
                        }));
                      }

                      toast({
                        title: "Success",
                        description: "Attachment uploaded successfully",
                      });
                    }
                  } catch (uploadError) {
                    console.error('Exception during attachment upload:', uploadError);
                    toast({
                      title: "Warning",
                      description: "Leave request updated but failed to upload attachment",
                      variant: "destructive",
                    });
                  }
                }

                // Reset form and close dialog
                setSelectedEditFile(null);
                setIsEditLeaveRequestOpen(false);

                // Refresh data
                await fetchLeaveRequests();

                toast({
                  title: "Success",
                  description: "Leave request updated successfully",
                });
              } catch (error) {
                console.error('Error updating leave request:', error);
                toast({
                  title: "Error",
                  description: "Failed to update leave request",
                  variant: "destructive",
                });
              }
            }}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk menampilkan detail late request */}
      <Dialog open={isViewLateRequestOpen} onOpenChange={setIsViewLateRequestOpen}>
        <DialogContent className="w-full max-w-full sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Late Request Details</DialogTitle>
            <DialogDescription>
              View details of the selected late request.
            </DialogDescription>
          </DialogHeader>
          {selectedLateRequest && (
            <div className="grid gap-3 sm:gap-4 py-2 sm:py-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Employee</Label>
                  <div className="font-medium">
                    {selectedLateRequest.employee ?
                      `${selectedLateRequest.employee.firstName} ${selectedLateRequest.employee.lastName}` :
                      'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Late Type</Label>
                  <div className="font-medium">{selectedLateRequest.lateType}</div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Date</Label>
                  <div className="font-medium">
                    {selectedLateRequest.lateDate ? new Date(selectedLateRequest.lateDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) : 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Estimated Time</Label>
                  <div className="font-medium">{selectedLateRequest.estimatedTime}</div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Status</Label>
                  <div className="font-medium">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-semibold ${
                        selectedLateRequest.status === "approved"
                          ? "bg-green-100 text-green-800"
                          : selectedLateRequest.status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {selectedLateRequest.status.charAt(0).toUpperCase() + selectedLateRequest.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <Label>Reason</Label>
                <div className="font-medium">{selectedLateRequest.reason}</div>
              </div>
              <div>
                <Label>Attachment</Label>
                <div className="font-medium">
                  {selectedLateRequest.attachmentUrl ? (
                    <div>
                      <a
                        href={selectedLateRequest.attachmentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        download
                        className="text-blue-600 hover:underline flex items-center gap-1"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Download Attachment
                      </a>
                    </div>
                  ) : (
                    <span className="text-gray-500 italic">No attachment</span>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewLateRequestOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk mengedit late request */}
      <Dialog open={isEditLateRequestOpen} onOpenChange={(open) => {
        setIsEditLateRequestOpen(open);
        if (!open) {
          setSelectedEditLateFile(null);
        }
      }}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Late Request</DialogTitle>
            <DialogDescription>
              Update the details of the late request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-lateType">Late Type</Label>
                <Select
                  value={editLateForm.lateType}
                  onValueChange={(value) => setEditLateForm({...editLateForm, lateType: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select late type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Urgent">Urgent</SelectItem>
                    <SelectItem value="Work">Work</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-lateDate">Date</Label>
                <Input
                  type="date"
                  id="edit-lateDate"
                  value={editLateForm.lateDate}
                  onChange={(e) => setEditLateForm({...editLateForm, lateDate: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit-estimatedTime">Estimated Time</Label>
                <Input
                  type="time"
                  id="edit-estimatedTime"
                  value={editLateForm.estimatedTime}
                  onChange={(e) => setEditLateForm({...editLateForm, estimatedTime: e.target.value})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="edit-late-reason">Reason</Label>
              <Textarea
                id="edit-late-reason"
                value={editLateForm.reason}
                onChange={(e) => setEditLateForm({...editLateForm, reason: e.target.value})}
                placeholder="Enter reason for late arrival"
              />
            </div>
            <div>
              <Label htmlFor="edit-attachment">Attachment</Label>
              {editLateForm.attachmentUrl && (
                <div className="mb-2 p-2 bg-gray-50 rounded flex items-center justify-between">
                  <a
                    href={editLateForm.attachmentUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline flex items-center gap-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                    {editLateForm.attachmentUrl.split('/').pop()}
                  </a>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-red-500 hover:text-red-700 hover:bg-red-50"
                    onClick={() => setEditLateForm(prev => ({...prev, attachmentUrl: ''}))}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </Button>
                </div>
              )}
              <Input
                type="file"
                id="edit-attachment"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    setSelectedEditLateFile(e.target.files[0]);
                  }
                }}
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              />
              <p className="text-xs text-muted-foreground mt-1">
                *Optional. Upload supporting documents (PDF, JPG, PNG, DOC, DOCX). Max 5MB.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditLateRequestOpen(false)}>Cancel</Button>
            <Button onClick={async () => {
              try {
                console.log('Updating late request with ID:', editLateForm.id);

                // Double-check if the request is still in pending status
                const currentRequest = lateRequests.find(req => req.id === editLateForm.id);
                if (currentRequest && currentRequest.status !== 'pending') {
                  toast({
                    title: "Cannot Edit",
                    description: `This late request has already been ${currentRequest.status}. Only pending requests can be edited.`,
                    variant: "destructive",
                  });
                  setIsEditLateRequestOpen(false);
                  return;
                }

                const response = await fetch(`/api/late-requests/${editLateForm.id}/update`, {
                  method: 'PUT',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    lateType: editLateForm.lateType,
                    lateDate: editLateForm.lateDate,
                    estimatedTime: editLateForm.estimatedTime,
                    reason: editLateForm.reason,
                    status: 'pending' // Pastikan status tetap pending
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                  console.error('Error response:', errorData);
                  throw new Error(errorData.error || 'Failed to update late request');
                }

                // If there's a file selected, upload it
                if (selectedEditLateFile) {
                  console.log('Uploading attachment for late request ID:', editLateForm.id);

                  const formData = new FormData();
                  formData.append('file', selectedEditLateFile);

                  try {
                    const uploadResponse = await fetch(`/api/late-requests/${editLateForm.id}/upload`, {
                      method: 'POST',
                      body: formData,
                    });

                    if (!uploadResponse.ok) {
                      const errorText = await uploadResponse.text();
                      console.error('Error uploading attachment:', errorText);
                      toast({
                        title: "Warning",
                        description: "Late request updated but failed to upload attachment",
                        variant: "destructive",
                      });
                    } else {
                      const uploadResult = await uploadResponse.json();
                      console.log('Attachment uploaded successfully:', uploadResult);

                      // Update the local state with the new attachment URL
                      if (uploadResult.url) {
                        setEditLateForm(prev => ({
                          ...prev,
                          attachmentUrl: uploadResult.url
                        }));
                      }

                      toast({
                        title: "Success",
                        description: "Late request updated and attachment uploaded successfully",
                      });
                    }
                  } catch (uploadError) {
                    console.error('Error uploading attachment:', uploadError);
                    toast({
                      title: "Warning",
                      description: "Late request updated but failed to upload attachment",
                      variant: "destructive",
                    });
                  }
                } else {
                  toast({
                    title: "Success",
                    description: "Late request updated successfully",
                  });
                }

                // Reset form and close dialog
                setSelectedEditLateFile(null);
                setIsEditLateRequestOpen(false);

                // Refresh data
                await fetchLateRequests();
              } catch (error) {
                console.error('Error updating late request:', error);
                toast({
                  title: "Error",
                  description: "Failed to update late request",
                  variant: "destructive",
                });
              }
            }}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk menampilkan detail exit request */}
      <Dialog open={isViewExitRequestOpen} onOpenChange={setIsViewExitRequestOpen}>
        <DialogContent className="w-full max-w-full sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Exit Request Details</DialogTitle>
            <DialogDescription>
              View details of the selected exit request.
            </DialogDescription>
          </DialogHeader>
          {selectedExitRequest && (
            <div className="grid gap-3 sm:gap-4 py-2 sm:py-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Employee</Label>
                  <div className="font-medium">
                    {selectedExitRequest.employee ?
                      `${selectedExitRequest.employee.firstName} ${selectedExitRequest.employee.lastName}` :
                      'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Exit Type</Label>
                  <div className="font-medium">{selectedExitRequest.exitType}</div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Date</Label>
                  <div className="font-medium">
                    {selectedExitRequest.exitDate ? new Date(selectedExitRequest.exitDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) : 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Exit Time</Label>
                  <div className="font-medium">{selectedExitRequest.exitTime}</div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Comeback Time</Label>
                  <div className="font-medium">{selectedExitRequest.comebackTime || 'N/A'}</div>
                </div>
                <div>
                  <Label>Not Comeback</Label>
                  <div className="font-medium">{selectedExitRequest.notComeback ? 'Yes' : 'No'}</div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label>Status</Label>
                  <div className="font-medium">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-semibold ${
                        selectedExitRequest.status === "approved"
                          ? "bg-green-100 text-green-800"
                          : selectedExitRequest.status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {selectedExitRequest.status.charAt(0).toUpperCase() + selectedExitRequest.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <Label>Reason</Label>
                <div className="font-medium">{selectedExitRequest.reason}</div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewExitRequestOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk mengedit exit request */}
      <Dialog open={isEditExitRequestOpen} onOpenChange={setIsEditExitRequestOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Exit Request</DialogTitle>
            <DialogDescription>
              Update the details of the exit request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-exitType">Exit Type</Label>
                <Select
                  value={editExitForm.exitType}
                  onValueChange={(value) => setEditExitForm({...editExitForm, exitType: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select exit type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Work">Work</SelectItem>
                    <SelectItem value="Personal">Personal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="edit-exitDate">Date</Label>
                <Input
                  type="date"
                  id="edit-exitDate"
                  value={editExitForm.exitDate}
                  onChange={(e) => setEditExitForm({...editExitForm, exitDate: e.target.value})}
                  disabled={true}
                />
              </div>
              <div>
                <Label htmlFor="edit-exitTime">Exit Time</Label>
                <Input
                  type="time"
                  id="edit-exitTime"
                  value={editExitForm.exitTime}
                  onChange={(e) => setEditExitForm({...editExitForm, exitTime: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit-comebackTime">Comeback Time</Label>
                <Input
                  type="time"
                  id="edit-comebackTime"
                  value={editExitForm.comebackTime}
                  onChange={(e) => setEditExitForm({...editExitForm, comebackTime: e.target.value})}
                  disabled={editExitForm.notComeback}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-notComeback"
                checked={editExitForm.notComeback}
                onCheckedChange={(checked) => setEditExitForm({...editExitForm, notComeback: checked as boolean})}
              />
              <Label htmlFor="edit-notComeback">Not coming back to school</Label>
            </div>
            <div>
              <Label htmlFor="edit-exit-reason">Reason</Label>
              <Textarea
                id="edit-exit-reason"
                value={editExitForm.reason}
                onChange={(e) => setEditExitForm({...editExitForm, reason: e.target.value})}
                placeholder="Enter reason for exit from school"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditExitRequestOpen(false)}>Cancel</Button>
            <Button onClick={async () => {
              try {
                logger.debug('Updating exit request');

                // Double-check if the request is still in pending status
                const currentRequest = exitRequests.find(req => req.id === editExitForm.id);
                if (currentRequest && currentRequest.status !== 'pending') {
                  toast({
                    title: "Cannot Edit",
                    description: `This exit request has already been ${currentRequest.status}. Only pending requests can be edited.`,
                    variant: "destructive",
                  });
                  setIsEditExitRequestOpen(false);
                  return;
                }

                // Validate required fields
                if (!editExitForm.exitType) {
                  toast({
                    title: "Error",
                    description: "Exit type is required",
                    variant: "destructive",
                  });
                  return;
                }

                if (!editExitForm.exitTime) {
                  toast({
                    title: "Error",
                    description: "Exit time is required",
                    variant: "destructive",
                  });
                  return;
                }

                if (!editExitForm.notComeback && !editExitForm.comebackTime) {
                  toast({
                    title: "Error",
                    description: "Comeback time is required when not checking 'Not coming back'",
                    variant: "destructive",
                  });
                  return;
                }

                if (!editExitForm.reason) {
                  toast({
                    title: "Error",
                    description: "Reason is required",
                    variant: "destructive",
                  });
                  return;
                }

                // Validate exit time and comeback time
                if (!editExitForm.notComeback && editExitForm.comebackTime && editExitForm.exitTime) {
                  const exitTimeMinutes = convertTimeToMinutes(editExitForm.exitTime);
                  const comebackTimeMinutes = convertTimeToMinutes(editExitForm.comebackTime);

                  console.log('Exit time minutes:', exitTimeMinutes);
                  console.log('Comeback time minutes:', comebackTimeMinutes);

                  if (comebackTimeMinutes <= exitTimeMinutes) {
                    toast({
                      title: "Error",
                      description: "Comeback time must be later than exit time",
                      variant: "destructive",
                    });
                    return;
                  }
                }

                const requestData = {
                  exitType: editExitForm.exitType,
                  exitDate: new Date().toISOString().split('T')[0], // Always use current date
                  exitTime: editExitForm.exitTime,
                  comebackTime: editExitForm.notComeback ? null : editExitForm.comebackTime,
                  notComeback: editExitForm.notComeback,
                  reason: editExitForm.reason
                };
                console.log('Request data:', requestData);

                const response = await fetch(`/api/exit-requests/${editExitForm.id}/edit`, {
                  method: 'PUT',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(requestData),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  console.error('Error response:', errorData);
                  throw new Error(errorData.error || 'Failed to update exit request');
                }

                const responseData = await response.json();
                console.log('Response data:', responseData);

                // Reset form and close dialog
                setIsEditExitRequestOpen(false);

                // Refresh data
                await fetchExitRequests();

                toast({
                  title: "Success",
                  description: "Exit request updated successfully",
                });
              } catch (error) {
                console.error('Error updating exit request:', error);
                toast({
                  title: "Error",
                  description: "Failed to update exit request",
                  variant: "destructive",
                });
              }
            }}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk leave type diimplementasikan di LeaveTypesTab.tsx */}

      {/* Dialog untuk edit leave type juga diimplementasikan di LeaveTypesTab.tsx */}
    </div>
  );
}
