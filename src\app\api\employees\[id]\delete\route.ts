/**
 * API Route: DELETE /api/employees/[id]/delete
 *
 * Deskripsi: Melakukan soft delete pada data karyawan berdasarkan ID
 * Penggunaan: Tombol hapus di halaman daftar karyawan
 *
 * Path Parameters:
 * - id: ID karyawan (number)
 *
 * Response:
 * - 200: <PERSON><PERSON><PERSON> berhasil di-soft-delete
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Karyawan tidak ditemukan
 * - 409: Karyawan tidak dapat dihapus (memiliki relasi)
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

export async function DELETE(_request: NextRequest) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Extract ID from URL path
    const id = parseInt(_request.nextUrl.pathname.split('/')[3] || '0');

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid employee ID' },
        { status: 400 }
      );
    }

    // Cek apakah karyawan ada
    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        departmentHead: true,
        subordinates: true,
        user: true
      }
    });

    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Cek apakah karyawan adalah kepala departemen
    if (employee.departmentHead.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete employee who is a department head' },
        { status: 409 }
      );
    }

    // Cek apakah karyawan memiliki bawahan
    if (employee.subordinates.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete employee who has subordinates' },
        { status: 409 }
      );
    }

    // Soft delete karyawan (tidak menghapus user)
    await prisma.employee.update({
      where: { id },
      data: {
        isDeleted: true,
        deletedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Employee soft deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting employee:', error);

    // Cek apakah error karena relasi
    if (error.code === 'P2003') {
      return NextResponse.json(
        { error: 'Cannot delete employee because it is referenced by other records' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete employee' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
