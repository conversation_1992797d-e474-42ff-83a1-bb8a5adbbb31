import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { SalaryHonor } from "@/lib/types";

// Define schema for form validation
const formSchema = z.object({
  nama: z.string().min(1, "Nama is required"),
  gp1: z.coerce.number().optional(),
  pph_dibayar_sklh: z.coerce.number().optional(),
  tot_tun_tep: z.coerce.number().optional(),
  hnr: z.coerce.number().optional(),
  jp: z.coerce.number().optional(),
  tot_hnr: z.coerce.number().optional(),
  jlh_hdr: z.coerce.number().optional(),
  hnr_hdr: z.coerce.number().optional(),
  tot_hnr_hdr: z.coerce.number().optional(),
  sblm_pph: z.coerce.number().optional(),
  jlhxc: z.coerce.number().optional(),
  xc: z.coerce.number().optional(),
  tmbhn: z.coerce.number().optional(),
  gaji_bruto: z.coerce.number().optional(),
  bruto_stlh_pot: z.coerce.number().optional(),
  nettstlhjamsostek: z.coerce.number().optional(),
  pph21: z.coerce.number().optional(),
  pinj_lainnya: z.coerce.number().optional(),
  iuran_wajib: z.coerce.number().optional(),
  pinj_kop: z.coerce.number().optional(),
  piutang: z.coerce.number().optional(),
  pot_bank: z.coerce.number().optional(),
  gaji_netto: z.coerce.number().min(0, "Gaji netto must be a positive number"),
});

type FormValues = z.infer<typeof formSchema>;

interface HonorSalaryFormProps {
  initialData?: SalaryHonor;
  onSubmit: (data: FormValues) => void;
}

export function HonorSalaryForm({ initialData, onSubmit }: HonorSalaryFormProps) {
  const [isCalculating, setIsCalculating] = useState(false);

  // Initialize form with default values or initial data
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData ? {
      nama: initialData.nama,
      gp1: initialData.gp1 || 0,
      pph_dibayar_sklh: initialData.pph_dibayar_sklh || 0,
      tot_tun_tep: initialData.tot_tun_tep || 0,
      hnr: initialData.hnr || 0,
      jp: initialData.jp || 0,
      tot_hnr: initialData.tot_hnr || 0,
      jlh_hdr: initialData.jlh_hdr || 0,
      hnr_hdr: initialData.hnr_hdr || 0,
      tot_hnr_hdr: initialData.tot_hnr_hdr || 0,
      sblm_pph: initialData.sblm_pph || 0,
      jlhxc: initialData.jlhxc || 0,
      xc: initialData.xc || 0,
      tmbhn: initialData.tmbhn || 0,
      gaji_bruto: initialData.gaji_bruto || 0,
      bruto_stlh_pot: initialData.bruto_stlh_pot || 0,
      nettstlhjamsostek: initialData.nettstlhjamsostek || 0,
      pph21: initialData.pph21 || 0,
      pinj_lainnya: initialData.pinj_lainnya || 0,
      iuran_wajib: initialData.iuran_wajib || 0,
      pinj_kop: initialData.pinj_kop || 0,
      piutang: initialData.piutang || 0,
      pot_bank: initialData.pot_bank || 0,
      gaji_netto: initialData.gaji_netto,
    } : {
      nama: "",
      gp1: 0,
      pph_dibayar_sklh: 0,
      tot_tun_tep: 0,
      hnr: 0,
      jp: 0,
      tot_hnr: 0,
      jlh_hdr: 0,
      hnr_hdr: 0,
      tot_hnr_hdr: 0,
      sblm_pph: 0,
      jlhxc: 0,
      xc: 0,
      tmbhn: 0,
      gaji_bruto: 0,
      bruto_stlh_pot: 0,
      nettstlhjamsostek: 0,
      pph21: 0,
      pinj_lainnya: 0,
      iuran_wajib: 0,
      pinj_kop: 0,
      piutang: 0,
      pot_bank: 0,
      gaji_netto: 0,
    },
  });

  // Calculate totals
  const calculateTotals = () => {
    setIsCalculating(true);
    
    try {
      const values = form.getValues();
      
      // Calculate tot_tun_tep (total tunjangan tetap)
      const totTunTep = (values.gp1 || 0) + (values.pph_dibayar_sklh || 0);
      form.setValue("tot_tun_tep", totTunTep);
      
      // Calculate tot_hnr (total honor)
      const totHnr = (values.hnr || 0) + (values.jp || 0);
      form.setValue("tot_hnr", totHnr);
      
      // Calculate tot_hnr_hdr (total honor hadir)
      if (values.jlh_hdr && values.hnr_hdr) {
        const totHnrHdr = values.jlh_hdr * values.hnr_hdr;
        form.setValue("tot_hnr_hdr", totHnrHdr);
      }
      
      // Calculate sblm_pph (sebelum pph)
      const sblmPph = (values.tot_tun_tep || 0) + (values.tot_hnr || 0) + (values.tot_hnr_hdr || 0);
      form.setValue("sblm_pph", sblmPph);
      
      // Calculate xc total if jlhxc is provided
      if (values.jlhxc && values.xc) {
        const xcTotal = values.jlhxc * values.xc;
        // This is just for calculation, not stored in a field
      }
      
      // Calculate gaji_bruto (gross salary)
      const gajiBruto = (values.sblm_pph || 0) + (values.xc || 0) + (values.tmbhn || 0);
      form.setValue("gaji_bruto", gajiBruto);
      
      // Calculate bruto_stlh_pot (gross after deductions)
      // This is a placeholder calculation, adjust as needed
      const brutoStlhPot = gajiBruto;
      form.setValue("bruto_stlh_pot", brutoStlhPot);
      
      // Calculate nettstlhjamsostek (net after jamsostek)
      // This is a placeholder calculation, adjust as needed
      const nettStlhJamsostek = brutoStlhPot;
      form.setValue("nettstlhjamsostek", nettStlhJamsostek);
      
      // Calculate total deductions
      const totalDeductions = (
        (values.pph21 || 0) +
        (values.pinj_lainnya || 0) +
        (values.iuran_wajib || 0) +
        (values.pinj_kop || 0) +
        (values.piutang || 0) +
        (values.pot_bank || 0)
      );
      
      // Calculate gaji_netto (net salary)
      const gajiNetto = gajiBruto - totalDeductions;
      form.setValue("gaji_netto", gajiNetto);
    } catch (error) {
      console.error("Error calculating totals:", error);
    } finally {
      setIsCalculating(false);
    }
  };

  // Handle form submission
  const handleSubmit = (data: FormValues) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 max-h-[70vh] overflow-y-auto p-1">
        <div className="grid grid-cols-2 gap-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="nama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nama</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gp1"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GP1</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pph_dibayar_sklh"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>PPh Dibayar Sekolah</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tot_tun_tep"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Tunjangan Tetap</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hnr"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Honor</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="jp"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>JP</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tot_hnr"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Honor</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="jlh_hdr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Hadir</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hnr_hdr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Honor Hadir</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="tot_hnr_hdr"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Honor Hadir</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sblm_pph"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sebelum PPh</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="jlhxc"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah XC</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="xc"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>XC</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Additional Information and Deductions */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="tmbhn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tambahan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gaji_bruto"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gaji Bruto</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bruto_stlh_pot"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bruto Setelah Potongan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nettstlhjamsostek"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nett Setelah Jamsostek</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pph21"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>PPh21</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pinj_lainnya"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pinjaman Lainnya</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="iuran_wajib"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Iuran Wajib</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pinj_kop"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pinjaman Koperasi</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="piutang"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Piutang</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pot_bank"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Potongan Bank</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gaji_netto"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gaji Netto</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-between">
          <Button 
            type="button" 
            variant="outline" 
            onClick={calculateTotals}
            disabled={isCalculating}
          >
            {isCalculating ? "Calculating..." : "Calculate Totals"}
          </Button>
          <Button type="submit">Submit</Button>
        </div>
      </form>
    </Form>
  );
}
