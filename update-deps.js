// Script to update package-lock.json with resolutions
const fs = require('fs');
const path = require('path');

// Function to recursively update dependencies in package-lock.json
function updateDependencies(packages, resolutions) {
  for (const [pkgName, pkg] of Object.entries(packages)) {
    // Skip the root package
    if (pkgName === '') continue;
    
    // Check if this package has dependencies that need to be updated
    if (pkg.dependencies) {
      for (const [depName, depInfo] of Object.entries(pkg.dependencies)) {
        if (resolutions[depName]) {
          console.log(`Updating ${depName} in ${pkgName}`);
          // Remove the dependency to let npm resolve it with the resolution
          delete pkg.dependencies[depName];
        }
      }
    }
    
    // Recursively update nested dependencies
    if (pkg.dependencies) {
      updateDependencies(pkg.dependencies, resolutions);
    }
  }
}

try {
  // Read package.json to get resolutions
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const resolutions = packageJson.resolutions || {};
  
  if (Object.keys(resolutions).length === 0) {
    console.log('No resolutions found in package.json');
    process.exit(0);
  }
  
  console.log('Resolutions found:', resolutions);
  
  // Check if package-lock.json exists
  const lockFilePath = path.join(__dirname, 'package-lock.json');
  if (!fs.existsSync(lockFilePath)) {
    console.log('package-lock.json not found');
    process.exit(0);
  }
  
  // Read and parse package-lock.json
  const packageLock = JSON.parse(fs.readFileSync(lockFilePath, 'utf8'));
  
  // Update dependencies in package-lock.json
  if (packageLock.packages) {
    console.log('Updating dependencies in package-lock.json...');
    updateDependencies(packageLock.packages, resolutions);
    
    // Write updated package-lock.json
    fs.writeFileSync(lockFilePath, JSON.stringify(packageLock, null, 2));
    console.log('package-lock.json updated successfully');
  } else {
    console.log('Invalid package-lock.json format');
  }
} catch (error) {
  console.error('Error:', error);
  process.exit(1);
}
