/**
 * API Route: GET /api/dashboard/overview
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { LeaveRequestStatus, LoanStatus, MemberStatus } from '@prisma/client';
import { cookies } from 'next/headers';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/db';

// Helper function removed since we're not using BigInt values anymore

export async function GET() {
  try {
    // Test database connection first
    try {
      await prisma.$queryRaw`SELECT 1`;
      logger.debug('Dashboard API - Database connection successful');
    } catch (connectionError) {
      logger.error('Dashboard API - Database connection test failed:', connectionError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      );
    }

    // Authentication logging
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      logger.debug('Dashboard API - No user cookie found');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse user cookie to verify role
    const userData = JSON.parse(userCookie.value);

    if (userData.role !== 'ADMIN') {
      console.log('Unauthorized access attempt by role:', userData.role);
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    console.log('Starting database queries...');

    // Individual queries with error handling
    // Get all non-academic departments
    const nonAcademicDepartments = await prisma.department.findMany({
      where: {
        NOT: {
          name: {
            in: ['TK', 'SD', 'SMP', 'SMA', 'SMP-SMA']
          }
        }
      },
      select: {
        id: true,
        name: true
      }
    }).catch(err => {
      console.error('Non-academic departments query failed:', err);
      return [];
    });

    console.log('Non-academic departments:', nonAcademicDepartments);

    // Query employee counts for each department
    const departmentCounts = await Promise.all(
      nonAcademicDepartments.map(dept =>
        prisma.employee.count({
          where: {
            departmentId: dept.id,
            NOT: {
              OR: [
                { employeeId: { startsWith: 'EMP' } }, // Mengecualikan admin web dan semua yang diawali EMP
                { employeeId: { startsWith: 'EXT' } } // Mengecualikan anggota eksternal
              ]
            },
            isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
          }
        }).then(count => ({
          id: dept.id,
          name: dept.name,
          count
        })).catch(err => {
          console.error(`Count for department ${dept.name} failed:`, err);
          return { id: dept.id, name: dept.name, count: 0 };
        })
      )
    );

    console.log('Department counts:', departmentCounts);

    const [totalCount, tetapCount, kontrakCount, honorCount, tkCount, sdCount, smpCount, smaCount, smpSmaCount, staffCount, managerCount] = await Promise.all([
      prisma.employee.count({
        where: {
          AND: [
            {
              status: {
                in: ['Tetap', 'Kontrak', 'Honor']
              }
            },
            {
              NOT: {
                OR: [
                  { employeeId: { startsWith: 'EMP' } }, // Mengecualikan admin web dan semua yang diawali EMP
                  { employeeId: { startsWith: 'EXT' } } // Mengecualikan anggota eksternal
                ]
              }
            },
            {
              isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
            }
          ]
        }
      }),
      prisma.employee.count({
        where: {
          AND: [
            { status: 'Tetap' },
            { NOT: {
              OR: [
                { employeeId: { startsWith: 'EMP' } },
                { employeeId: { startsWith: 'EXT' } }
              ]
            } },
            { isDeleted: false } // Mengecualikan employee yang sudah di-soft-delete
          ]
        }
      }),
      prisma.employee.count({
        where: {
          AND: [
            { status: 'Kontrak' },
            { NOT: {
              OR: [
                { employeeId: { startsWith: 'EMP' } },
                { employeeId: { startsWith: 'EXT' } }
              ]
            } },
            { isDeleted: false } // Mengecualikan employee yang sudah di-soft-delete
          ]
        }
      }),
      prisma.employee.count({
        where: {
          AND: [
            { status: 'Honor' },
            { NOT: {
              OR: [
                { employeeId: { startsWith: 'EMP' } },
                { employeeId: { startsWith: 'EXT' } }
              ]
            } },
            { isDeleted: false } // Mengecualikan employee yang sudah di-soft-delete
          ]
        }
      }),
      // TK department employees count
      prisma.employee.count({
        where: {
          department: {
            name: { equals: 'TK' }
          },
          NOT: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ]
          },
          isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
        }
      }),

      // SD department employees count
      prisma.employee.count({
        where: {
          department: {
            name: { equals: 'SD' }
          },
          NOT: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ]
          },
          isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
        }
      }),

      // SMP department employees count
      prisma.employee.count({
        where: {
          department: {
            name: { equals: 'SMP' }
          },
          NOT: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ]
          },
          isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
        }
      }),

      // SMA department employees count
      prisma.employee.count({
        where: {
          department: {
            name: { equals: 'SMA' }
          },
          NOT: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ]
          },
          isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
        }
      }),

      // SMP-SMA department employees count
      prisma.employee.count({
        where: {
          department: {
            name: { equals: 'SMP-SMA' }
          },
          NOT: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ]
          },
          isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
        }
      }),

      // Non-academic employees count (employees not in TK, SD, SMP, SMA, or SMP-SMA departments)
      prisma.employee.count({
        where: {
          AND: [
            {
              NOT: {
                OR: [
                  { employeeId: { startsWith: 'EMP' } },
                  { employeeId: { startsWith: 'EXT' } }
                ]
              }
            },
            {
              NOT: {
                OR: [
                  { department: { name: { equals: 'TK' } } },
                  { department: { name: { equals: 'SD' } } },
                  { department: { name: { equals: 'SMP' } } },
                  { department: { name: { equals: 'SMA' } } },
                  { department: { name: { equals: 'SMP-SMA' } } }
                ]
              }
            },
            {
              isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
            }
          ]
        }
      }),

      // Manager count (employees with managerial positions)
      prisma.employee.count({
        where: {
          AND: [
            {
              NOT: {
                OR: [
                  { employeeId: { startsWith: 'EMP' } },
                  { employeeId: { startsWith: 'EXT' } }
                ]
              }
            },
            {
              OR: [
                // Uppercase variants
                { position: { title: { contains: 'KEPALA' } } },
                { position: { title: { contains: 'MANAGER' } } },
                { position: { title: { contains: 'DIREKTUR' } } },
                { position: { title: { contains: 'CHIEF' } } },
                { position: { title: { contains: 'KOORDINATOR' } } },
                { position: { title: { contains: 'PSIKOLOG' } } },
                { position: { title: { contains: 'HEADMASTER' } } },

                // Lowercase variants
                { position: { title: { contains: 'kepala' } } },
                { position: { title: { contains: 'manager' } } },
                { position: { title: { contains: 'direktur' } } },
                { position: { title: { contains: 'chief' } } },
                { position: { title: { contains: 'koordinator' } } },
                { position: { title: { contains: 'psikolog' } } },
                { position: { title: { contains: 'headmaster' } } }
              ]
            },
            {
              isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
            }
          ]
        }
      })
    ]).catch(err => {
      console.error('Employee counts query failed:', err);
      return [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    });

    const queries = [
      // Department count already fetched
      Promise.resolve(0),

      // Project count remains 0 as it doesn't exist
      Promise.resolve(0),

      // Total leave requests
      prisma.leaveRequest.count().catch(err => {
        console.error('Total leave requests count query failed:', err);
        return 0;
      }),

      // Active leaves
      prisma.leaveRequest.count({
        where: {
          status: LeaveRequestStatus.approved,
          endDate: {
            gte: new Date()
          }
        }
      }).catch(err => {
        console.error('Active leave count query failed:', err);
        return 0;
      }),

      // Pending leaves
      prisma.leaveRequest.count({
        where: { status: LeaveRequestStatus.pending }
      }).catch(err => {
        console.error('Pending leave count query failed:', err);
        return 0;
      }),

      // Late requests count
      prisma.lateRequest.count().catch(err => {
        console.error('Late requests count query failed:', err);
        return 0;
      }),

      // Pending late requests
      prisma.lateRequest.count({
        where: { status: 'pending' }
      }).catch(err => {
        console.error('Pending late requests count query failed:', err);
        return 0;
      }),

      // Exit requests count
      prisma.exitRequest.count().catch(err => {
        console.error('Exit requests count query failed:', err);
        return 0;
      }),

      // Pending exit requests
      prisma.exitRequest.count({
        where: { status: 'pending' }
      }).catch(err => {
        console.error('Pending exit requests count query failed:', err);
        return 0;
      }),

      // Payroll (removed - using fixed value since table was removed)
      Promise.resolve({ _sum: { totalAmount: 0 } }).catch(err => {
        console.error('Payroll aggregate query failed:', err);
        return { _sum: { totalAmount: 0 } };
      }),

      // Koperasi loans
      prisma.koperasiLoan.count({
        where: {
          status: LoanStatus.approved,
          NOT: {
            status: LoanStatus.completed
          }
        }
      }).catch(err => {
        console.error('Active loans count query failed:', err);
        return 0;
      }),

      // Koperasi members (total)
      prisma.koperasiMember.count().catch(err => {
        console.error('Koperasi members count query failed:', err);
        return 0;
      }),

      // Koperasi internal members
      prisma.koperasiMember.count({
        where: {
          employee: {
            NOT: {
              employeeId: { startsWith: 'EXT' }
            }
          }
        }
      }).catch(err => {
        console.error('Koperasi internal members count query failed:', err);
        return 0;
      }),

      // Koperasi external members
      prisma.koperasiMember.count({
        where: {
          employee: {
            employeeId: { startsWith: 'EXT' }
          }
        }
      }).catch(err => {
        console.error('Koperasi external members count query failed:', err);
        return 0;
      }),

      // Koperasi active members
      prisma.koperasiMember.count({
        where: {
          status: MemberStatus.active
        }
      }).catch(err => {
        console.error('Koperasi active members count query failed:', err);
        return 0;
      }),

      // Koperasi inactive members
      prisma.koperasiMember.count({
        where: {
          status: MemberStatus.inactive
        }
      }).catch(err => {
        console.error('Koperasi inactive members count query failed:', err);
        return 0;
      }),

      // Koperasi savings - menggunakan pendekatan yang sama dengan halaman koperasi
      Promise.all([
        // Get total savings from KoperasiMember
        prisma.koperasiMember.aggregate({
          where: { status: 'active' },
          _sum: {
            totalSavings: true
          }
        }),
        // Get total one-time contributions
        prisma.koperasiMember.aggregate({
          where: { status: 'active' },
          _sum: {
            oneTimeContribution: true
          }
        }),
        // Get total monthly contributions
        prisma.koperasiMember.aggregate({
          where: { status: 'active' },
          _sum: {
            monthlyContribution: true
          }
        }),
        // Get total optional contributions
        prisma.koperasiMember.aggregate({
          where: { status: 'active' },
          _sum: {
            optionalContribution: true
          }
        })
      ]).then(([totalSavings, oneTimeContrib, monthlyContrib, optionalContrib]) => {
        // Hitung total savings dari komponen-komponennya jika totalSavings tidak tersedia
        const oneTimeAmount = Number(oneTimeContrib?._sum?.oneTimeContribution || 0);
        const monthlyAmount = Number(monthlyContrib?._sum?.monthlyContribution || 0);
        const optionalAmount = Number(optionalContrib?._sum?.optionalContribution || 0);
        const calculatedTotal = oneTimeAmount + monthlyAmount + optionalAmount;

        // Gunakan totalSavings dari database jika tersedia, jika tidak gunakan hasil perhitungan
        const finalTotal = Number(totalSavings?._sum?.totalSavings || 0) || calculatedTotal;

        console.log('Dashboard - Calculated total savings:', {
          fromDatabase: Number(totalSavings?._sum?.totalSavings || 0),
          calculated: calculatedTotal,
          final: finalTotal
        });

        return finalTotal;
      }).catch(err => {
        console.error('Koperasi savings aggregate query failed:', err);
        return 0;
      }),

      // Latest salary staff update
      prisma.salaryStaff.findFirst({
        orderBy: {
          updatedAt: 'desc'
        },
        select: {
          updatedAt: true
        }
      }).catch(err => {
        console.error('Latest salary staff update query failed:', err);
        return { updatedAt: null };
      }),

      // Latest salary guru update
      prisma.salaryGuru.findFirst({
        orderBy: {
          updatedAt: 'desc'
        },
        select: {
          updatedAt: true
        }
      }).catch(err => {
        console.error('Latest salary guru update query failed:', err);
        return { updatedAt: null };
      }),

      // Latest salary honor update
      prisma.salaryHonor.findFirst({
        orderBy: {
          updatedAt: 'desc'
        },
        select: {
          updatedAt: true
        }
      }).catch(err => {
        console.error('Latest salary honor update query failed:', err);
        return { updatedAt: null };
      })
    ];

    console.log('Executing all queries...');
    const results = await Promise.all(queries);

    const response = {
      organization: {
        employeeCount: totalCount,
        departmentCount: results[0] || 0,
        tetapCount,
        kontrakCount,
        honorCount,
        tkCount,
        sdCount,
        smpCount,
        smaCount,
        smpSmaCount,
        staffCount,
        managerCount,
        nonAcademicDepartments: departmentCounts
      },
      leaves: {
        totalLeaveRequests: results[2],
        activeLeaves: results[3],
        pendingLeaves: results[4],
        totalLateRequests: results[5],
        pendingLateRequests: results[6],
        totalExitRequests: results[7],
        pendingExitRequests: results[8]
      },
      financial: {
        totalPayroll: '0', // Temporarily hardcoded since payroll model doesn't exist
        activeLoans: results[10]
      },
      koperasi: {
        totalMembers: results[11],
        internalMembers: results[12],
        externalMembers: results[13],
        activeMembers: results[14],
        inactiveMembers: results[15],
        totalSavings: results[16], // Menggunakan hasil perhitungan dari query koperasi savings
        activeLoans: results[10]
      },
      salary: {
        lastStaffUpdate: results[17]?.updatedAt || null,
        lastGuruUpdate: results[18]?.updatedAt || null,
        lastHonorUpdate: results[19]?.updatedAt || null
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    logger.error('Failed to fetch dashboard overview:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}





