"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

/**
 * Komponen untuk memicu iklan Adsterra Social Bar saat navigasi
 */
export function AdsterraNavTrigger() {
  const pathname = usePathname();
  const [previousPath, setPreviousPath] = useState("");

  useEffect(() => {
    // Hanya jalankan jika path berubah (navigasi terjadi)
    if (pathname !== previousPath) {
      setPreviousPath(pathname);
      
      // Hapus script lama jika ada
      const existingScript = document.getElementById("adsterra-social-bar");
      if (existingScript) {
        existingScript.remove();
      }
      
      // Buat dan tambahkan script baru
      const script = document.createElement("script");
      script.id = "adsterra-social-bar";
      script.src = "//pl26499283.profitableratecpm.com/f4/d0/0c/f4d00c1107e7b45f8ad3c413098c0576.js";
      script.async = true;
      
      // Tambahkan script ke body
      document.body.appendChild(script);
    }
  }, [pathname, previousPath]);

  // Komponen ini tidak merender apa pun
  return null;
}
