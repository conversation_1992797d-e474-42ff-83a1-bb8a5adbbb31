/**
 * API Route: PUT /api/exit-requests/[id]/update
 *
 * Deskripsi: Memperbarui status permintaan keluar (approve/reject)
 * Penggunaan: Form approval permintaan keluar
 *
 * Path Parameters:
 * - id: ID permintaan keluar (number)
 *
 * Body:
 * - status: Status baru (string) - 'approved' atau 'rejected'
 * - approvedById: ID karyawan yang melakukan approval (number)
 * - notes: Catatan (string, opsional)
 *
 * Response:
 * - 200: Permintaan keluar berhasil diperbarui
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Permintaan keluar tidak ditemukan
 * - 500: Error server
 */

import { NextResponse, NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;
    // Get ID from params
    const id = params.id;
    const data = await request.json();
    const { status, approvedById } = data;

    // Check if exit request exists
    const exitRequest = await prisma.exitRequest.findUnique({
      where: { id: parseInt(id) },
      include: {
        employee: {
          include: {
            department: {
              include: {
                head: true
              }
            }
          }
        }
      }
    });

    if (!exitRequest) {
      return NextResponse.json({ error: 'Exit request not found' }, { status: 404 });
    }

    // Validasi: Hanya admin, supervisor, dan HEAD yang dapat approve/reject
    if (userRole !== 'ADMIN' && userRole !== 'SUPERVISOR' && userRole !== 'HEAD') {
      return NextResponse.json(
        { error: 'Only admin, supervisor, and head can approve or reject exit requests' },
        { status: 403 }
      );
    }

    // Supervisor dan HEAD tidak dapat approve/reject exit request miliknya sendiri
    const isOwnRequest = exitRequest.employeeId === parseInt(userData.id);
    if ((userRole === 'SUPERVISOR' || userRole === 'HEAD') && isOwnRequest) {
      return NextResponse.json(
        { error: 'You cannot approve/reject your own exit requests' },
        { status: 403 }
      );
    }

    // Supervisor hanya dapat approve/reject exit request dari bawahannya
    if (userRole === 'SUPERVISOR') {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const supervisorId = userData.employeeId ? userData.employeeId : userData.id;

      // Cek apakah supervisor adalah head dari departemen karyawan
      const isSupervisorOfEmployee =
        exitRequest.employee?.department?.head?.id === parseInt(supervisorId) ||
        exitRequest.employee?.department?.head?.employeeId === supervisorId;

      if (!isSupervisorOfEmployee) {
        return NextResponse.json(
          { error: 'You can only approve/reject exit requests from your subordinates' },
          { status: 403 }
        );
      }
    }

    // HEAD hanya dapat approve/reject exit request dari karyawan dengan posisi Chief atau Kepala
    else if (userRole === 'HEAD') {
      // Ambil data posisi karyawan
      const employeeWithPosition = await prisma.employee.findUnique({
        where: { id: exitRequest.employeeId },
        include: { position: true }
      });

      const positionTitle = employeeWithPosition?.position?.title || '';
      const isChiefOrKepalaOrPsikolog =
        positionTitle.toLowerCase().includes('chief') ||
        positionTitle.toLowerCase().includes('kepala') ||
        positionTitle.toLowerCase().includes('psikolog');

      if (!isChiefOrKepalaOrPsikolog) {
        console.error(`PUT /api/exit-requests/${id}/update - Forbidden: Employee is not a Chief, Kepala, or Psikolog`);
        return NextResponse.json(
          { error: 'You can only approve/reject exit requests from Chief, Kepala, or Psikolog positions' },
          { status: 403 }
        );
      }
    }

    // Update exit request
    const updatedExitRequest = await prisma.exitRequest.update({
      where: { id: parseInt(id) },
      data: {
        status,
        approvedById: parseInt(approvedById),
        approvedAt: new Date(),
        // Tidak mengupdate reason saat approve/reject
      },
    });

    return NextResponse.json({
      message: `Exit request ${status} successfully`,
      exitRequest: updatedExitRequest,
    });
  } catch (error: any) {
    console.error('Error updating exit request:', error);
    return NextResponse.json(
      { error: 'Failed to update exit request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
