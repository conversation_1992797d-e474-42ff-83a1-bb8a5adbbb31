import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get user from cookie or request header
    const userCookie = request.cookies.get('user')?.value;

    if (!userCookie) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated'
      }, { status: 401 });
    }

    // Parse user data from cookie
    const userData = JSON.parse(userCookie);

    // If we have a user ID, fetch the latest user data from database
    if (userData.id) {
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userData.id) },
        include: {
          employee: true
        }
      });

      if (user) {
        // Prepare user data to return (excluding password)
        const updatedUserData = {
          ...userData,
          id: user.id.toString(),
          name: userData.name || user.username,
        };

        // If user has employee data, add additional information
        if (user.employee) {
          updatedUserData.name = `${user.employee.firstName || ''} ${user.employee.lastName || ''}`.trim() || user.username;

          // Log the employee ID for debugging
          console.log('GET /api/auth/me - User employeeId:', user.employeeId);
        }

        return NextResponse.json({
          success: true,
          user: updatedUserData
        });
      }
    }

    // If we couldn't fetch updated data, return the data from cookie
    return NextResponse.json({
      success: true,
      user: userData
    });

  } catch (error) {
    console.error('GET /api/auth/me - Error:', error);
    return NextResponse.json({
      success: false,
      message: 'Error fetching user data'
    }, { status: 500 });
  }
}
