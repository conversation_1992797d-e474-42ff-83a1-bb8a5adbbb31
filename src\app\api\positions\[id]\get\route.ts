/**
 * API Route: GET /api/positions/[id]/get
 *
 * Deskripsi: Mengambil detail posisi berdasarkan ID
 * Penggunaan: Halaman detail posisi, form edit posisi
 *
 * Path Parameters:
 * - id: ID posisi (number)
 *
 * Response:
 * - 200: Detail posisi
 * - 400: ID tidak valid
 * - 401: Tidak terautentikasi
 * - 404: Posisi tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/db';



export async function GET(request: Request) {
  // Get the ID from the URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 2]; // Get the ID from the URL path

  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const positionId = Number(id);

    if (isNaN(positionId)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    const position = await prisma.position.findUnique({
      where: { id: positionId },
      include: {
        department: true,
        employees: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    if (!position) {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    return NextResponse.json(position);
  } catch (error) {
    console.error('Error fetching position:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
