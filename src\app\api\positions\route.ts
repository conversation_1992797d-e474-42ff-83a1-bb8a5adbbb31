/**
 * API Route: /api/positions
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/db';

export async function GET() {
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const positions = await prisma.position.findMany({
      include: {
        department: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        title: 'asc'
      }
    });

    return NextResponse.json(positions);
  } catch (error) {
    console.error('Error fetching positions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    console.log('Received data:', data); // Debug log

    // Validate required fields
    if (!data.title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }

    if (!data.departmentId && data.departmentId !== 0) {
      return NextResponse.json(
        { error: 'Department ID is required' },
        { status: 400 }
      );
    }

    // Parse and validate departmentId
    const departmentId = parseInt(data.departmentId);
    if (isNaN(departmentId)) {
      return NextResponse.json(
        { error: 'Invalid department ID format' },
        { status: 400 }
      );
    }

    // Validate department exists
    const department = await prisma.department.findUnique({
      where: { id: departmentId }
    });

    if (!department) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Handle salary values
    let minSalary = 0;
    let maxSalary = 0;

    if (data.minSalary !== undefined && data.minSalary !== '') {
      minSalary = parseFloat(data.minSalary);
      if (isNaN(minSalary)) {
        return NextResponse.json(
          { error: 'Invalid minimum salary format' },
          { status: 400 }
        );
      }
    }

    if (data.maxSalary !== undefined && data.maxSalary !== '') {
      maxSalary = parseFloat(data.maxSalary);
      if (isNaN(maxSalary)) {
        return NextResponse.json(
          { error: 'Invalid maximum salary format' },
          { status: 400 }
        );
      }
    }

    // Validate salary range if both are provided
    if (minSalary > 0 && maxSalary > 0 && minSalary > maxSalary) {
      return NextResponse.json(
        { error: 'Minimum salary cannot be greater than maximum salary' },
        { status: 400 }
      );
    }

    // Create position
    const newPosition = await prisma.position.create({
      data: {
        title: data.title.trim(),
        departmentId: departmentId,
        minSalary: minSalary,
        maxSalary: maxSalary,
        description: data.description?.trim() || null,
      },
      include: {
        department: {
          select: {
            name: true
          }
        }
      }
    });

    return NextResponse.json(newPosition);
  } catch (error) {
    console.error('Error creating position:', error);
    if (error instanceof Error) {
      return NextResponse.json(
        { error: `Failed to create position: ${error.message}` },
        { status: 500 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create position' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}







