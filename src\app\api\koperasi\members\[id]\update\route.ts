/**
 * API Route: /api/koperasi/members/[id]/update
 *
 * Endpoint untuk memperbarui data anggota koperasi
 *
 * Method: PUT
 *
 * Request body:
 * - monthlyContribution: <PERSON><PERSON><PERSON> kontribusi bulanan (opsional)
 * - oneTimeContribution: <PERSON><PERSON><PERSON> kontribusi satu kali (opsional)
 * - optionalContribution: Jumlah kontribusi opsional (opsional)
 * - status: Status anggota (opsional)
 * - notes: Catatan (opsional)
 *
 * Response:
 * - 200: Data anggota koperasi berhasil diperbarui
 * - 401: Tidak terautentikasi atau tidak memiliki akses
 * - 404: Anggota koperasi tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

export async function PUT(request: NextRequest) {
  try {
    // Verify admin or operator_kop access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = JSON.parse(userCookie.value);

    if (!user.roles.includes('admin') && !user.roles.includes('operator_kop')) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Extract ID from URL path
    const urlParts = request.url.split('/');
    const idIndex = urlParts.findIndex(part => part === 'members') + 1;
    const id = parseInt(urlParts[idIndex] || '0');

    const data = await request.json();
    const {
      monthlyContribution,
      oneTimeContribution,
      optionalContribution,
      status,
      notes
    } = data;

    // Check if member exists
    const member = await prisma.koperasiMember.findUnique({
      where: { id }
    });

    if (!member) {
      return NextResponse.json({ error: 'Koperasi member not found' }, { status: 404 });
    }

    // Update member
    const updateData: Record<string, any> = {};

    if (monthlyContribution !== undefined) {
      updateData.monthlyContribution = parseFloat(monthlyContribution);
    }

    if (oneTimeContribution !== undefined) {
      updateData.oneTimeContribution = parseFloat(oneTimeContribution);
    }

    if (optionalContribution !== undefined) {
      updateData.optionalContribution = parseFloat(optionalContribution);
    }

    if (status !== undefined) {
      updateData.status = status;
    }

    if (notes !== undefined) {
      updateData.notes = notes;
    }

    updateData.updatedAt = new Date();

    const updatedMember = await prisma.koperasiMember.update({
      where: { id },
      data: updateData,
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            department: {
              select: {
                name: true
              }
            },
            position: {
              select: {
                title: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json(updatedMember);
  } catch (error) {
    console.error('Error updating koperasi member:', error);
    return NextResponse.json(
      { error: 'Failed to update koperasi member' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
