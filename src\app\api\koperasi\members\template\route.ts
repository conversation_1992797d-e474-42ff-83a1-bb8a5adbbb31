/**
 * API Route: GET /api/koperasi/members/template
 *
 * Deskripsi: Generate template Excel untuk import anggota koperasi
 * Penggunaan: Download template import anggota koperasi
 *
 * Response:
 * - 200: File Excel template
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export async function GET() {
  try {
    // Create workbook
    const wb = XLSX.utils.book_new();

    // Create worksheet with headers and sample data
    const wsData = [
      ['employeeId', 'oneTimeContribution', 'monthlyContribution', 'optionalContribution', 'notes'],
      ['001', 500000, 100000, 0, 'Contoh data 1'],
      ['002', 750000, 150000, 50000, 'Contoh data 2']
    ];

    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Template');

    // Create instruction sheet
    const instructionData = [
      ['INSTRUKSI PENGGUNAAN TEMPLATE'],
      [''],
      ['1. <PERSON><PERSON> mengu<PERSON> nama kolom pada baris pertama.'],
      ['2. Pastikan ID Karyawan (employeeId) sudah terdaftar dalam sistem.'],
      ['3. Iuran Bulanan (monthlyContribution) harus berupa angka.'],
      ['4. Iuran Satu Kali (oneTimeContribution) bersifat opsional.'],
      ['5. Iuran Opsional (optionalContribution) bersifat opsional.'],
      ['6. Kolom Catatan (notes) bersifat opsional.'],
      ['7. Jika ada karyawan yang sudah menjadi anggota koperasi, data tersebut akan diperbarui.'],
      [''],
      ['Format Data:'],
      ['- employeeId: ID Karyawan (contoh: 001)'],
      ['- oneTimeContribution: Iuran Satu Kali (contoh: 500000, opsional)'],
      ['- monthlyContribution: Iuran Bulanan (contoh: 100000)'],
      ['- optionalContribution: Iuran Opsional (contoh: 50000, opsional)'],
      ['- notes: Catatan (opsional)']
    ];

    // Create instruction worksheet
    const instructionWs = XLSX.utils.aoa_to_sheet(instructionData);

    // Add instruction worksheet to workbook
    XLSX.utils.book_append_sheet(wb, instructionWs, 'Instruksi');

    // Generate Excel file
    const excelBuffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    // Return Excel file
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="koperasi_members_import_template.xlsx"'
      }
    });
  } catch (error) {
    console.error('Failed to generate template:', error);
    return NextResponse.json(
      { error: 'Failed to generate template' },
      { status: 500 }
    );
  }
}
