import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';

// Helper function to parse period from string like "1/2023" (month/year)
function parsePeriod(periodStr: string): Date {
  if (!periodStr) {
    return new Date();
  }

  try {
    // Log the period string for debugging
    console.log('Parsing period string:', periodStr);

    // Handle different formats
    if (periodStr.includes('/')) {
      const parts = periodStr.split('/');
      if (parts.length === 2) {
        const month = parseInt(parts[0]) - 1; // 0-based month
        const year = parseInt(parts[1]);
        console.log(`Parsed month: ${month}, year: ${year}`);
        return new Date(year, month, 1);
      }
    } else if (periodStr.includes('-')) {
      const parts = periodStr.split('-');
      if (parts.length === 2) {
        const month = parseInt(parts[0]) - 1; // 0-based month
        const year = parseInt(parts[1]);
        console.log(`Parsed month: ${month}, year: ${year}`);
        return new Date(year, month, 1);
      }
    }

    // If we can't parse the string, log it and return current date
    console.error('Could not parse period string:', periodStr);
  } catch (error) {
    console.error('Error parsing period:', error);
  }

  return new Date();
}



export async function POST(request: Request) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Read file
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: 'array' });

    // Get first sheet
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];

    // Convert to JSON
    const data = XLSX.utils.sheet_to_json(sheet);

    if (!data || data.length === 0) {
      return NextResponse.json({ error: 'No data found in file' }, { status: 400 });
    }

    // Process data
    let imported = 0;
    let errors = 0;

    // Debug: Log the first row to see the structure
    if (data.length > 0) {
      console.log('First row of data:', JSON.stringify(data[0], null, 2));
    }

    for (const row of data as Record<string, any>[]) {
      try {
        // Map Excel columns to database fields
        const employeeIdValue = String(row['EmployeeId'] || '');

        // Check if employeeId exists in the database
        if (employeeIdValue) {
          // Try to find by employee_id (string format)
          const employeeByCode = await prisma.employee.findFirst({
            where: {
              employeeId: employeeIdValue,
              isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
            }
          });

          if (employeeByCode) {
            // Found by employee_id
            row._employeeId = employeeByCode.id; // Store the actual ID for later use
          } else {
            // Try to find by numeric ID
            const employeeId = parseInt(employeeIdValue);
            if (!isNaN(employeeId) && employeeId > 0) {
              const employeeById = await prisma.employee.findFirst({
                where: {
                  id: employeeId,
                  isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
                }
              });

              if (employeeById) {
                // Found by numeric ID
                row._employeeId = employeeById.id;
              } else {
                console.error(`Employee with ID ${employeeIdValue} not found or has been soft-deleted`);
                // Tetap lanjutkan, tapi dengan employeeId null
                row._employeeId = null;
                // Tambahkan warning ke log
                console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
              }
            } else {
              console.error(`Invalid employee ID format: ${employeeIdValue}`);
              // Tetap lanjutkan, tapi dengan employeeId null
              row._employeeId = null;
              // Tambahkan warning ke log
              console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
            }
          }
        } else {
          console.error('No employee ID provided');
          // Tetap lanjutkan, tapi dengan employeeId null
          row._employeeId = null;
          // Tambahkan warning ke log
          console.warn(`Continuing import for ${row['Nama']} without employee ID reference`);
        }

        // Helper function to safely parse numeric values
        const parseNumber = (value: any): number => {
          if (value === undefined || value === null || value === '') return 0;
          if (typeof value === 'number') return value;
          if (typeof value === 'string') {
            // Remove any non-numeric characters except decimal point
            const cleanedValue = value.replace(/[^0-9.]/g, '');
            return parseFloat(cleanedValue) || 0;
          }
          return 0;
        };

        // Helper function to safely parse boolean or numeric values for absensi fields
        const parseBoolean = (value: any): number => {
          // If it's already a boolean, convert to number (1 for true, 0 for false)
          if (typeof value === 'boolean') return value ? 1 : 0;

          // If it's a number, return the number
          if (typeof value === 'number') return value;

          // If it's a string that can be parsed as a number
          if (typeof value === 'string') {
            // Check if it's a string representation of a number
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
              return numValue; // Return the numeric value
            }

            // Otherwise check for boolean string values and convert to number
            return (value.toLowerCase() === 'ya' || value.toLowerCase() === 'yes' || value.toLowerCase() === 'true') ? 1 : 0;
          }

          // Default case
          return 0;
        };

        const salaryData = {
          employeeId: row._employeeId || null,
          nama: String(row['Nama'] || ''),
          gp: parseNumber(row['GP']),
          load: parseInt(String(row['Load'] || '0')),
          hm: parseNumber(row['HM']),
          nominal: parseNumber(row['Nominal']),
          jlh_xc: parseNumber(row['Jumlah XC']),
          jab: parseNumber(row['Jabatan']),
          walas: parseNumber(row['Wali Kelas']),
          pemb_osis: parseNumber(row['Pembina OSIS']),
          xc: parseNumber(row['XC']),
          menggantikan: parseNumber(row['Menggantikan']),
          tot: parseNumber(row['Total']),
          sl: parseBoolean(row['SL']),
          vl: parseBoolean(row['VL']),
          awol: parseBoolean(row['AWOL']),
          jlh_sl: parseInt(String(row['Jumlah SL'] || '0')),
          jlh_vl: parseInt(String(row['Jumlah VL'] || '0')),
          jlh_awol: parseInt(String(row['Jumlah AWOL'] || '0')),
          freq: parseBoolean(row['Freq']),
          minute: parseBoolean(row['Minute']),
          jlh_freq: parseInt(String(row['Jumlah Freq'] || '0')),
          jlh_minute: parseInt(String(row['Jumlah Minute'] || '0')),
          pot_abs: parseNumber(row['Potongan Absensi']),
          pot_ngajar: parseNumber(row['Potongan Ngajar']),
          bruto: parseNumber(row['Bruto']),
          pot_bpjstku: parseNumber(row['Potongan BPJS TK']),
          pot_bpjskes: parseNumber(row['Potongan BPJS Kesehatan']),
          netto_stlh_bpjs: parseNumber(row['Netto Setelah BPJS']),
          pph21: parseNumber(row['PPh21']),
          pinj_lainnya: parseNumber(row['Pinjaman Lainnya']),
          iuran_wajib: parseNumber(row['Iuran Wajib']),
          pinj_kop: parseNumber(row['Pinjaman Koperasi']),
          piutang: parseNumber(row['Piutang']),
          pot_bank: parseNumber(row['Potongan Bank']),
          tot_pot: parseNumber(row['Total Potongan']),
          gaji_netto: parseNumber(row['Gaji Netto']),
          period: parsePeriod(String(row['Periode'] || '')),
        };

        // Debug: Log the processed data
        console.log('Processed salary data:', JSON.stringify(salaryData, null, 2));

        // Create record
        await prisma.salaryGuru.create({
          data: salaryData,
        });

        imported++;
      } catch (error) {
        console.error('Error importing row:', error);
        errors++;
      }
    }

    return NextResponse.json({ imported, errors });
  } catch (error) {
    console.error('Failed to import guru salary data:', error);
    return NextResponse.json(
      {
        error: 'Failed to import guru salary data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
