/**
 * API Route: GET /api/leave-management/get
 *
 * Deskripsi: Mengambil daftar permintaan cuti
 * Penggunaan: Halaman manajemen cuti
 *
 * Query Parameters:
 * - employeeId: <PERSON> karyawan (opsional)
 * - status: Status permintaan cuti (opsional)
 *
 * Response:
 * - 200: Daftar permintaan cuti
 * - 401: Tidak terautentikasi
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { logger } from '@/lib/logger';



export async function GET(request: Request) {
  logger.debug('GET /api/leave-management/get - Start fetching leave requests');
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      logger.debug('GET /api/leave-management/get - Authentication failed: No user cookie found');
      return NextResponse.json([], { status: 401 });
    }

    logger.debug('GET /api/leave-management/get - User authenticated');

    const { searchParams } = new URL(request.url);
    const employeeId = searchParams.get('employeeId');
    const status = searchParams.get('status');

    // Build where clause
    const whereClause: any = {};

    if (employeeId) {
      whereClause.employeeId = parseInt(employeeId);
    }

    if (status) {
      whereClause.status = status;
    }

    logger.debug('GET /api/leave-management/get - Executing database query');

    const leaveRequests = await prisma.leaveRequest.findMany({
      where: whereClause,
      include: {
        employee: true,
        leaveType: true,
        approvedBy: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Log the first leave request to check if attachmentUrl is included
    if (leaveRequests.length > 0) {
      console.log('First leave request from database:', JSON.stringify({
        id: leaveRequests[0].id,
        attachmentUrl: leaveRequests[0].attachmentUrl,
        // Include other fields as needed
      }, null, 2));
    }

    logger.debug(`GET /api/leave-management/get - Found ${leaveRequests.length} leave requests`);
    return NextResponse.json(leaveRequests);
  } catch (error) {
    logger.error('GET /api/leave-management/get - Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch leave requests' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
