"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/lib/auth";
import LoginForm from "@/components/auth/LoginForm";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2 } from "lucide-react";
import { logger } from "@/lib/logger";

// Komponen untuk menangani parameter URL
function LoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, login, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Tambahkan state untuk menampilkan pesan sukses registrasi
  const [showRegistrationSuccess, setShowRegistrationSuccess] = useState(false);

  // Cek query parameter untuk menampilkan pesan sukses
  useEffect(() => {
    const registered = searchParams.get('registered');
    if (registered === 'true') {
      setShowRegistrationSuccess(true);
      // Hilangkan pesan setelah 5 detik
      const timer = setTimeout(() => {
        setShowRegistrationSuccess(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [searchParams]);

  // Redirect if already logged in
  useEffect(() => {
    if (user && !authLoading) {
      let redirectPath;

      switch (user.role) {
        case 'ADMIN':
          redirectPath = '/dashboard';
          break;
        case 'SUPERVISOR':
          redirectPath = '/employees';
          break;
        case 'HEAD':
          redirectPath = '/employees';
          break;
        case 'OPERATOR_KOP':
          redirectPath = '/koperasi';
          break;
        case 'EMPLOYEE':
        default:
          redirectPath = '/leave-management';
          break;
      }

      logger.debug('Login page: redirecting authenticated user');
      window.location.href = redirectPath;
    }
  }, [user, authLoading]);

  const handleLogin = async (data: {
    username: string;
    password: string;
    rememberMe: boolean;
  }) => {
    if (isLoading) return;

    setIsLoading(true);
    setError("");
    logger.debug('Login page: processing login request');

    try {
      const result = await login(data.username, data.password);
      logger.debug('Login page: received login result');

      // Jika login berhasil, biarkan loading state tetap aktif
      // karena akan ada redirect yang ditangani oleh useEffect
      if (!result.success) {
        setError("Invalid username or password");
        setIsLoading(false);
        logger.debug('Login page: authentication failed');
      } else {
        logger.debug('Login page: authentication successful');
      }
    } catch (err) {
      logger.error('Login page: authentication error occurred');
      setError("An error occurred during login");
      setIsLoading(false);
    }
  };

  // Loading skeleton
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8">
        {showRegistrationSuccess && (
          <Alert className="bg-green-50 border-green-200 mb-6">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-700">
              Registration successful! Please log in with your credentials.
            </AlertDescription>
          </Alert>
        )}

        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold">Welcome Back</h1>
          <p className="text-gray-600 mt-2">
            Log in to access the Employee Management System
          </p>
        </div>

        <LoginForm onSubmit={handleLogin} isLoading={isLoading} error={error} />
      </div>
    </div>
  );
}

// Komponen utama yang menggunakan Suspense
export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    }>
      <LoginContent />
    </Suspense>
  );
}





