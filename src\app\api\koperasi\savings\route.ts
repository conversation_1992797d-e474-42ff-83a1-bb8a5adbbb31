/**
 * API Route: /api/koperasi/savings
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';

// Use the shared Prisma client instance
const prisma = db;

export async function GET() {
  logger.debug('GET /api/koperasi/savings - Start fetching savings');
  try {
    // Fetch all savings transactions with member data
    const savingsTransactions = await prisma.koperasiSaving.findMany({
      select: {
        id: true,
        memberId: true,
        amount: true,
        type: true,
        contributionType: true,
        date: true,
        notes: true,
        createdAt: true,
        updatedAt: true,
        member: {
          select: {
            employee: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Log only the count of transactions, not the sensitive data
    logger.debug(`Retrieved ${savingsTransactions.length} savings transactions`);

    // Transform the data for the frontend
    const savings = Array.isArray(savingsTransactions) ? savingsTransactions.map((transaction: any) => {
      return {
        id: transaction.id,
        member_id: transaction.memberId,
        member_name: `${transaction.member.employee.firstName} ${transaction.member.employee.lastName || ''}`,
        email: transaction.member.employee.email,
        amount: Number(transaction.amount) || 0,
        type: transaction.type,
        contributionType: transaction.contributionType,
        date: transaction.date,
        notes: transaction.notes,
        created_at: transaction.createdAt,
        updated_at: transaction.updatedAt
      };
    }) : [];

    logger.debug(`Transformed ${savings.length} savings transactions for frontend`);
    return NextResponse.json(savings);
  } catch (error) {
    logger.error('GET /api/koperasi/savings - Failed to fetch savings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch savings' },
      { status: 500 }
    );
  }
}





