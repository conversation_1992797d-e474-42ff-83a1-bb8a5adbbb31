import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { DialogFooter } from "@/components/ui/dialog";
import { LeaveType, LeaveTypeFormData } from "@/types/leave";

interface LeaveTypeFormProps {
  onSubmit: (data: LeaveTypeFormData) => void;
  onCancel: () => void;
  initialData?: LeaveType | null;
}

export function LeaveTypeForm({ onSubmit, onCancel, initialData }: LeaveTypeFormProps) {
  const [formData, setFormData] = useState<LeaveTypeFormData>({
    name: '',
    description: '',
    daysAllowed: 0,
    requiresApproval: true,
  });

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        description: initialData.description || '',
        daysAllowed: initialData.daysAllowed,
        requiresApproval: initialData.requiresApproval,
      });
    }
  }, [initialData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'daysAllowed' ? parseInt(value) : value,
    }));
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
          />
        </div>
        <div>
          <Label htmlFor="daysAllowed">Days Allowed</Label>
          <Input
            id="daysAllowed"
            name="daysAllowed"
            type="number"
            min="1"
            step="1"
            value={formData.daysAllowed}
            onChange={handleChange}
            required
            className="w-full"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Enter the number of days allowed per year (no maximum limit)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="requiresApproval"
            checked={formData.requiresApproval}
            onCheckedChange={(checked) => 
              setFormData(prev => ({ ...prev, requiresApproval: !!checked }))
            }
          />
          <Label htmlFor="requiresApproval">Requires Approval</Label>
        </div>
      </div>
      <DialogFooter className="mt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {initialData ? 'Save Changes' : 'Create'}
        </Button>
      </DialogFooter>
    </form>
  );
}

