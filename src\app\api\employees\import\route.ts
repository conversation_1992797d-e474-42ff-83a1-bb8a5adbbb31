/**
 * API Route: POST /api/employees/import
 *
 * Deskripsi: Mengimpor data karyawan dari file Excel
 * Penggunaan: Fitur import karyawan
 *
 * Body:
 * - employees: Array data karyawan dari Excel
 *
 * Response:
 * - 200: Import berhasil dengan detail hasil
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    const data = await request.json();
    const employees = data.employees;
    console.log('Received data:', employees[0]); // Log first row for debugging
    console.log('Total employees to import:', employees.length);
    console.log('Employee ID from first row:', employees[0]['Employee ID *']);
    console.log('All columns in first row:', Object.keys(employees[0]));

    const results = {
      total: employees.length,
      processed: 0,
      created: 0,
      updated: 0,
      failed: 0,
      duplicates: 0,
      errors: [] as string[],
      details: {
        created: [] as string[],
        updated: [] as string[]
      }
    };

    await prisma.$transaction(async (tx) => {
      for (const emp of employees) {
        try {
          // Log data untuk debugging
          console.log('Processing employee:', emp['Employee ID *'] || 'unknown ID');

          // Validasi hanya field yang required
          const requiredFields = ['Employee ID *', 'Full Name *', 'Email *', 'Join Date *', 'Status *', 'Department *', 'Position *', 'Role *'];
          const missingFields = requiredFields.filter(field => !emp[field] || emp[field].toString().trim() === '' || emp[field] === '-');

          if (missingFields.length > 0) {
            console.log('Missing fields:', missingFields);
            throw new Error(`Missing required fields for employee ${emp['Employee ID *'] || 'unknown'}: ${missingFields.join(', ')}`);
          }

          // Increment processed counter
          results.processed++;

          // Cek apakah employee dengan ID tersebut sudah ada
          const existingEmployee = await tx.employee.findFirst({
            where: { employeeId: emp['Employee ID *'] },
            include: { user: true }
          });

          if (existingEmployee) {
            results.duplicates++;
            console.log(`Found duplicate employee ID: ${emp['Employee ID *']}. Will update existing data.`);
          }

          const hashedPassword = emp['Password'] ?
            await bcrypt.hash(emp['Password'], 10) :
            await bcrypt.hash('defaultpass123', 10);

          // Prepare employee data with proper type handling for Prisma
          const departmentId = emp['Department *'] !== '-' ?
            await getDepartmentId(tx, emp['Department *']) : undefined;

          const positionId = emp['Position *'] !== '-' ?
            await getPositionId(tx, emp['Position *']) : undefined;

          // Create base employee data object
          const baseEmployeeData = {
            employeeId: emp['Employee ID *'],
            firstName: emp['Full Name *'].split(' ')[0],
            lastName: emp['Full Name *'].split(' ').slice(1).join(' '),
            email: emp['Email *'] === '-' ? undefined : emp['Email *'],
            phone: emp['Phone'] === '-' ? undefined : emp['Phone'],
            address: emp['Address'] === '-' ? undefined : emp['Address'],
            birthDate: emp['Birth Date'] && emp['Birth Date'] !== '-'
              ? new Date(emp['Birth Date'])
              : undefined,
            birthPlace: emp['Birth Place'] === '-' ? undefined : emp['Birth Place'],
            gender: emp['Gender'] === '-' ? undefined : emp['Gender'],
            religion: emp['Religion'] === '-' ? undefined : emp['Religion'],
            maritalStatus: emp['Marital Status'] === '-' ? undefined : emp['Marital Status'],
            educationLevel: emp['Education Level'] === '-' ? undefined : emp['Education Level'],
            hireDate: new Date(emp['Join Date *']),
            status: emp['Status *'],
            bankName: emp['Bank Name'] === '-' ? undefined : emp['Bank Name'],
            bankAccount: emp['Bank Account'] === '-' ? undefined : emp['Bank Account'],
            npwp: emp['NPWP'] === '-' ? undefined : emp['NPWP'],
            bpjsKesehatan: emp['BPJS Kesehatan'] === '-' ? undefined : emp['BPJS Kesehatan'],
            bpjsKetenagakerjaan: emp['BPJS Ketenagakerjaan'] === '-' ? undefined : emp['BPJS Ketenagakerjaan'],
          };

          // For update operations, we can include departmentId and positionId
          const employeeData = {
            ...baseEmployeeData,
            departmentId: departmentId,
            positionId: positionId,
          };

          if (existingEmployee) {
            // Update existing employee
            await tx.employee.update({
              where: { id: existingEmployee.id },
              data: employeeData
            });

            // Update associated user if exists
            if (existingEmployee.user) {
              await tx.user.update({
                where: { id: existingEmployee.user.id },
                data: {
                  username: emp['Employee ID *'],
                  password: hashedPassword,
                  role: emp['Role *'] || 'EMPLOYEE',
                  email: emp['Email *']
                }
              });
            } else {
              // Create new user if not exists
              await tx.user.create({
                data: {
                  username: emp['Employee ID *'],
                  password: hashedPassword,
                  role: emp['Role *'] || 'EMPLOYEE',
                  email: emp['Email *'],
                  employeeId: existingEmployee.id
                }
              });
            }

            results.updated++;
            results.details.updated.push(`${emp['Employee ID *']} - ${emp['Full Name *']}`);
            console.log(`Updated employee: ${emp['Employee ID *']} - ${emp['Full Name *']}`);
          } else {
            // Create new employee with direct field assignments
            // We need to handle departmentId and positionId differently for create vs update
            let createData: any = { ...baseEmployeeData };

            // Only add departmentId and positionId if they are defined
            if (departmentId !== undefined) {
              createData.department = { connect: { id: departmentId } };
            }

            if (positionId !== undefined) {
              createData.position = { connect: { id: positionId } };
            }

            const newEmployee = await tx.employee.create({
              data: createData
            });

            // Create new user
            await tx.user.create({
              data: {
                username: emp['Employee ID *'],
                password: hashedPassword,
                role: emp['Role *'] || 'EMPLOYEE',
                email: emp['Email *'],
                employeeId: newEmployee.id
              }
            });

            results.created++;
            results.details.created.push(`${emp['Employee ID *']} - ${emp['Full Name *']}`);
            console.log(`Created employee: ${emp['Employee ID *']} - ${emp['Full Name *']}`);
          }
        } catch (error: any) {
          results.failed++;
          results.errors.push(
            `Failed to process employee ${emp['Employee ID *']}: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }
    });

    return NextResponse.json({
      message: 'Import process completed',
      results
    });
  } catch (error: any) {
    console.error('Import error:', error);
    return NextResponse.json(
      { error: 'Failed to import employees' },
      { status: 500 }
    );
  }
}

// Helper function to get department ID
async function getDepartmentId(tx: any, departmentName: string): Promise<number> {
  const department = await tx.department.findFirst({
    where: { name: departmentName }
  });
  if (!department) {
    throw new Error(`Department "${departmentName}" not found`);
  }
  return department.id;
}

// Helper function to get position ID
async function getPositionId(tx: any, positionTitle: string): Promise<number> {
  const position = await tx.position.findFirst({
    where: { title: positionTitle }
  });
  if (!position) {
    throw new Error(`Position "${positionTitle}" not found`);
  }
  return position.id;
}










