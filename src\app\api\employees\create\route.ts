/**
 * API Route: POST /api/employees/create
 *
 * Deskripsi: Membuat data karyawan baru
 * Penggunaan: Form tambah karyawan
 *
 * Body:
 * - employeeId: <PERSON> karyawan (string)
 * - firstName: <PERSON><PERSON> de<PERSON> (string)
 * - lastName: <PERSON><PERSON> (string, opsional)
 * - email: <PERSON><PERSON> (string, opsional)
 * - phone: Nomor telepon (string, opsional)
 * - departmentId: ID departemen (number)
 * - positionId: ID posisi (number)
 * - dll.
 *
 * Response:
 * - 201: Karyawan berhasil dibuat
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import bcrypt from 'bcryptjs';

// Test database connection
async function testDatabaseConnection() {
  try {
    console.log('Testing database connection...');
    const result = await prisma.$queryRaw`SELECT 1+1 as result`;
    console.log('Database connection successful:', result);
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

export async function POST(request: Request) {
  console.log('POST /api/employees/create - Start');

  // Test database connection
  const isConnected = await testDatabaseConnection();
  if (!isConnected) {
    return NextResponse.json(
      { error: 'Database connection failed' },
      { status: 500 }
    );
  }
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    console.log('User cookie:', userCookie ? 'Found' : 'Not found');

    // TEMPORARY: Skip authentication check for debugging
    const skipAuthCheck = true; // Set to false in production

    if (!skipAuthCheck) {
      if (!userCookie) {
        console.log('No user cookie found');
        return NextResponse.json(
          { error: 'Unauthorized - No user cookie' },
          { status: 401 }
        );
      }

      const userData = JSON.parse(userCookie.value);
      console.log('User role:', userData.role);

      if (userData.role !== 'ADMIN') {
        console.log('User is not an admin');
        return NextResponse.json(
          { error: 'Unauthorized - Not an admin' },
          { status: 401 }
        );
      }
    } else {
      console.log('WARNING: Authentication check skipped for debugging');
    }

    const data = await request.json();
    console.log('Received data:', data);

    // Validasi data yang diperlukan
    const requiredFields = ['employeeId', 'firstName', 'departmentId', 'positionId', 'status', 'role'];
    const missingFields = requiredFields.filter(field => !data[field]);

    if (missingFields.length > 0) {
      console.error('Missing required fields:', missingFields);
      return NextResponse.json(
        {
          error: 'Missing required fields',
          details: `The following fields are required: ${missingFields.join(', ')}`
        },
        { status: 400 }
      );
    }

    // Validasi format data
    try {
      // Pastikan departmentId dan positionId adalah angka
      const departmentId = parseInt(data.departmentId);
      const positionId = parseInt(data.positionId);

      // Validasi academicYearId jika ada
      if (data.academicYearId && data.academicYearId !== "none") {
        const academicYearId = parseInt(data.academicYearId);
        if (isNaN(academicYearId)) {
          throw new Error('Academic Year ID must be a number');
        }
      }

      if (isNaN(departmentId)) {
        throw new Error('Department ID must be a number');
      }

      if (isNaN(positionId)) {
        throw new Error('Position ID must be a number');
      }

      // Pastikan status adalah salah satu dari nilai yang valid
      const validStatuses = ['Tetap', 'Kontrak', 'Honor'];
      if (!validStatuses.includes(data.status)) {
        throw new Error(`Status must be one of: ${validStatuses.join(', ')}`);
      }

      // Pastikan role adalah salah satu dari nilai yang valid
      const validRoles = ['ADMIN', 'SUPERVISOR', 'EMPLOYEE'];
      if (!validRoles.includes(data.role)) {
        throw new Error(`Role must be one of: ${validRoles.join(', ')}`);
      }
    } catch (validationError) {
      console.error('Validation error:', validationError);
      return NextResponse.json(
        {
          error: 'Invalid data format',
          details: validationError instanceof Error ? validationError.message : 'Unknown validation error'
        },
        { status: 400 }
      );
    }

    // Cek apakah employeeId sudah digunakan
    const existingEmployee = await prisma.employee.findUnique({
      where: { employeeId: data.employeeId }
    });

    if (existingEmployee) {
      return NextResponse.json(
        { error: 'Employee ID already exists' },
        { status: 400 }
      );
    }

    console.log('Creating employee with data:', {
      employeeId: data.employeeId,
      firstName: data.firstName,
      departmentId: data.departmentId,
      positionId: data.positionId,
      status: data.status,
      role: data.role,
      hireDate: data.hireDate
    });

    // Buat karyawan baru dalam transaksi
    const result = await prisma.$transaction(async (tx) => {
      // Buat data karyawan
      console.log('Creating employee record...');
      // Buat email default jika tidak ada
      const defaultEmail = `${data.employeeId}@example.com`;
      console.log('Using email:', data.email || defaultEmail);

      // Handle academicYearId - set to null if "none" or empty, otherwise convert to integer
      let academicYearId = null;
      if (data.academicYearId && data.academicYearId !== "none") {
        academicYearId = parseInt(data.academicYearId);
      }

      const employee = await tx.employee.create({
        data: {
          employeeId: data.employeeId,
          firstName: data.firstName,
          lastName: data.lastName || null,
          email: data.email || defaultEmail, // Gunakan email default jika tidak ada
          phone: data.phone || null,
          hireDate: data.hireDate ? new Date(data.hireDate) : new Date(),
          departmentId: parseInt(data.departmentId),
          positionId: parseInt(data.positionId),
          academicYearId: academicYearId, // Tambahkan academicYearId
          address: data.address || null,
          city: data.city || null,
          state: data.state || null,
          postalCode: data.postalCode || null,
          country: data.country || null,
          birthDate: data.birthDate ? new Date(data.birthDate) : null,
          gender: data.gender || null,
          maritalStatus: data.maritalStatus || null,
          emergencyContactName: data.emergencyContactName || null,
          emergencyContactPhone: data.emergencyContactPhone || null,
          status: data.status, // Status wajib diisi
        }
      });
      console.log('Employee created successfully:', employee);

      // Buat user untuk karyawan
      console.log('Creating user record...');
      const defaultPassword = await bcrypt.hash('password123', 10);
      const user = await tx.user.create({
        data: {
          username: data.employeeId, // employeeId menjadi username
          password: defaultPassword,
          role: data.role, // Role wajib diisi
          email: data.email || defaultEmail, // Gunakan email yang sama dengan karyawan
          employeeId: employee.id
        }
      });
      console.log('User created successfully:', { id: user.id, username: user.username, role: user.role });

      return employee;
    });
    console.log('Transaction completed successfully');

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating employee:', error);
    // Tampilkan detail error yang lebih lengkap
    const errorMessage = error instanceof Error ?
      `${error.name}: ${error.message}\n${error.stack}` :
      'Unknown error';
    console.error('Detailed error:', errorMessage);

    return NextResponse.json(
      {
        error: 'Failed to create employee',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  } finally {
    try {
      await prisma.$disconnect();
      console.log('Prisma disconnected successfully');
    } catch (disconnectError) {
      console.error('Error disconnecting from Prisma:', disconnectError);
    }
  }
}
