"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Define a schema for form validation
const membershipFormSchema = z.object({
  memberType: z.enum(['employee', 'external']),
  employeeId: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email().optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional(),
  monthlyContribution: z.string().min(1, "Monthly contribution is required"),
  oneTimeContribution: z.string().optional(),
  optionalContribution: z.string().optional(),
  joinDate: z.string(),
  notes: z.string().optional()
}).refine(data => {
  // If member type is employee, employeeId is required
  if (data.memberType === 'employee') {
    return !!data.employeeId;
  }
  // If member type is external, firstName and lastName are required
  if (data.memberType === 'external') {
    return !!data.firstName && !!data.lastName;
  }
  return true;
}, {
  message: "Required fields missing for the selected member type",
  path: ["memberType"]
});

type MembershipFormData = z.infer<typeof membershipFormSchema>;

interface MembershipFormProps {
  initialData?: Partial<MembershipFormData>;
  onSubmit: (data: MembershipFormData) => void;
  onCancel: () => void;
}

export function MembershipForm({ initialData, onSubmit, onCancel }: MembershipFormProps) {
  const form = useForm<MembershipFormData>({
    resolver: zodResolver(membershipFormSchema),
    defaultValues: {
      memberType: initialData?.memberType || 'employee',
      employeeId: initialData?.employeeId || "",
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      address: initialData?.address || "",
      monthlyContribution: initialData?.monthlyContribution || "",
      oneTimeContribution: initialData?.oneTimeContribution || "",
      optionalContribution: initialData?.optionalContribution || "",
      joinDate: initialData?.joinDate || new Date().toISOString().split('T')[0],
      notes: initialData?.notes || ""
    }
  });

  const handleSubmit = form.handleSubmit((data) => {
    onSubmit(data);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4 max-h-[60vh] overflow-y-auto pr-2">
        <FormField
          control={form.control}
          name="memberType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Member Type</FormLabel>
              <Select onValueChange={(value) => {
                field.onChange(value);
                // Reset fields based on member type
                if (value === 'employee') {
                  form.setValue('firstName', '');
                  form.setValue('lastName', '');
                  form.setValue('email', '');
                  form.setValue('phone', '');
                  form.setValue('address', '');
                } else {
                  form.setValue('employeeId', '');
                }
              }} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select member type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="employee">Employee</SelectItem>
                  <SelectItem value="external">External</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>Select whether this member is an employee or external person</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {form.watch('memberType') === 'employee' ? (
          <FormField
            control={form.control}
            name="employeeId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Employee ID</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter employee ID" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ) : (
          <>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter first name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter last name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input {...field} type="email" placeholder="Enter email address" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter phone number" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter address" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        <FormField
          control={form.control}
          name="oneTimeContribution"
          render={({ field }) => (
            <FormItem>
              <FormLabel>One Time Contribution</FormLabel>
              <FormControl>
                <Input type="number" {...field} placeholder="Enter one time contribution amount (optional)" />
              </FormControl>
              <FormDescription>Optional one-time contribution paid when joining</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="monthlyContribution"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Monthly Contribution</FormLabel>
              <FormControl>
                <Input type="number" {...field} placeholder="Enter monthly contribution amount" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="optionalContribution"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Optional Contribution</FormLabel>
              <FormControl>
                <Input type="number" {...field} placeholder="Enter optional contribution amount (optional)" />
              </FormControl>
              <FormDescription>Additional optional contribution</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />



        <FormField
          control={form.control}
          name="joinDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Join Date</FormLabel>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter additional notes (optional)" />
              </FormControl>
              <FormDescription>Optional notes about this member</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Register Member</Button>
        </div>
      </form>
    </Form>
  );
}