"use client";

import React from "react";
import { useRouter } from "next/navigation";
// import { cn } from "@/lib/utils"; // Not used in this component
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { UserRound, Calendar, DollarSign, ArrowRight, CreditCard } from "lucide-react";
import { useAuth } from "@/lib/auth";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href?: string;
  requiresAuth?: boolean;
  allowedRoles?: string[];
}

const FeatureCard = ({
  icon = <UserRound className="h-10 w-10 text-primary" />,
  title = "Feature Title",
  description = "Feature description goes here",
  href,
  requiresAuth = false,
  allowedRoles = ["ADMIN", "SUPERVISOR", "EMPLOYEE"],
}: FeatureCardProps) => {
  const router = useRouter();
  const { user } = useAuth();

  const handleClick = () => {
    if (!href) return;

    if (requiresAuth && !user) {
      // Redirect to login if authentication is required but user is not logged in
      // Redirect to login page
      router.push('/login');
      return;
    }

    // Check if user has the required role
    if (user && allowedRoles.length > 0 && user.role && !allowedRoles.includes(user.role)) {
      // User doesn't have the required role, don't navigate
      // Access denied - user doesn't have required role
      return;
    }

    // Navigate to the specified page

    // Navigate to the specified href
    router.push(href);
  };

  // Improved canAccess logic with better handling of role checking
  const canAccess = !requiresAuth || (
    user && (
      allowedRoles.length === 0 || // No role restrictions
      (user.role && allowedRoles.includes(user.role)) // User has an allowed role
    )
  );

  // Access control logic

  return (
    <Card
      className={`h-full transition-all ${canAccess ? 'hover:shadow-lg hover:scale-105' : 'opacity-70'} bg-white border border-gray-200 flex flex-col ${canAccess ? 'cursor-pointer' : 'cursor-not-allowed'}`}
      onClick={() => canAccess && handleClick()}
    >
      <CardHeader className="flex flex-col items-center text-center">
        <div className="rounded-full p-3 mb-4" style={{ backgroundColor: title === "Employee Tracking" ? "rgba(37, 99, 235, 0.1)" :
                                                                 title === "Leave Management" ? "rgba(22, 163, 74, 0.1)" :
                                                                 title === "Koperasi Access" ? "rgba(147, 51, 234, 0.1)" :
                                                                 "rgba(217, 119, 6, 0.1)" }}>{icon}</div>
        <CardTitle className="text-xl text-gray-800">{title}</CardTitle>
      </CardHeader>
      <CardContent className="text-center flex-grow">
        <CardDescription className="text-sm text-gray-600">
          {description}
        </CardDescription>
      </CardContent>
      {href && (
        <CardFooter className="pt-0 pb-4 flex justify-center">
          <Button
            variant={canAccess ? "default" : "ghost"}
            className={canAccess ?
              "bg-primary text-primary-foreground hover:bg-primary/90 transition-all" :
              "text-muted-foreground hover:text-muted-foreground/80 transition-all"}
            onClick={handleClick}
            disabled={!canAccess}
          >
            {!user && requiresAuth ? "Login to Access" :
             !canAccess ? "Access Restricted" : "Explore"}
            {canAccess && <ArrowRight className="ml-2 h-4 w-4" />}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

interface FeaturesSectionProps {
  title?: string;
  description?: string;
  features?: FeatureCardProps[];
}

const FeaturesSection = ({
  title = "Key Features",
  description = "Our employee management system offers powerful tools to streamline your HR operations",
  features = [
    {
      icon: <UserRound className="h-10 w-10 text-blue-600" />,
      title: "Employee Tracking",
      description:
        "Easily track employee information, documents, and history in one centralized location.",
      href: "/employees",
      requiresAuth: true,
      allowedRoles: ["ADMIN", "SUPERVISOR"], // Only ADMIN and SUPERVISOR can access Employee Tracking
    },
    {
      icon: <Calendar className="h-10 w-10 text-green-600" />,
      title: "Leave Management",
      description:
        "Monitor leave requests, late arrivals, and early exits with our intuitive and user-friendly interface.",
      href: "/leave-management",
      requiresAuth: true,
      allowedRoles: ["ADMIN", "SUPERVISOR", "EMPLOYEE"], // All roles can access Leave Management
    },
    {
      icon: <CreditCard className="h-10 w-10 text-purple-600" />,
      title: "Koperasi Access",
      description:
        "Manage koperasi memberships, savings, loans, and transactions in a simple interface.",
      href: "/koperasi",
      requiresAuth: true,
      allowedRoles: ["ADMIN", "SUPERVISOR", "EMPLOYEE"], // All roles can access Koperasi
    },
    {
      icon: <DollarSign className="h-10 w-10 text-amber-600" />,
      title: "Salary Sheet",
      description:
        "Access and download your salary slips easily from any device with a secure and user-friendly interface.",
      href: "/salary",
      requiresAuth: true,
      allowedRoles: ["ADMIN", "SUPERVISOR", "EMPLOYEE"], // All roles can access Salary
    },
  ],
}: FeaturesSectionProps) => {
  return (
    <section className="w-full py-16 bg-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tighter text-gray-800 sm:text-4xl md:text-5xl">
            {title}
          </h2>
          <p className="max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
            {description}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              href={feature.href}
              requiresAuth={feature.requiresAuth}
              allowedRoles={feature.allowedRoles}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
