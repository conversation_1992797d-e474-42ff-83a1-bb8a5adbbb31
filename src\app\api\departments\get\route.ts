/**
 * API Route: GET /api/departments/get
 *
 * Deskripsi: Mengambil daftar departemen
 * Penggunaan: Halaman daftar departemen, dropdown pilihan departemen
 *
 * Response:
 * - 200: Daftar departemen
 * - 500: Error server
 */

import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Dapatkan semua departemen dengan jumlah karyawan (tidak termasuk yang diawali EMP dan EXT)
    const departments = await prisma.department.findMany({
      include: {
        head: {
          select: {
            firstName: true,
            lastName: true,
          }
        },
        _count: {
          select: {
            employees: {
              where: {
                NOT: {
                  OR: [
                    { employeeId: { startsWith: 'EMP' } },
                    { employeeId: { startsWith: 'EXT' } }
                  ]
                },
                isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
              }
            }
          }
        },
        employees: {
          where: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ],
            isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
          },
          select: {
            id: true
          }
        }
      },
      orderBy: {
        name: 'asc',
      },
    });

    const transformedDepartments = departments.map(dept => {
      // Jumlah karyawan sudah dikurangi yang diawali EMP dan EXT dalam query
      return {
        id: dept.id,
        name: dept.name,
        description: dept.description,
        headId: dept.headId,
        headName: dept.head ? `${dept.head.firstName} ${dept.head.lastName}`.trim() : null,
        employeeCount: dept._count.employees,
        createdAt: dept.createdAt,
        updatedAt: dept.updatedAt,
      };
    });

    return NextResponse.json(transformedDepartments);
  } catch (error) {
    console.error('Error fetching departments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch departments' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
