import { SalaryHonor } from "@/lib/types";
import { useState } from "react";

interface HonorSalarySlipProps {
  data: SalaryHonor;
}

export function HonorSalarySlip({ data }: HonorSalarySlipProps) {
  // Format currency
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return "-";

    // Handle NaN values
    if (isNaN(Number(amount))) return "-";

    // Convert to number to ensure proper formatting
    const numAmount = Number(amount);

    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numAmount);
  };

  // Format date
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString('id-ID', { month: 'long', year: 'numeric' });
  };

  // Format absensi value
  const formatAbsensiValue = (value: any) => {
    if (value === null || value === undefined || value === 0 || value === false) {
      return "Tidak Ada";
    }
    return value;
  };

  return (
    <div className="p-1 px-8 bg-white text-xs">
      {/* Top separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5"></div>

      {/* Header */}
      <div className="mb-2">
        <div className="flex items-center w-full mb-2">
          <div className="mr-4">
            <img
              src="/images/LOGO%20Medium.jpg"
              alt="Sekolah Darma Bangsa Logo"
              className="h-8 w-auto object-contain"
            />
          </div>
          <div className="text-center flex-1">
            <h1 className="text-base font-bold">SEKOLAH DARMA BANGSA</h1>
            <h2 className="text-sm font-bold">SLIP GAJI</h2>
          </div>
        </div>
        <div className="border-t border-b border-dashed border-gray-400 py-0.5 w-full"></div>
      </div>

      {/* Employee Info */}
      <div className="mb-1">
        <div className="flex items-start period-row">
          <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Periode</div>
          <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
          <div className="flex-1 pr-2">{formatDate(data.period)}</div>
        </div>
        <div className="flex items-start">
          <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Nama Karyawan</div>
          <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
          <div className="flex-1 pr-2">{data.nama}</div>
        </div>
      </div>

      {/* Separator line */}
      <div className="border-t border-b border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Gaji Pokok */}
      <div className="flex items-start -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Gaji Pokok</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.gp1)}</div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Two column layout for Honor Mengajar and Honor Transport */}
      <div className="flex w-full mb-1">
        {/* Left Column - Honor Mengajar */}
        <div className="w-1/2 pr-4">
          <div className="flex items-start -mt-1">
            <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Honor Mengajar</div>
          </div>
          <div className="flex items-start">
            <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Per Jam</div>
            <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
            <div className="flex-1 pr-2">{formatCurrency(data.hnr)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Jumlah JP</div>
            <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
            <div className="flex-1 pr-2">{data.jp || "-"}</div>
          </div>
          <div className="flex items-start">
            <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Total</div>
            <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
            <div className="flex-1 pr-2">{formatCurrency(data.tot_hnr)}</div>
          </div>
        </div>

        {/* Right Column - Honor Transport */}
        <div className="w-1/2 pl-4">
          <div className="flex items-start -mt-1">
            <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Honor Transport</div>
          </div>
          <div className="flex items-start">
            <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Per Kehadiran</div>
            <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
            <div className="flex-1 pr-2">{formatCurrency(data.hnr_hdr)}</div>
          </div>
          <div className="flex items-start">
            <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Jumlah Kehadiran</div>
            <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
            <div className="flex-1 pr-2">{data.jlh_hdr || "-"}</div>
          </div>
          <div className="flex items-start">
            <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Total</div>
            <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
            <div className="flex-1 pr-2">{formatCurrency(data.tot_hnr_hdr)}</div>
          </div>
        </div>
      </div>

      {/* Total */}
      <div className="flex items-start mb-1 -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Total</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.sblm_pph)}</div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Honor XC */}
      <div className="flex items-start -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Honor XC</div>
      </div>
      <div className="flex items-start">
        <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Jumlah XC</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{data.jlhxc || "-"}</div>
      </div>
      <div className="flex items-start">
        <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Total</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.xc)}</div>
      </div>
      <div className="flex items-start mb-1">
        <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Tambahan</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.tmbhn)}</div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Total + Honor XC + Tambahan */}
      <div className="flex items-start mb-1 -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Total + Honor XC + Tambahan</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.gaji_bruto)}</div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* PPh21 dibayar Sekolah */}
      <div className="flex items-start mb-1 -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">PPh21 dibayar Sekolah</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.pph_dibayar_sklh)}</div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Potongan */}
      <div className="flex items-start -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Potongan</div>
      </div>
      <div className="flex items-start">
        <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">PPh21</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.pph21)}</div>
      </div>
      <div className="flex items-start">
        <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Pinjaman Lainnya</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.pinj_lainnya)}</div>
      </div>
      <div className="flex items-start">
        <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Potongan Bank</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.pot_bank)}</div>
      </div>
      <div className="flex items-start">
        <div className="w-[12rem] min-w-[12rem] pl-4 flex-shrink-0">Koperasi</div>
      </div>
      <div className="flex items-start">
        <div className="w-[12rem] min-w-[12rem] pl-8 flex-shrink-0">Iuran Wajib</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.iuran_wajib)}</div>
      </div>
      <div className="flex items-start mb-1">
        <div className="w-[12rem] min-w-[12rem] pl-8 flex-shrink-0">Pinjaman Koperasi</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.pinj_kop)}</div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Piutang */}
      <div className="flex items-start mb-1 -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Piutang</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.piutang)}</div>
      </div>

      {/* Separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mb-1"></div>

      {/* Gaji Nett */}
      <div className="flex items-start font-bold -mt-1">
        <div className="w-[12rem] min-w-[12rem] flex-shrink-0 pl-1">Gaji Nett</div>
        <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
        <div className="flex-1 pr-2">{formatCurrency(data.gaji_netto)}</div>
      </div>

      {/* Bottom separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mt-1"></div>

      {/* Footer */}
      <div className="text-[7px] text-right italic text-gray-500 mt-1 pr-2">
        Dicetak secara elektronik pada {new Date().getDate()}/{new Date().getMonth() + 1}/{new Date().getFullYear()} sehingga tidak membutuhkan tanda tangan. Untuk legalitas perlu ditambahkan cap basah Yayasan
      </div>
    </div>
  );
}
