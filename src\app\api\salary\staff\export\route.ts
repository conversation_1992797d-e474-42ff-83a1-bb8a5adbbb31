import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';

// POST: Export staff salary data to Excel
export async function POST(request: Request) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { data } = await request.json();

    if (!data || !Array.isArray(data) || data.length === 0) {
      return NextResponse.json({ error: 'No data to export' }, { status: 400 });
    }

    // Format currency for Excel
    const formatCurrency = (value: number | null | undefined) => {
      if (value === null || value === undefined) return 0;
      return value;
    };

    // Format date for Excel
    const formatDate = (date: Date) => {
      const d = new Date(date);
      return `${d.getMonth() + 1}/${d.getFullYear()}`;
    };

    // Prepare data for Excel
    const excelData = data.map(item => ({
      'Nama': item.nama,
      'GP': formatCurrency(item.gp),
      'Gol Kontrak': formatCurrency(item.gol_kontrak),
      'Gol Tetap': formatCurrency(item.gol_tetap),
      'Beban Kerja': formatCurrency(item.beban_kerja),
      'Insentif': formatCurrency(item.insentif),
      'Tunjangan Keahlian': formatCurrency(item.tunj_keahlian),
      'Tunjangan Jabatan': formatCurrency(item.tunj_jab),
      'Total Gross': formatCurrency(item.tot_gross),
      'BPJS TK Yayasan': formatCurrency(item.bpjstku_yay),
      'BPJS Kesehatan Yayasan': formatCurrency(item.bpjskes_yay),
      'PPh Yayasan': formatCurrency(item.pph_yay),
      'Total Gross Yayasan': formatCurrency(item.tot_gross_yay),
      'AWOL': item.awol ? 'Ya' : 'Tidak',
      'Jumlah AWOL': item.jlh_awol || 0,
      'Freq': item.freq ? 'Ya' : 'Tidak',
      'Minute': item.minute ? 'Ya' : 'Tidak',
      'Jumlah Freq': item.jlh_freq || 0,
      'Jumlah Minute': item.jlh_minute || 0,
      'Potongan Absensi': formatCurrency(item.pot_abs),
      'Gaji Setelah Potongan Absensi': formatCurrency(item.gaji_stlh_pot_abs),
      'BPJS TK': formatCurrency(item.bpjstku),
      'BPJS Kesehatan': formatCurrency(item.bpjskes),
      'Nett Setelah BPJS': formatCurrency(item.nett_stlh_bpjs),
      'PPh': formatCurrency(item.pph),
      'Pinjaman Lain': formatCurrency(item.pinj_lain),
      'Iuran Wajib': formatCurrency(item.iuran_wajib),
      'Pinjaman Koperasi': formatCurrency(item.pinj_kop),
      'Piutang': formatCurrency(item.piutang),
      'Potongan Bank': formatCurrency(item.pot_bank),
      'Total Potongan': formatCurrency(item.tot_pot),
      'Gaji Netto': formatCurrency(item.gaji_netto),
      'Periode': formatDate(item.period),
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Staff Salary');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });

    // Return Excel file as response
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="staff_salary_export.xlsx"',
      },
    });
  } catch (error) {
    console.error('Failed to export staff salary data:', error);
    return NextResponse.json(
      {
        error: 'Failed to export staff salary data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
