import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Hitung jumlah karyawan yang memiliki manager
    const employeesWithManager = await prisma.employee.count({
      where: {
        managerId: {
          not: null
        },
        isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
      }
    });

    // Ambil sample data karyawan dengan manager
    const sampleEmployees = await prisma.employee.findMany({
      where: {
        managerId: {
          not: null
        },
        isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
      },
      select: {
        id: true,
        employeeId: true,
        firstName: true,
        lastName: true,
        manager: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      take: 5
    });

    return NextResponse.json({
      totalEmployeesWithManager: employeesWithManager,
      sampleData: sampleEmployees
    });

  } catch (error) {
    console.error('Error checking managers:', error);
    return NextResponse.json(
      { error: 'Failed to check managers data' },
      { status: 500 }
    );
  }
}