/**
 * API Route: GET /api/leave-types/get
 * 
 * Deskripsi: Mengambil daftar tipe cuti
 * Penggunaan: Halaman daftar tipe cuti, dropdown pilihan tipe cuti
 * 
 * Response:
 * - 200: Daftar tipe cuti
 * - 401: Tidak terautentikasi
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function GET() {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const leaveTypes = await prisma.leaveType.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json(leaveTypes);
  } catch (error) {
    console.error('Error fetching leave types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch leave types' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
