/**
 * API Route: /api/leave-management/approve/[id]
 *
 * Deskripsi: Endpoint untuk menyetujui leave request
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function PUT(request: Request, { params }: { params: { id: string } }) {
  // Get the ID from params
  const id = params.id;

  try {

    console.log(`PUT /api/leave-management/approve/${id} - Start approving leave request`);
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      console.error('PUT /api/leave-management/approve - Unauthorized: No user cookie found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    console.log(`PUT /api/leave-management/approve/${id} - Authenticated user:`, userData.username, 'Role:', userData.role);

    // <PERSON>ya <PERSON>, SUPERVISOR, dan H<PERSON>D yang dapat menyetujui leave request
    if (userData.role !== 'ADMIN' && userData.role !== 'SUPERVISOR' && userData.role !== 'HEAD') {
      console.error(`PUT /api/leave-management/approve/${id} - Forbidden: User role ${userData.role} not allowed to approve leave requests`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Pastikan leave request ada
    const leaveRequest = await prisma.leaveRequest.findUnique({
      where: {
        id: parseInt(id)
      },
      include: {
        employee: {
          include: {
            department: {
              include: {
                head: true
              }
            }
          }
        }
      }
    });

    if (!leaveRequest) {
      console.error(`PUT /api/leave-management/approve/${id} - Not found: Leave request with ID ${id} not found`);
      return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
    }

    // Jika user adalah SUPERVISOR atau HEAD, pastikan leave request adalah milik bawahannya
    if (userData.role === 'SUPERVISOR') {
      const isSupervisorOfEmployee = leaveRequest.employee?.department?.head?.employeeId === userData.username;

      if (!isSupervisorOfEmployee) {
        console.error(`PUT /api/leave-management/approve/${id} - Forbidden: User is not supervisor of employee`);
        return NextResponse.json({ error: 'Forbidden: You can only approve leave requests from your subordinates' }, { status: 403 });
      }
    } else if (userData.role === 'HEAD') {
      // Untuk HEAD, periksa apakah karyawan memiliki posisi Chief, Kepala, atau Psikolog
      const employeePosition = await prisma.employee.findUnique({
        where: { id: leaveRequest.employeeId },
        include: { position: true }
      });

      const positionTitle = employeePosition?.position?.title || '';
      const isChiefOrKepalaOrPsikolog =
        positionTitle.toLowerCase().includes('chief') ||
        positionTitle.toLowerCase().includes('kepala') ||
        positionTitle.toLowerCase().includes('psikolog');

      if (!isChiefOrKepalaOrPsikolog) {
        console.error(`PUT /api/leave-management/approve/${id} - Forbidden: Employee is not a Chief, Kepala, or Psikolog`);
        return NextResponse.json({ error: 'Forbidden: You can only approve leave requests from Chief, Kepala, or Psikolog positions' }, { status: 403 });
      }
    }

    // Update status leave request menjadi approved
    const updatedLeaveRequest = await prisma.leaveRequest.update({
      where: {
        id: parseInt(id)
      },
      data: {
        status: 'approved',
        approvedById: parseInt(userData.id),
        updatedAt: new Date()
      }
    });

    console.log(`PUT /api/leave-management/approve/${id} - Leave request approved successfully`);
    return NextResponse.json(updatedLeaveRequest);
  } catch (error) {
    console.error(`PUT /api/leave-management/approve/${id} - Failed to approve leave request:`, error);
    if (error instanceof Error) {
      console.error('Error stack:', error.stack);
      console.error('Error message:', error.message);
    }
    return NextResponse.json({ error: 'Failed to approve leave request' }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
