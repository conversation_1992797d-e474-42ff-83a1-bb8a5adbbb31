/**
 * API Route: POST /api/koperasi/members/import
 *
 * Deskripsi: Import anggota koperasi dari file Excel
 * Penggunaan: Form import anggota koperasi
 *
 * Body:
 * - file: File Excel (multipart/form-data)
 *
 * Response:
 * - 200: Import berhasil
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';



export async function POST(request: Request) {
  try {
    // Verify admin or operator_kop access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      );
    }

    // Check file extension
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
      return NextResponse.json(
        { error: 'Only Excel files (.xlsx, .xls) are allowed' },
        { status: 400 }
      );
    }

    // Read file
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    // Get first sheet
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    if (!Array.isArray(jsonData) || jsonData.length === 0) {
      return NextResponse.json(
        { error: 'No data found in the Excel file' },
        { status: 400 }
      );
    }

    // Validate data
    const validationErrors: string[] = [];
    jsonData.forEach((row: any, index) => {
      if (!row.employeeId) {
        validationErrors.push(`Row ${index + 2}: Employee ID is required`);
      }
      if (!row.monthlyContribution) {
        validationErrors.push(`Row ${index + 2}: Monthly contribution is required`);
      }
    });

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation errors', details: validationErrors },
        { status: 400 }
      );
    }

    // Import data
    const results = {
      total: jsonData.length,
      success: 0,
      updated: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const row of jsonData as any[]) {
      try {
        // Check if employee exists
        const employee = await prisma.employee.findUnique({
          where: { employeeId: row.employeeId.toString() }
        });

        if (!employee) {
          results.failed++;
          results.errors.push(`Employee ID ${row.employeeId} not found`);
          continue;
        }

        // Check if employee is already a member
        const existingMember = await prisma.koperasiMember.findUnique({
          where: { employeeId: employee.id }
        });

        // Prepare data for create or update
        const memberData = {
          monthlyContribution: parseFloat(row.monthlyContribution),
          oneTimeContribution: row.oneTimeContribution ? parseFloat(row.oneTimeContribution) : null,
          optionalContribution: row.optionalContribution ? parseFloat(row.optionalContribution) : null,
        };

        if (existingMember) {
          // Update existing member
          await prisma.koperasiMember.update({
            where: { id: existingMember.id },
            data: memberData
          });

          results.updated++;
          continue;
        }

        // Create new koperasi member
        await prisma.koperasiMember.create({
          data: {
            employee: { connect: { id: employee.id } },
            joinDate: new Date(),
            ...memberData,
            totalSavings: 0,
            status: 'active'
          }
        });

        results.success++;
      } catch (error) {
        console.error('Error importing member:', error);
        results.failed++;
        results.errors.push(`Error importing employee ID ${row.employeeId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      message: 'Import completed',
      results
    });
  } catch (error) {
    console.error('Failed to import koperasi members:', error);
    return NextResponse.json(
      { error: 'Failed to import koperasi members' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
