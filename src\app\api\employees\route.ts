/**
 * API Route: /api/employees
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    // Periksa apakah user sudah login
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      // Jangan log error, ini normal ketika user logout
      return NextResponse.json([], { status: 401 });
    }

    // Periksa parameter include dan filter
    const { searchParams } = new URL(request.url);
    const includeParam = searchParams.get('include');
    const includeAcademicYear = includeParam?.includes('academicYear');
    const includeDeleted = searchParams.get('includeDeleted') === 'true';

    // Base where clause untuk mengecualikan admin web dan anggota eksternal
    const baseWhereClause: any = {
      NOT: {
        OR: [
          { employeeId: { startsWith: 'EMP' } }, // Mengecualikan admin web dan semua yang diawali EMP
          { employeeId: { startsWith: 'EXT' } } // Mengecualikan anggota eksternal
        ]
      }
    };

    // Hanya tambahkan filter isDeleted jika tidak includeDeleted
    if (!includeDeleted) {
      baseWhereClause.isDeleted = false; // Mengecualikan employee yang sudah di-soft-delete
    }

    // Menggunakan where clause dasar untuk semua query
    const whereClause = baseWhereClause;

    const employees = await prisma.employee.findMany({
      where: whereClause,
      select: {
        id: true,
        employeeId: true,
        firstName: true,
        lastName: true,
        status: true,
        hireDate: true,
        email: true,
        phone: true,
        address: true,
        birthDate: true,
        birthPlace: true,
        gender: true,
        religion: true,
        maritalStatus: true,
        educationLevel: true,
        bankName: true,
        bankAccount: true,
        npwp: true,
        bpjsKesehatan: true,
        bpjsKetenagakerjaan: true,
        contractType: true,
        identityType: true,
        identityNumber: true,
        educationMajor: true,
        educationInstitution: true,
        isDeleted: true, // Tambahkan isDeleted field
        department: {
          select: {
            id: true,
            name: true,
            head: {
              select: {
                id: true,
                employeeId: true, // Tambahkan employeeId
                firstName: true,
                lastName: true,
                position: {
                  select: {
                    title: true
                  }
                }
              }
            }
          }
        },
        position: {
          select: {
            id: true,
            title: true
          }
        },
        ...(includeAcademicYear ? {
          academicYear: {
            select: {
              id: true,
              ta: true,
              description: true
            }
          },
          academicYearId: true
        } : {})
      },
      orderBy: {
        firstName: 'asc'
      }
    });

    return NextResponse.json(employees);
  } catch (error: any) {
    console.error('Error fetching employees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch employees' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Implementasi POST - data diambil tapi tidak digunakan
    await req.json();
    return NextResponse.json({ message: 'Success' });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to create employee' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

