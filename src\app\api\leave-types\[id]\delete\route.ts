/**
 * API Route: DELETE /api/leave-types/[id]/delete
 *
 * Deskripsi: Menghapus tipe cuti berdasarkan ID
 * Penggunaan: Tombol hapus di halaman daftar tipe cuti
 *
 * Path Parameters:
 * - id: ID tipe cuti (number)
 *
 * Response:
 * - 200: Tipe cuti berhasil dihapus
 * - 400: Tipe cuti tidak dapat dihapus (memiliki permintaan cuti terkait)
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Tipe cuti tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';



export async function DELETE(_request: NextRequest) {
  try {
    // Await params before accessing its properties
    // params is now directly available
    // Verify admin access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extract ID from URL path
    const id = _request.nextUrl.pathname.split('/').pop() || '';

    // Check if leave type exists
    const leaveType = await prisma.leaveType.findUnique({
      where: { id: parseInt(id) }
    });

    if (!leaveType) {
      return NextResponse.json({ error: 'Leave type not found' }, { status: 404 });
    }

    // Check if there are any leave requests using this type
    const existingRequests = await prisma.leaveRequest.findFirst({
      where: { leaveTypeId: parseInt(id) }
    });

    if (existingRequests) {
      return NextResponse.json(
        { error: 'Cannot delete leave type that has associated leave requests' },
        { status: 400 }
      );
    }

    // Delete leave type
    await prisma.leaveType.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Leave type deleted successfully' });
  } catch (error) {
    console.error('Error deleting leave type:', error);
    return NextResponse.json(
      { error: 'Failed to delete leave type' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
