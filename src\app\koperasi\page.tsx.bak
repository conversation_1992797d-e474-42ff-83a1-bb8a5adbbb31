"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/lib/auth";
import type {
  KoperasiMember,
  KoperasiLoan,
  KoperasiSaving,
  KoperasiOverview,
} from "@/lib/types";
import { format, parseISO } from "date-fns";
import Navbar from "@/components/layout/Navbar";
import ProtectedRoute from "@/components/ProtectedRoute";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  CreditCard,
  DollarSign,
  Plus,
  Users,
  Wallet,
  MoreHorizontal,
  TrendingUp,
  Clock,
  AlertCircle,
  CheckCircle2,
  History,
  Edit,
  Filter,
  Download,
  Eye,
  Pencil,
  Trash2,
  Upload,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";

// Import form components
import { SavingsForm } from "@/components/forms/savings-form";
import { LoanForm } from "@/components/forms/loan-form";
import { MembershipForm } from "@/components/forms/membership-form";

export default function KoperasiPage() {
  const { user } = useAuth();

  // Inisialisasi state dengan array kosong
  const [members, setMembers] = useState<KoperasiMember[]>([]);
  const [loans, setLoans] = useState<KoperasiLoan[]>([]);
  const [savings, setSavings] = useState<KoperasiSaving[]>([]);
  const [overview, setOverview] = useState<KoperasiOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAddAccountOpen, setIsAddAccountOpen] = useState(false);
  const [isNewLoanOpen, setIsNewLoanOpen] = useState(false);
  const [isAddMemberOpen, setIsAddMemberOpen] = useState(false);
  const [isImportMembersOpen, setIsImportMembersOpen] = useState(false);
  const [isViewMemberOpen, setIsViewMemberOpen] = useState(false);
  const [isEditMemberOpen, setIsEditMemberOpen] = useState(false);
  const [isDeleteMemberOpen, setIsDeleteMemberOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<any>(null);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importLoading, setImportLoading] = useState(false);
  const [importResults, setImportResults] = useState<any>(null);
  const [importSuccess, setImportSuccess] = useState(false);
  const [notification, setNotification] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);

  // Pagination state
  const [savingsPage, setSavingsPage] = useState(1);
  const [loansPage, setLoansPage] = useState(1);
  const [membersPage, setMembersPage] = useState(1);
  const itemsPerPage = 10;

  // Helper function untuk menampilkan notifikasi
  const showNotification = (type: "success" | "error", message: string) => {
    setNotification({ type, message });
    // Hapus notifikasi setelah 5 detik
    setTimeout(() => {
      setNotification(null);
    }, 5000);
  };

  // Helper function untuk pagination
  const paginateData = (data: any[], page: number, itemsPerPage: number) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  // Helper function untuk status variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'destructive';
      case 'completed':
        return 'secondary';
      case 'active':
        return 'success';
      default:
        return 'default';
    }
  };

  // Helper function untuk capitalize first letter
  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Helper function untuk pagination
  const paginateData = (data: any[], page: number, itemsPerPage: number) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  // Helper function untuk format tanggal
  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return "-";
    try {
      const date =
        typeof dateString === "string" ? parseISO(dateString) : dateString;
      return format(date, "dd MMM yyyy");
    } catch (error) {
      console.error("Error formatting date:", error);
      return "-";
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [membersRes, loansRes, savingsRes, overviewRes] =
          await Promise.all([
            fetch("/api/koperasi/members"),
            fetch("/api/koperasi/loans"),
            fetch("/api/koperasi/savings"),
            fetch("/api/koperasi/overview"),
          ]);

        const [membersData, loansData, savingsData, overviewData] =
          await Promise.all([
            membersRes.json(),
            loansRes.json(),
            savingsRes.json(),
            overviewRes.json(),
          ]);

        // Pastikan data yang diterima adalah array, jika tidak gunakan array kosong
        setMembers(Array.isArray(membersData) ? membersData : []);
        setLoans(Array.isArray(loansData) ? loansData : []);
        setSavings(Array.isArray(savingsData) ? savingsData : []);
        setOverview(overviewData || null);
      } catch (error) {
        console.error("Failed to fetch koperasi data:", error);
        // Set nilai default jika terjadi error
        setMembers([]);
        setLoans([]);
        setSavings([]);
        setOverview(null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Form handlers
  const handleSavingsSubmit = async (data: any) => {
    try {
      console.log("Savings data:", data);
      setIsAddAccountOpen(false);
    } catch (error) {
      console.error("Error submitting savings:", error);
    }
  };

  const handleLoanSubmit = async (data: any) => {
    try {
      console.log("Loan data:", data);
      setIsNewLoanOpen(false);
    } catch (error) {
      console.error("Error submitting loan:", error);
    }
  };

  const handleMembershipSubmit = async (data: any) => {
    try {
      console.log("Membership data:", data);

      // Prepare data for API
      const payload = {
        employeeId: data.employeeId,
        monthlyContribution: data.monthlyContribution,
        oneTimeContribution: data.oneTimeContribution,
        optionalContribution: data.optionalContribution,
      };

      // Send data to API
      const response = await fetch("/api/koperasi/members/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create member");
      }

      // Refresh members list
      const membersRes = await fetch("/api/koperasi/members");
      const membersData = await membersRes.json();
      setMembers(Array.isArray(membersData) ? membersData : []);

      // Close dialog
      setIsAddMemberOpen(false);

      // Show success message
      showNotification("success", "Member added successfully");
    } catch (error) {
      console.error("Error submitting membership:", error);
      showNotification(
        "error",
        error instanceof Error ? error.message : "Failed to add member",
      );
    }
  };

  // Handler untuk tombol View Details
  const handleViewMember = (member: any) => {
    setSelectedMember(member);
    setIsViewMemberOpen(true);
  };

  // Handler untuk tombol Edit
  const handleEditMember = (member: any) => {
    setSelectedMember(member);
    setIsEditMemberOpen(true);
  };

  // Handler untuk tombol Delete
  const handleDeleteMember = (member: any) => {
    setSelectedMember(member);
    setIsDeleteMemberOpen(true);
  };

  // Handler untuk konfirmasi delete
  const handleConfirmDelete = async () => {
    if (!selectedMember) return;

    try {
      const response = await fetch(
        `/api/koperasi/members/${selectedMember.id}/delete`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete member");
      }

      // Refresh members list
      const membersRes = await fetch("/api/koperasi/members");
      const membersData = await membersRes.json();
      setMembers(Array.isArray(membersData) ? membersData : []);

      // Close dialog
      setIsDeleteMemberOpen(false);
      setSelectedMember(null);

      // Show success message
      showNotification("success", "Member deleted successfully");
    } catch (error) {
      console.error("Error deleting member:", error);
      showNotification(
        "error",
        error instanceof Error ? error.message : "Failed to delete member",
      );
    }
  };

  // Handler untuk submit edit
  const handleEditSubmit = async () => {
    if (!selectedMember) return;

    try {
      // Prepare data for API
      const payload = {
        monthlyContribution: selectedMember.monthly_contribution,
        oneTimeContribution: selectedMember.one_time_contribution,
        optionalContribution: selectedMember.optional_contribution,
        joinDate: selectedMember.join_date,
        status: selectedMember.status,
      };

      // Send data to API
      const response = await fetch(
        `/api/koperasi/members/${selectedMember.id}/edit`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update member");
      }

      // Refresh members list
      const membersRes = await fetch("/api/koperasi/members");
      const membersData = await membersRes.json();
      setMembers(Array.isArray(membersData) ? membersData : []);

      // Close dialog
      setIsEditMemberOpen(false);
      setSelectedMember(null);

      // Show success message
      showNotification("success", "Member updated successfully");
    } catch (error) {
      console.error("Error updating member:", error);
      showNotification(
        "error",
        error instanceof Error ? error.message : "Failed to update member",
      );
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  // Tambahkan pengecekan sebelum mapping
  const renderSavingsTable = () => {
    if (!Array.isArray(savings) || savings.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={7} className="text-center">
            No savings data available
          </TableCell>
        </TableRow>
      );
    }

    // Apply pagination
    const paginatedSavings = paginateData(savings, savingsPage, itemsPerPage);

    return paginatedSavings.map((saving) => (
      <TableRow key={saving.id}>
        <TableCell>SA{String(saving.id).padStart(3, "0")}</TableCell>
        <TableCell>
          {saving.member_name || `Member ${saving.member_id}`}
        </TableCell>
        <TableCell>
          {new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(saving.balance)}
        </TableCell>
        <TableCell>
          {new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(saving.one_time_contribution || 0)}
        </TableCell>
        <TableCell>
          {new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(saving.monthly_contribution)}
        </TableCell>
        <TableCell>
          {new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(saving.optional_contribution || 0)}
        </TableCell>
        <TableCell>{formatDate(saving.updated_at)}</TableCell>
        <TableCell className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <History className="h-4 w-4 mr-2" />
                Transaction History
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      </TableRow>
    ));
  };

  // Helper function untuk empty state
  const renderEmptyState = (message: string, colSpan: number) => {
    return (
      <TableRow>
        <TableCell colSpan={colSpan} className="text-center">
          {message}
        </TableCell>
      </TableRow>
    );
  };

  // Contoh penggunaan renderEmptyState:
  // {loans.length === 0 && renderEmptyState('No loans data available', 8)}
      <TableRow key={member.id}>
        <TableCell>M{String(member.id).padStart(3, "0")}</TableCell>
        <TableCell>E{String(member.employee_code).padStart(3, "0")}</TableCell>
        <TableCell>
          {member.join_date ? formatDate(member.join_date) : "-"}
        </TableCell>
        <TableCell>
          {new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(member.monthly_contribution)}
        </TableCell>
        <TableCell>
          {new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(member.total_savings)}
        </TableCell>
        <TableCell>
          <Badge variant={member.status === "active" ? "success" : "secondary"}>
            {member.status}
          </Badge>
        </TableCell>
        <TableCell className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Edit Member
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Member
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      </TableRow>
    ));
  };

  const renderLoansTable = () => {
    if (!Array.isArray(loans) || loans.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={8} className="text-center">
            No loans data available
          </TableCell>
        </TableRow>
      );
    }

    return loans.map((loan) => (
      <TableRow key={loan.id}>
        <TableCell>L{String(loan.id).padStart(3, "0")}</TableCell>
        <TableCell>M{String(loan.member_id).padStart(3, "0")}</TableCell>
        <TableCell>
          {new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 2,
          }).format(loan.amount)}
        </TableCell>
        <TableCell>{loan.purpose}</TableCell>
        <TableCell>
          {loan.application_date ? formatDate(loan.application_date) : "-"}
        </TableCell>
        <TableCell>
          <Badge variant={getStatusVariant(loan.status)}>
            {capitalizeFirstLetter(loan.status)}
          </Badge>
        </TableCell>
        <TableCell className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          {notification && (
            <div
              className={`p-4 mb-4 rounded-md ${notification.type === "success" ? "bg-green-50 text-green-700 border border-green-200" : "bg-red-50 text-red-700 border border-red-200"}`}
            >
              <div className="flex items-center">
                {notification.type === "success" ? (
                  <CheckCircle2 className="h-5 w-5 mr-2" />
                ) : (
                  <AlertCircle className="h-5 w-5 mr-2" />
                )}
                <p>{notification.message}</p>
              </div>
            </div>
          )}

          <div className="mb-8">
            <h1 className="text-3xl font-bold">Koperasi</h1>
            <p className="text-muted-foreground mt-2">
              Manage cooperative savings and loans
            </p>
          </div>

          <Tabs defaultValue="savings" className="space-y-4">
            <TabsList>
              <TabsTrigger value="savings">Savings</TabsTrigger>
              <TabsTrigger value="loans">Loans</TabsTrigger>
              {user?.role === "ADMIN" && (
                <TabsTrigger value="memberships">Memberships</TabsTrigger>
              )}
            </TabsList>

            {/* Savings Tab Content */}
            <TabsContent value="savings">
              <div className="bg-card rounded-lg shadow-sm p-4">
                <h2 className="text-xl font-semibold mb-4">Account Overview</h2>
                <div className="space-y-4">
                  {/* Overview Cards */}
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Total Savings
                        </CardTitle>
                        <DollarSign className="h-4 w-4 text-green-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-lg font-medium">
                          {new Intl.NumberFormat("id-ID", {
                            style: "currency",
                            currency: "IDR",
                          }).format(overview?.totalSavings || 0)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Updated today
                        </p>
                      </CardContent>
                    </Card>
                    {/* Add more overview cards as needed */}
                  </div>

                  {/* Savings Table */}
                  <div className="bg-card rounded-lg shadow">
                    <div className="p-4 flex justify-between items-center border-b">
                      <h2 className="text-lg font-semibold">
                        Savings Accounts
                      </h2>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                        <Dialog
                          open={isAddAccountOpen}
                          onOpenChange={setIsAddAccountOpen}
                        >
                          <DialogTrigger asChild>
                            <Button size="sm">
                              <Plus className="h-4 w-4 mr-2" />
                              Add Account
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Add New Savings Account</DialogTitle>
                            </DialogHeader>
                            <SavingsForm
                              onSubmit={handleSavingsSubmit}
                              onCancel={() => setIsAddAccountOpen(false)}
                            />
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Account ID</TableHead>
                            <TableHead>Member Name</TableHead>
                            <TableHead>Balance</TableHead>
                            <TableHead>One Time Contribution</TableHead>
                            <TableHead>Monthly Contribution</TableHead>
                            <TableHead>Optional Contribution</TableHead>
                            <TableHead>Last Updated</TableHead>
                            <TableHead className="text-right">
                              Actions
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>{renderSavingsTable()}</TableBody>
                      </Table>
                      <div className="flex items-center justify-center py-4">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setSavingsPage(prev => Math.max(prev - 1, 1))}
                                disabled={savingsPage <= 1}
                              >
                                <ChevronLeft className="h-4 w-4" />
                              </Button>
                            </PaginationItem>
                            {Array.from({ length: Math.ceil(savings.length / itemsPerPage) || 1 }).map((_, i) => (
                              <PaginationItem key={i}>
                                <Button
                                  variant={savingsPage === i + 1 ? "default" : "outline"}
                                  size="icon"
                                  onClick={() => setSavingsPage(i + 1)}
                                >
                                  {i + 1}
                                </Button>
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setSavingsPage(prev => Math.min(prev + 1, Math.ceil(savings.length / itemsPerPage) || 1))}
                                disabled={savingsPage >= (Math.ceil(savings.length / itemsPerPage) || 1)}
                              >
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Loans Tab Content */}
            <TabsContent value="loans">
              <div className="space-y-4">
                {/* Loans Overview Cards */}
                <div className="grid gap-4 md:grid-cols-3">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Active Loans
                      </CardTitle>
                      <CreditCard className="h-4 w-4 text-blue-500" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {overview?.activeLoanCount || 0}
                      </div>
                      <p className="text-xs text-blue-500 flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        Total value:{" "}
                        {new Intl.NumberFormat("id-ID", {
                          style: "currency",
                          currency: "IDR",
                        }).format(overview?.totalLoanAmount || 0)}
                      </p>
                    </CardContent>
                  </Card>
                  {/* Add more loan overview cards */}
                </div>

                {/* Loans Table */}
                <div className="bg-card rounded-lg shadow">
                  <div className="p-4 flex justify-between items-center border-b">
                    <h2 className="text-lg font-semibold">Loan Applications</h2>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                      <Dialog
                        open={isNewLoanOpen}
                        onOpenChange={setIsNewLoanOpen}
                      >
                        <DialogTrigger asChild>
                          <Button size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            New Application
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>New Loan Application</DialogTitle>
                          </DialogHeader>
                          <LoanForm
                            onSubmit={handleLoanSubmit}
                            onCancel={() => setIsNewLoanOpen(false)}
                          />
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Loan ID</TableHead>
                          <TableHead>Member ID</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Purpose</TableHead>
                          <TableHead>Application Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginateData(loans, loansPage, itemsPerPage).map((loan) => (
                          <TableRow key={loan.id}>
                            <TableCell>
                              L{String(loan.id).padStart(3, "0")}
                            </TableCell>
                            <TableCell>
                              M{String(loan.member_id).padStart(3, "0")}
                            </TableCell>
                            <TableCell>
                              {new Intl.NumberFormat("id-ID", {
                                style: "currency",
                                currency: "IDR",
                              }).format(loan.amount)}
                            </TableCell>
                            <TableCell>{loan.purpose}</TableCell>
                            <TableCell>
                              {formatDate(loan.application_date)}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  loan.status === "approved"
                                    ? "success"
                                    : loan.status === "pending"
                                      ? "warning"
                                      : loan.status === "rejected"
                                        ? "destructive"
                                        : loan.status === "completed"
                                          ? "secondary"
                                          : "default"
                                }
                              >
                                {loan.status.charAt(0).toUpperCase() +
                                  loan.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="h-4 w-4 mr-2" />
                                    View Details
                                  </DropdownMenuItem>
                                  {user?.role === "ADMIN" && (
                                    <>
                                      <DropdownMenuItem>
                                        <Pencil className="h-4 w-4 mr-2" />
                                        Edit
                                      </DropdownMenuItem>
                                      <DropdownMenuItem className="text-destructive">
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        Delete
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    <div className="flex items-center justify-center py-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => setLoansPage(prev => Math.max(prev - 1, 1))}
                              disabled={loansPage <= 1}
                            >
                              <ChevronLeft className="h-4 w-4" />
                            </Button>
                          </PaginationItem>
                          {Array.from({ length: Math.ceil(loans.length / itemsPerPage) || 1 }).map((_, i) => (
                            <PaginationItem key={i}>
                              <Button
                                variant={loansPage === i + 1 ? "default" : "outline"}
                                size="icon"
                                onClick={() => setLoansPage(i + 1)}
                              >
                                {i + 1}
                              </Button>
                            </PaginationItem>
                          ))}
                          <PaginationItem>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => setLoansPage(prev => Math.min(prev + 1, Math.ceil(loans.length / itemsPerPage) || 1))}
                              disabled={loansPage >= (Math.ceil(loans.length / itemsPerPage) || 1)}
                            >
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Memberships Tab Content - Only for Admin */}
            {user?.role === "ADMIN" && (
              <TabsContent value="memberships">
                <div className="space-y-4">
                  {/* Membership Overview */}
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          Total Members
                        </CardTitle>
                        <Users className="h-4 w-4 text-purple-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {overview?.totalMembers || 0}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Members Table */}
                  <div className="bg-card rounded-lg shadow">
                    <div className="p-4 flex justify-between items-center border-b">
                      <h2 className="text-lg font-semibold">Members List</h2>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                        <Dialog
                          open={isAddMemberOpen}
                          onOpenChange={setIsAddMemberOpen}
                        >
                          <DialogTrigger asChild>
                            <Button size="sm">
                              <Plus className="h-4 w-4 mr-2" />
                              Add Member
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Add New Member</DialogTitle>
                            </DialogHeader>
                            <MembershipForm
                              onSubmit={handleMembershipSubmit}
                              onCancel={() => setIsAddMemberOpen(false)}
                            />
                          </DialogContent>
                        </Dialog>
                        <Dialog
                          open={isImportMembersOpen}
                          onOpenChange={setIsImportMembersOpen}
                        >
                          <DialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <Upload className="h-4 w-4 mr-2" />
                              Import Members
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Import Members</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                              <div className="space-y-2">
                                <Label htmlFor="import-file">
                                  Upload Excel File
                                </Label>
                                <Input
                                  id="import-file"
                                  type="file"
                                  accept=".xlsx,.xls"
                                  onChange={(e) => {
                                    if (
                                      e.target.files &&
                                      e.target.files.length > 0
                                    ) {
                                      setImportFile(e.target.files[0]);
                                      setImportResults(null);
                                    }
                                  }}
                                />
                                <p className="text-sm text-muted-foreground">
                                  Upload Excel file with member data.{" "}
                                  <a
                                    href="/api/koperasi/members/template"
                                    className="text-blue-500 hover:underline"
                                  >
                                    Download template
                                  </a>
                                </p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  *Jika employee ID sudah ada, data akan
                                  diperbarui (tidak ditambahkan sebagai data
                                  baru).
                                </p>
                              </div>

                              {importResults && (
                                <div className="border rounded-md p-4 bg-muted/50">
                                  <h3 className="font-medium mb-2">
                                    Import Results
                                  </h3>
                                  <div className="space-y-1 text-sm">
                                    <p>Total records: {importResults.total}</p>
                                    <p className="text-green-600">
                                      Successfully imported:{" "}
                                      {importResults.success}
                                    </p>
                                    <p className="text-blue-600">
                                      Updated: {importResults.updated}
                                    </p>
                                    <p className="text-red-600">
                                      Failed: {importResults.failed}
                                    </p>
                                    {importSuccess &&
                                      importResults.failed === 0 && (
                                        <p className="text-sm mt-2 text-green-600">
                                          Import successful! This dialog will
                                          close automatically in a few
                                          seconds...
                                        </p>
                                      )}

                                    {importResults.errors &&
                                      importResults.errors.length > 0 && (
                                        <div className="mt-2">
                                          <p className="font-medium">Errors:</p>
                                          <ul className="list-disc pl-5 text-xs text-red-600 mt-1">
                                            {importResults.errors
                                              .slice(0, 5)
                                              .map(
                                                (
                                                  error: string,
                                                  index: number,
                                                ) => (
                                                  <li key={index}>{error}</li>
                                                ),
                                              )}
                                            {importResults.errors.length >
                                              5 && (
                                              <li>
                                                ...and{" "}
                                                {importResults.errors.length -
                                                  5}{" "}
                                                more errors
                                              </li>
                                            )}
                                          </ul>
                                        </div>
                                      )}
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setIsImportMembersOpen(false);
                                  setImportFile(null);
                                  setImportResults(null);
                                }}
                                disabled={importLoading}
                              >
                                Cancel
                              </Button>
                              <Button
                                onClick={async () => {
                                  if (!importFile) {
                                    alert("Please select a file to import");
                                    return;
                                  }

                                  setImportLoading(true);
                                  try {
                                    const formData = new FormData();
                                    formData.append("file", importFile);

                                    const response = await fetch(
                                      "/api/koperasi/members/import",
                                      {
                                        method: "POST",
                                        body: formData,
                                      },
                                    );

                                    const result = await response.json();

                                    if (!response.ok) {
                                      throw new Error(
                                        result.error ||
                                          "Failed to import members",
                                      );
                                    }

                                    setImportResults(result.results);
                                    setImportSuccess(true);

                                    // Refresh members list if any were successfully imported or updated
                                    if (
                                      result.results.success > 0 ||
                                      result.results.updated > 0
                                    ) {
                                      const membersRes = await fetch(
                                        "/api/koperasi/members",
                                      );
                                      const membersData =
                                        await membersRes.json();
                                      setMembers(
                                        Array.isArray(membersData)
                                          ? membersData
                                          : [],
                                      );
                                    }

                                    // Auto close the dialog after 3 seconds only if there are no failures
                                    if (result.results.failed === 0) {
                                      setTimeout(() => {
                                        setIsImportMembersOpen(false);
                                        setImportFile(null);
                                        setImportResults(null);
                                        setImportSuccess(false);
                                      }, 3000);
                                    }
                                  } catch (error) {
                                    console.error(
                                      "Error importing members:",
                                      error,
                                    );
                                    alert(
                                      error instanceof Error
                                        ? error.message
                                        : "Failed to import members",
                                    );
                                  } finally {
                                    setImportLoading(false);
                                  }
                                }}
                                disabled={importLoading}
                              >
                                {importLoading ? "Memproses..." : "Import"}
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Member ID</TableHead>
                            <TableHead>Employee Name</TableHead>
                            <TableHead>Join Date</TableHead>
                            <TableHead>One Time Contribution</TableHead>
                            <TableHead>Monthly Contribution</TableHead>
                            <TableHead>Optional Contribution</TableHead>
                            <TableHead>Total Savings</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">
                              Actions
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {paginateData(members, membersPage, itemsPerPage).map((member) => (
                            <TableRow key={member.id}>
                              <TableCell>
                                M{String(member.id).padStart(3, "0")}
                              </TableCell>
                              <TableCell>{member.employee_name}</TableCell>
                              <TableCell>
                                {formatDate(member.join_date)}
                              </TableCell>
                              <TableCell>
                                {new Intl.NumberFormat("id-ID", {
                                  style: "currency",
                                  currency: "IDR",
                                }).format(member.one_time_contribution)}
                              </TableCell>
                              <TableCell>
                                {new Intl.NumberFormat("id-ID", {
                                  style: "currency",
                                  currency: "IDR",
                                }).format(member.monthly_contribution)}
                              </TableCell>
                              <TableCell>
                                {new Intl.NumberFormat("id-ID", {
                                  style: "currency",
                                  currency: "IDR",
                                }).format(member.optional_contribution)}
                              </TableCell>
                              <TableCell>
                                {new Intl.NumberFormat("id-ID", {
                                  style: "currency",
                                  currency: "IDR",
                                }).format(member.total_savings)}
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    member.status === "active"
                                      ? "success"
                                      : "secondary"
                                  }
                                >
                                  {member.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                      onClick={() => handleViewMember(member)}
                                    >
                                      <Eye className="h-4 w-4 mr-2" />
                                      View Details
                                    </DropdownMenuItem>
                                    {user?.role === "ADMIN" && (
                                      <>
                                        <DropdownMenuItem
                                          onClick={() =>
                                            handleEditMember(member)
                                          }
                                        >
                                          <Pencil className="h-4 w-4 mr-2" />
                                          Edit
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                          className="text-destructive"
                                          onClick={() =>
                                            handleDeleteMember(member)
                                          }
                                        >
                                          <Trash2 className="h-4 w-4 mr-2" />
                                          Delete
                                        </DropdownMenuItem>
                                      </>
                                    )}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                      <div className="flex items-center justify-center py-4">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setMembersPage(prev => Math.max(prev - 1, 1))}
                                disabled={membersPage <= 1}
                              >
                                <ChevronLeft className="h-4 w-4" />
                              </Button>
                            </PaginationItem>
                            {Array.from({ length: Math.ceil(members.length / itemsPerPage) || 1 }).map((_, i) => (
                              <PaginationItem key={i}>
                                <Button
                                  variant={membersPage === i + 1 ? "default" : "outline"}
                                  size="icon"
                                  onClick={() => setMembersPage(i + 1)}
                                >
                                  {i + 1}
                                </Button>
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setMembersPage(prev => Math.min(prev + 1, Math.ceil(members.length / itemsPerPage) || 1))}
                                disabled={membersPage >= (Math.ceil(members.length / itemsPerPage) || 1)}
                              >
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            )}
          </Tabs>

          {/* Dialog untuk View Member Details */}
          <Dialog open={isViewMemberOpen} onOpenChange={setIsViewMemberOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Member Details</DialogTitle>
              </DialogHeader>
              {selectedMember && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Member ID:</p>
                      <p>{selectedMember.id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Employee Name:</p>
                      <p>{selectedMember.employee_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Join Date:</p>
                      <p>{formatDate(selectedMember.join_date)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Status:</p>
                      <Badge
                        variant={
                          selectedMember.status === "active"
                            ? "success"
                            : "secondary"
                        }
                      >
                        {selectedMember.status}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        One Time Contribution:
                      </p>
                      <p>
                        {new Intl.NumberFormat("id-ID", {
                          style: "currency",
                          currency: "IDR",
                        }).format(selectedMember.one_time_contribution)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        Monthly Contribution:
                      </p>
                      <p>
                        {new Intl.NumberFormat("id-ID", {
                          style: "currency",
                          currency: "IDR",
                        }).format(selectedMember.monthly_contribution)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        Optional Contribution:
                      </p>
                      <p>
                        {new Intl.NumberFormat("id-ID", {
                          style: "currency",
                          currency: "IDR",
                        }).format(selectedMember.optional_contribution)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Total Savings:</p>
                      <p>
                        {new Intl.NumberFormat("id-ID", {
                          style: "currency",
                          currency: "IDR",
                        }).format(selectedMember.total_savings)}
                      </p>
                    </div>
                  </div>
                </div>
              )}
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setIsViewMemberOpen(false)}
                >
                  Close
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          {/* Dialog untuk Edit Member */}
          <Dialog open={isEditMemberOpen} onOpenChange={setIsEditMemberOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Member</DialogTitle>
              </DialogHeader>
              {selectedMember && (
                <div className="space-y-4">
                  <div className="grid gap-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="edit-employee" className="text-right">
                        Employee:
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="edit-employee"
                          value={selectedMember.employee_name}
                          disabled
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="edit-join-date" className="text-right">
                        Join Date:
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="edit-join-date"
                          type="date"
                          defaultValue={
                            selectedMember.join_date
                              ? new Date(selectedMember.join_date)
                                  .toISOString()
                                  .split("T")[0]
                              : ""
                          }
                          onChange={(e) => {
                            setSelectedMember({
                              ...selectedMember,
                              join_date: e.target.value,
                            });
                          }}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="edit-one-time" className="text-right">
                        One Time Contribution:
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="edit-one-time"
                          type="number"
                          defaultValue={selectedMember.one_time_contribution}
                          onChange={(e) => {
                            setSelectedMember({
                              ...selectedMember,
                              one_time_contribution: e.target.value,
                            });
                          }}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="edit-monthly" className="text-right">
                        Monthly Contribution:
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="edit-monthly"
                          type="number"
                          defaultValue={selectedMember.monthly_contribution}
                          onChange={(e) => {
                            setSelectedMember({
                              ...selectedMember,
                              monthly_contribution: e.target.value,
                            });
                          }}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="edit-optional" className="text-right">
                        Optional Contribution:
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="edit-optional"
                          type="number"
                          defaultValue={selectedMember.optional_contribution}
                          onChange={(e) => {
                            setSelectedMember({
                              ...selectedMember,
                              optional_contribution: e.target.value,
                            });
                          }}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="edit-status" className="text-right">
                        Status:
                      </Label>
                      <div className="col-span-3">
                        <Select
                          defaultValue={selectedMember.status}
                          onValueChange={(value) => {
                            setSelectedMember({
                              ...selectedMember,
                              status: value,
                            });
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsEditMemberOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleEditSubmit}>Save Changes</Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Dialog untuk Delete Member */}
          <Dialog
            open={isDeleteMemberOpen}
            onOpenChange={setIsDeleteMemberOpen}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Member</DialogTitle>
              </DialogHeader>
              {selectedMember && (
                <div className="space-y-4">
                  <p>Are you sure you want to delete this member?</p>
                  <div className="bg-muted p-4 rounded-md">
                    <p>
                      <strong>Member ID:</strong> {selectedMember.id}
                    </p>
                    <p>
                      <strong>Employee Name:</strong>{" "}
                      {selectedMember.employee_name}
                    </p>
                  </div>
                  <p className="text-destructive">
                    This action cannot be undone.
                  </p>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsDeleteMemberOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button variant="destructive" onClick={handleConfirmDelete}>
                      Delete
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </main>
      </div>
    </ProtectedRoute>
  );
}
