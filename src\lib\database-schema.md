# Employee Management System Database Schema

## Tables

### 1. users
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'employee', -- 'admin', 'supervisor', 'employee'
  last_login TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  employee_id INTEGER REFERENCES employees(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. employees
```sql
CREATE TABLE employees (
  id SERIAL PRIMARY KEY,
  employee_id VARCHAR(10) UNIQUE NOT NULL,
  first_name VARCHAR(50) NOT NULL,
  last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  phone VARCHAR(20),
  hire_date DATE NOT NULL,
  department_id INTEGER REFERENCES departments(id),
  position_id INTEGER REFERENCES positions(id),
  manager_id INTEGER REFERENCES employees(id),
  address TEXT,
  city VARCHAR(50),
  state VARCHAR(50),
  postal_code VARCHAR(20),
  country VARCHAR(50),
  birth_date DATE,
  gender VARCHAR(10),
  marital_status VARCHAR(20),
  emergency_contact_name VARCHAR(100),
  emergency_contact_phone VARCHAR(20),
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. departments
```sql
CREATE TABLE departments (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  head_id INTEGER REFERENCES employees(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. positions
```sql
CREATE TABLE positions (
  id SERIAL PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  department_id INTEGER REFERENCES departments(id),
  description TEXT,
  min_salary DECIMAL(12,2),
  max_salary DECIMAL(12,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. leave_types
```sql
CREATE TABLE leave_types (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) UNIQUE N/*  */OT NULL,
  description TEXT,
  days_allowed INTEGER NOT NULL,
  requires_approval BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. leave_requests
```sql
CREATE TABLE leave_requests (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER REFERENCES employees(id) NOT NULL,
  leave_type_id INTEGER REFERENCES leave_types(id) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  reason TEXT,
  status VARCHAR(20) DEFAULT 'pending',
  approved_by INTEGER REFERENCES employees(id),
  approved_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 6. koperasi_members
```sql
CREATE TABLE koperasi_members (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER REFERENCES employees(id) UNIQUE NOT NULL,
  join_date DATE NOT NULL,
  monthly_contribution DECIMAL(12,2) NOT NULL,
  total_savings DECIMAL(12,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. koperasi_loans
```sql
CREATE TABLE koperasi_loans (
  id SERIAL PRIMARY KEY,
  member_id INTEGER REFERENCES koperasi_members(id) NOT NULL,
  amount DECIMAL(12,2) NOT NULL,
  purpose TEXT NOT NULL,
  application_date DATE NOT NULL,
  approval_date DATE,
  start_date DATE,
  end_date DATE,
  interest_rate DECIMAL(5,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  approved_by INTEGER REFERENCES employees(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 8. koperasi_loan_payments
```sql
CREATE TABLE koperasi_loan_payments (
  id SERIAL PRIMARY KEY,
  loan_id INTEGER REFERENCES koperasi_loans(id) NOT NULL,
  payment_date DATE NOT NULL,
  amount DECIMAL(12,2) NOT NULL,
  payment_method VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 9. salary_components
```sql
CREATE TABLE salary_components (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  type VARCHAR(20) NOT NULL, -- 'earning' or 'deduction'
  is_taxable BOOLEAN DEFAULT FALSE,
  is_percentage BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 10. employee_salary
```sql
CREATE TABLE employee_salary (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER REFERENCES employees(id) NOT NULL,
  basic_salary DECIMAL(12,2) NOT NULL,
  effective_date DATE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 11. employee_salary_components
```sql
CREATE TABLE employee_salary_components (
  id SERIAL PRIMARY KEY,
  employee_salary_id INTEGER REFERENCES employee_salary(id) NOT NULL,
  component_id INTEGER REFERENCES salary_components(id) NOT NULL,
  amount DECIMAL(12,2) NOT NULL,
  percentage DECIMAL(5,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 12. payroll
```sql
CREATE TABLE payroll (
  id SERIAL PRIMARY KEY,
  period_start_date DATE NOT NULL,
  period_end_date DATE NOT NULL,
  payment_date DATE NOT NULL,
  status VARCHAR(20) DEFAULT 'draft',
  processed_by INTEGER REFERENCES employees(id),
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 13. payroll_items
```sql
CREATE TABLE payroll_items (
  id SERIAL PRIMARY KEY,
  payroll_id INTEGER REFERENCES payroll(id) NOT NULL,
  employee_id INTEGER REFERENCES employees(id) NOT NULL,
  basic_salary DECIMAL(12,2) NOT NULL,
  total_earnings DECIMAL(12,2) NOT NULL,
  total_deductions DECIMAL(12,2) NOT NULL,
  net_salary DECIMAL(12,2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 14. payroll_item_components
```sql
CREATE TABLE payroll_item_components (
  id SERIAL PRIMARY KEY,
  payroll_item_id INTEGER REFERENCES payroll_items(id) NOT NULL,
  component_id INTEGER REFERENCES salary_components(id) NOT NULL,
  amount DECIMAL(12,2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Relationships

1. An employee belongs to one department and one position
2. A department can have many employees
3. A position can have many employees
4. An employee can have many leave requests
5. A leave request belongs to one employee and one leave type
6. An employee can be a koperasi member
7. A koperasi member can have many loans
8. A loan can have many payments
9. An employee can have one current salary record
10. A salary record can have many components
11. A payroll period can have many payroll items (one per employee)
12. A payroll item can have many components

## Indexes

```sql
-- Employees table indexes
CREATE INDEX idx_employees_department ON employees(department_id);
CREATE INDEX idx_employees_position ON employees(position_id);
CREATE INDEX idx_employees_manager ON employees(manager_id);
CREATE INDEX idx_employees_status ON employees(status);

-- Leave requests indexes
CREATE INDEX idx_leave_requests_employee ON leave_requests(employee_id);
CREATE INDEX idx_leave_requests_status ON leave_requests(status);
CREATE INDEX idx_leave_requests_dates ON leave_requests(start_date, end_date);

-- Koperasi loans indexes
CREATE INDEX idx_koperasi_loans_member ON koperasi_loans(member_id);
CREATE INDEX idx_koperasi_loans_status ON koperasi_loans(status);

-- Payroll indexes
CREATE INDEX idx_payroll_dates ON payroll(period_start_date, period_end_date);
CREATE INDEX idx_payroll_items_employee ON payroll_items(employee_id);
CREATE INDEX idx_payroll_items_payroll ON payroll_items(payroll_id);
```
