/**
 * API Route: /api/leave-management/approve
 * 
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 */

import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
