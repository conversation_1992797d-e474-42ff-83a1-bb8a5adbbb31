{"private": true, "scripts": {"dev": "next dev --turbo", "build": "next build --no-lint", "build:docker": "NEXT_TYPESCRIPT_CHECK=0 NODE_OPTIONS='--max-old-space-size=4096 --no-warnings' next build --no-lint", "start": "next start", "clean": "rimraf .next node_modules/.cache", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true next build", "predev": "npm run clean", "prebuild": "npm run clean"}, "dependencies": {"@aws-sdk/client-s3": "^3.810.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "latest", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "autoprefixer": "^10.4.17", "bcryptjs": "^3.0.2", "beasties": "^0.3.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "critters": "^0.0.23", "embla-carousel-react": "^8.0.0", "exceljs": "^4.4.0", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.350.0", "next": "latest", "next-themes": "^0.2.1", "prisma": "latest", "react": "latest", "react-day-picker": "^8.10.0", "react-dom": "latest", "react-hook-form": "^7.50.1", "react-resizable-panels": "^2.0.9", "react-to-print": "^3.0.5", "recharts": "^2.15.3", "stripe": "^14.17.0", "swr": "^2.3.3", "vaul": "^0.9.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@next/bundle-analyzer": "^15.2.3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/js-cookie": "^3.0.6", "@types/node": "^20.17.25", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@types/uuid": "^10.0.0", "glob": "^11.0.1", "lru-cache": "^11.1.0", "postcss": "^8.4.35", "prettier": "^3.5.3", "rimraf": "^6.0.1", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "latest"}, "engines": {"node": ">=22.0.0"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "resolutions": {"glob": "^11.0.1", "rimraf": "^6.0.1", "inflight": "^2.0.1", "lodash.isequal": "^4.5.0"}}