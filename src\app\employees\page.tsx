"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { ImportEmployeeDialog } from "@/components/ImportEmployeeDialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { FileDown, Eye, Edit, Trash2, Search, Filter, Download, Plus, Upload } from "lucide-react";
import { Users, UserCheck, User, UserMinus } from "lucide-react";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { useAuth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import Navbar from "@/components/layout/Navbar";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { EmployeeForm } from "@/components/forms/employee-form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import * as XLSX from 'xlsx';
import { ScrollArea } from "@/components/ui/scroll-area";

type Employee = {
  id: number;
  employeeId: string;
  firstName: string;
  lastName: string;
  department?: {
    id: number;
    name: string;
    head?: {
      id: number;
      employeeId?: string;
      firstName: string;
      lastName: string;
      position?: {
        title: string;
      }
    }
  };
  position?: {
    id: number;
    title: string;
  };
  status: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  birthDate?: string;
  birthPlace?: string;
  gender?: string;
  religion?: string;
  maritalStatus?: string;
  educationLevel?: string;
  educationMajor?: string;
  educationInstitution?: string;
  hireDate?: string;
  joinDate?: string;
  contractType?: string;
  identityType?: string;
  identityNumber?: string;
  npwp?: string;
  bpjsKesehatan?: string;
  bpjsKetenagakerjaan?: string;
  bankName?: string;
  bankAccount?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  isDeleted?: boolean;
  deletedAt?: string;
  user?: {
    username: string;
    role: string;
  };
};

export default function EmployeesPage() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const { user } = useAuth();
  const isAdmin = user?.role === "ADMIN";
  const isHead = user?.role === "HEAD";
  const { toast } = useToast();

  // Add departments state
  const [departments, setDepartments] = useState<{ id: number; name: string; }[]>([]);

  // Tambahkan state baru
  const [searchQuery, setSearchQuery] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);

  // Tambahkan state untuk summary
  const [summary, setSummary] = useState({
    total: 0,
    tetap: 0,
    kontrak: 0,
    honor: 0
  });

  // Tambahkan fungsi untuk menghitung summary
  const calculateSummary = useCallback((employeeData: Employee[]) => {
    if (user?.role === 'SUPERVISOR') {
      // Logika khusus untuk supervisor: hitung berdasarkan department head
      console.log('Calculating summary for SUPERVISOR:', user.employeeId);

      const filteredData = employeeData.filter(emp => {
        const matchById = emp.department?.head?.id === parseInt(user.id || '0');
        const matchByEmployeeId = emp.department?.head?.employeeId === user.employeeId;

        return matchById || matchByEmployeeId;
      });

      console.log('Filtered data for summary:', filteredData.length);

      const supervisorSummary = filteredData.reduce((acc, employee) => {
        acc.total += 1;
        switch (employee.status?.toLowerCase()) {
          case 'tetap':
            acc.tetap += 1;
            break;
          case 'kontrak':
            acc.kontrak += 1;
            break;
          case 'honor':
            acc.honor += 1;
            break;
        }
        return acc;
      }, {
        total: 0,
        tetap: 0,
        kontrak: 0,
        honor: 0
      });

      console.log('Supervisor summary:', supervisorSummary);

      setSummary(supervisorSummary);
    } else {
      // Logika untuk admin: hitung semua kecuali yang diawali dengan EMP
      const adminSummary = employeeData.reduce((acc, employee) => {
        if (!employee.employeeId.startsWith('EMP')) {
          acc.total += 1;
          switch (employee.status?.toLowerCase()) {
            case 'tetap':
              acc.tetap += 1;
              break;
            case 'kontrak':
              acc.kontrak += 1;
              break;
            case 'honor':
              acc.honor += 1;
              break;
          }
        }
        return acc;
      }, {
        total: 0,
        tetap: 0,
        kontrak: 0,
        honor: 0
      });

      setSummary(adminSummary);
    }
  }, [user]);

  // Add useEffect to fetch departments
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const response = await fetch('/api/departments');
        if (!response.ok) throw new Error('Failed to fetch departments');
        const data = await response.json();
        setDepartments(data);
      } catch (error) {
        logger.error('Error fetching departments:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch departments"
        });
      }
    };

    fetchDepartments();
  }, []);

  // Tambahkan fungsi filter
  const getFilteredEmployees = useCallback(() => {
    return employees.filter(emp => {
      // Pastikan emp dan propertinya ada sebelum mengakses
      const fullName = `${emp?.firstName || ''} ${emp?.lastName || ''}`.toLowerCase();
      const employeeId = emp?.employeeId?.toLowerCase() || '';
      const departmentId = emp?.department?.id?.toString() || '';
      const employeeStatus = emp?.status || '';

      const matchesSearch =
        fullName.includes(searchQuery.toLowerCase()) ||
        employeeId.includes(searchQuery.toLowerCase());

      const matchesDepartment =
        filterDepartment === 'all' ||
        departmentId === filterDepartment;

      const matchesStatus =
        filterStatus === 'all' ||
        employeeStatus.toLowerCase() === filterStatus.toLowerCase();

      return matchesSearch && matchesDepartment && matchesStatus;
    });
  }, [employees, searchQuery, filterDepartment, filterStatus]);

  // Tambahkan fungsi export
  const handleExportExcel = async () => {
    // Pastikan hanya ADMIN yang bisa mengakses fitur ini
    if (user?.role !== 'ADMIN') {
      toast({
        title: "Access Denied",
        description: "Only administrators can export employee data",
        variant: "destructive",
      });
      return;
    }
    try {
      const filteredData = getFilteredEmployees();

      // Prepare data untuk Excel dengan informasi lengkap dari relasi
      const excelData = filteredData.map(emp => ({
        'Employee ID': emp.employeeId,
        'Full Name': `${emp.firstName} ${emp.lastName}`,
        'Department': emp.department?.name || '-',
        'Position': emp.position?.title || '-',
        'Status': emp.status,
        'Join Date': emp.hireDate ? new Date(emp.hireDate).toLocaleDateString('id-ID') : '-',
        'Email': emp.email || '-',
        'Phone': emp.phone || '-',
        'Address': emp.address || '-',
        'Birth Date': emp.birthDate ? new Date(emp.birthDate).toLocaleDateString('id-ID') : '-',
        'Birth Place': emp.birthPlace || '-',
        'Gender': emp.gender || '-',
        'Religion': emp.religion || '-',
        'Marital Status': emp.maritalStatus || '-',
        'Education Level': emp.educationLevel || '-',
        'Bank Name': emp.bankName || '-',
        'Bank Account': emp.bankAccount || '-',
        'NPWP': emp.npwp || '-',
        'BPJS Kesehatan': emp.bpjsKesehatan || '-',
        'BPJS Ketenagakerjaan': emp.bpjsKetenagakerjaan || '-',
        'Contract Type': emp.contractType || '-',
        'Identity Type': emp.identityType || '-',
        'Identity Number': emp.identityNumber || '-',
        'Education Major': emp.educationMajor || '-',
        'Education Institution': emp.educationInstitution || '-'
      }));

      // Buat workbook baru
      const workbook = XLSX.utils.book_new();

      // Convert data ke worksheet
      const worksheet = XLSX.utils.json_to_sheet(excelData);

      // Styling: set lebar kolom
      const columnWidths = [
        { wch: 12 },  // Employee ID
        { wch: 30 },  // Full Name
        { wch: 20 },  // Department
        { wch: 20 },  // Position
        { wch: 12 },  // Status
        { wch: 12 },  // Join Date
        { wch: 30 },  // Email
        { wch: 15 },  // Phone
        { wch: 40 },  // Address
        { wch: 12 },  // Birth Date
        { wch: 20 },  // Birth Place
        { wch: 10 },  // Gender
        { wch: 15 },  // Religion
        { wch: 15 },  // Marital Status
        { wch: 15 },  // Education Level
        { wch: 15 },  // Bank Name
        { wch: 20 },  // Bank Account
        { wch: 20 },  // NPWP
        { wch: 20 },  // BPJS Kesehatan
        { wch: 20 },  // BPJS Ketenagakerjaan
        { wch: 15 },  // Contract Type
        { wch: 15 },  // Identity Type
        { wch: 20 },  // Identity Number
        { wch: 15 },  // Education Level
        { wch: 25 },  // Education Major
        { wch: 30 },  // Education Institution
      ];
      worksheet['!cols'] = columnWidths;

      // Tambahkan styling untuk header
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:T1');
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = XLSX.utils.encode_col(C) + '1';
        if (!worksheet[address]) continue;
        worksheet[address].s = {
          font: { bold: true },
          fill: { fgColor: { rgb: "EFEFEF" } },
          alignment: { horizontal: "center" }
        };
      }

      // Tambahkan worksheet ke workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Employees');

      // Generate dan download file dengan timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      XLSX.writeFile(workbook, `employees_data_${timestamp}.xlsx`);

      toast({
        title: "Success",
        description: "Employee data exported successfully to Excel",
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: "Error",
        description: "Failed to export employee data",
        variant: "destructive",
      });
    }
  };

  // Hapus useRef untuk print dan useReactToPrint

  // Tambahkan handler untuk export PDF
  const handleExportPDF = () => {
    // Pastikan hanya ADMIN yang bisa mengakses fitur ini
    if (user?.role !== 'ADMIN') {
      toast({
        title: "Access Denied",
        description: "Only administrators can export employee data to PDF",
        variant: "destructive",
      });
      return;
    }
    if (!selectedEmployee) {
      toast({
        title: "Error",
        description: "No employee selected for export",
        variant: "destructive",
      });
      return;
    }

    try {
      const doc = new jsPDF();
      const marginLeft = 25; // Menggeser semua konten ke kanan

      // Set font styles untuk judul utama
      doc.setFont("helvetica", "bold");
      doc.setFontSize(20);
      doc.setTextColor(33, 33, 33);
      doc.text('Employee Details', marginLeft, 20);

      // Personal Information Section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(14);
      doc.setTextColor(71, 85, 105);
      doc.text('Personal Information', marginLeft, 40);

      // Background rectangle untuk personal info
      doc.setFillColor(243, 244, 246);
      doc.rect(marginLeft - 5, 45, 170, 60, 'F'); // Menggeser rectangle ke kanan

      const personalInfo = [
        ['Employee ID:', selectedEmployee.employeeId],
        ['Full Name:', `${selectedEmployee.firstName} ${selectedEmployee.lastName || ''}`],
        ['Birth Date:', selectedEmployee.birthDate ? new Date(selectedEmployee.birthDate).toLocaleDateString() : '-'],
        ['Gender:', selectedEmployee.gender || '-'],
        ['Marital Status:', selectedEmployee.maritalStatus || '-']
      ];

      // Employment Information Section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(14);
      doc.setTextColor(71, 85, 105);
      doc.text('Employment Information', marginLeft, 120);

      // Background rectangle untuk employment info
      doc.setFillColor(243, 244, 246);
      doc.rect(marginLeft - 5, 125, 170, 60, 'F');

      const employmentInfo = [
        ['Department:', selectedEmployee.department?.name || '-'],
        ['Position:', selectedEmployee.position?.title || '-'],
        ['Status:', selectedEmployee.status],
        ['Hire Date:', selectedEmployee.hireDate ? new Date(selectedEmployee.hireDate).toLocaleDateString() : '-']
      ];

      // Contact Information Section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(14);
      doc.setTextColor(71, 85, 105);
      doc.text('Contact Information', marginLeft, 200);

      // Background rectangle untuk contact info - tinggi disesuaikan
      doc.setFillColor(243, 244, 246);
      doc.rect(marginLeft - 5, 205, 170, 85, 'F'); // Mengubah tinggi dari 60 ke 85

      const contactInfo = [
        ['Email:', selectedEmployee.email || '-'],
        ['Phone:', selectedEmployee.phone || '-'],
        ['Address:', selectedEmployee.address || '-'],
        ['City:', selectedEmployee.city || '-'],
        ['State:', selectedEmployee.state || '-'],
        ['Postal Code:', selectedEmployee.postalCode || '-'],
        ['Country:', selectedEmployee.country || '-']
      ];

      // Generate tables dengan margin yang disesuaikan
      autoTable(doc, {
        startY: 50,
        margin: { left: marginLeft },
        body: personalInfo,
        theme: 'plain',
        styles: {
          fontSize: 10,
          textColor: [51, 51, 51],
          cellPadding: 3,
        },
        columnStyles: {
          0: {
            fontStyle: 'bold',
            cellWidth: 40,
          },
          1: {
            cellWidth: 125, // Sedikit dikurangi untuk menyesuaikan margin
          }
        },
      });

      autoTable(doc, {
        startY: 130,
        margin: { left: marginLeft },
        body: employmentInfo,
        theme: 'plain',
        styles: {
          fontSize: 10,
          textColor: [51, 51, 51],
          cellPadding: 3,
        },
        columnStyles: {
          0: {
            fontStyle: 'bold',
            cellWidth: 40,
          },
          1: {
            cellWidth: 125,
          }
        },
      });

      // Generate table untuk contact info dengan penyesuaian
      autoTable(doc, {
        startY: 210,
        margin: { left: marginLeft },
        body: contactInfo,
        theme: 'plain',
        styles: {
          fontSize: 10,
          textColor: [51, 51, 51],
          cellPadding: 3,
          minCellHeight: 10, // Memastikan tinggi sel minimum
        },
        columnStyles: {
          0: {
            fontStyle: 'bold',
            cellWidth: 40,
          },
          1: {
            cellWidth: 125,
          }
        },
      });

      // Footer dengan tanggal - posisi disesuaikan
      const today = new Date().toLocaleDateString();
      doc.setFont("helvetica", "italic");
      doc.setFontSize(8);
      doc.setTextColor(156, 163, 175);
      doc.text(`Generated on: ${today}`, marginLeft, 300); // Mengubah posisi Y dari 280 ke 300

      doc.save(`Employee-${selectedEmployee.employeeId}-details.pdf`);

      toast({
        title: "Success",
        description: "PDF exported successfully",
      });
    } catch (error) {
      logger.error('PDF export failed:', error);
      toast({
        title: "Error",
        description: "Failed to export PDF",
        variant: "destructive",
      });
    }
  };

  // Pindahkan fetchEmployees keluar dari useEffect
  const fetchEmployees = async () => {
    logger.debug('Employees Page - Starting to fetch employees');
    try {
      // Gunakan endpoint yang sama untuk semua role
      const url = '/api/employees';
      const response = await fetch(url);
      logger.debug('Employees Page - API Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      // Tidak perlu log data karyawan yang sensitif
      logger.debug(`Fetched ${Array.isArray(data) ? data.length : 0} employees`);

      setEmployees(Array.isArray(data) ? data : []);
      calculateSummary(Array.isArray(data) ? data : []);
    } catch (error) {
      logger.error('Employees Page - Failed to fetch employees:', error);
      setEmployees([]);
      setSummary({ total: 0, tetap: 0, kontrak: 0, honor: 0 });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  const handleEmployeeSubmit = async (data: any) => {
    try {
      logger.debug('Submitting employee data');
      const response = await fetch('/api/employees/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();
      logger.debug('Employee creation response received');

      if (!response.ok) {
        throw new Error(responseData.error || responseData.details || 'Failed to add employee');
      }

      // Refresh employee list
      await fetchEmployees();

      setIsDialogOpen(false);
      toast({
        title: "Success",
        description: "Employee added successfully",
      });
    } catch (error) {
      logger.error("Error submitting employee:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add employee",
        variant: "destructive",
      });
    }
  };

  const handleView = async (id: number) => {
    try {
      const response = await fetch(`/api/employees/${id}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch employee details');
      }

      const employeeData = await response.json();
      setSelectedEmployee(employeeData);
      setIsViewDialogOpen(true);
    } catch (error) {
      logger.error('Error fetching employee details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch employee details",
        variant: "destructive",
      });
    }
  };

  const handleEdit = async (id: number) => {
    try {
      logger.debug(`Preparing to edit employee with ID: ${id}`);

      if (!id || typeof id !== 'number') {
        throw new Error('Invalid employee ID');
      }

      const response = await fetch(`/api/employees/${id}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch employee data');
      }

      const employeeData = await response.json();
      logger.debug('Employee data fetched successfully');

      // Transform data untuk form dengan memastikan semua field terisi
      const formData = {
        id: employeeData.id,
        employeeId: employeeData.employeeId,
        firstName: employeeData.firstName,
        lastName: employeeData.lastName || '',
        email: employeeData.email || '',
        phone: employeeData.phone || '',
        departmentId: employeeData.department?.id.toString() || employeeData.departmentId.toString(),
        positionId: employeeData.position?.id.toString() || employeeData.positionId.toString(),
        status: employeeData.status,
        role: employeeData.user?.role || 'EMPLOYEE', // Ambil role dari relasi user
        hireDate: employeeData.hireDate ? employeeData.hireDate.split('T')[0] : new Date().toISOString().split('T')[0],
        // Perbaiki format birthDate
        birthDate: employeeData.birthDate ? employeeData.birthDate.split('T')[0] : '',
        address: employeeData.address || '',
        city: employeeData.city || '',
        state: employeeData.state || '',
        postalCode: employeeData.postalCode || '',
        country: employeeData.country || '',
        gender: employeeData.gender || '',
        maritalStatus: employeeData.maritalStatus || '',
        emergencyContactName: employeeData.emergencyContactName || '',
        emergencyContactPhone: employeeData.emergencyContactPhone || '',
      };

      logger.debug('Form data prepared for editing');
      setSelectedEmployee(formData);
      setIsEditDialogOpen(true);
    } catch (error) {
      logger.error('Error preparing edit:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to prepare employee data for editing",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to soft delete this employee? The employee will be hidden from the system but their data will be preserved.")) {
      return;
    }

    try {
      const response = await fetch(`/api/employees/${id}/delete`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete employee');
      }

      toast({
        title: "Success",
        description: "Employee soft deleted successfully",
      });

      // Refresh data
      fetchEmployees();
    } catch (error) {
      logger.error('Error deleting employee:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to soft delete employee",
      });
    }
  };

  const handleEditSubmit = async (data: any) => {
    try {
      if (!selectedEmployee) return;

      const response = await fetch(`/api/employees/${selectedEmployee.id}`, {
        method: 'PATCH', // Changed from PUT to PATCH
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update employee');
      }

      toast({
        title: "Success",
        description: "Employee updated successfully",
      });
      setIsEditDialogOpen(false);
      fetchEmployees(); // Refresh data setelah update
    } catch (error) {
      logger.error('Error updating employee:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update employee",
        variant: "destructive",
      });
    }
  };

  const handleStatusChange = async (employeeId: number, newStatus: string) => {
    try {
      const response = await fetch(`/api/employees/${employeeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to update status');
      }

      // Refresh data setelah update
      fetchEmployees();

      toast({
        title: "Success",
        description: "Employee status updated successfully",
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: "Error",
        description: "Failed to update employee status",
        variant: "destructive",
      });
    }
  };

  const renderActions = (employee: Employee) => {
    return (
      <>
        <Button
          variant="ghost"
          size="sm"
          className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
          onClick={() => handleView(employee.id)}  // Hapus komentar JSX yang menyebabkan error
        >
          <Eye className="h-4 w-4 mr-2" />
          View
        </Button>
        {isAdmin && (
          <>
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
              onClick={() => handleEdit(employee.id)}  // Hapus komentar JSX yang menyebabkan error
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-red-600 hover:text-red-900 hover:bg-red-50"
              onClick={() => handleDelete(employee.id)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </>
        )}
      </>
    );
  };





  // Tambahkan fungsi untuk mengurutkan departments
  const getSortedDepartments = useCallback(() => {
    return [...departments].sort((a, b) => a.name.localeCompare(b.name));
  }, [departments]);

  // Definisikan status options yang diurutkan
  const statusOptions = ['Honor', 'Kontrak', 'Tetap'].sort();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const employeesPerPage = 10;

  // Fungsi untuk mendapatkan data yang sudah difilter dan dipaginasi
  const getPaginatedData = useCallback(() => {
    const filteredEmployees = getFilteredEmployees();

    // Jika user adalah SUPERVISOR, filter berdasarkan departemen yang dikepalai
    let finalData = filteredEmployees;

    if (user?.role === 'SUPERVISOR') {
      logger.debug('Filtering employees for supervisor');

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      finalData = filteredEmployees.filter(emp => {
        // Log untuk debugging
        console.log('Checking employee:', emp.firstName, emp.lastName);
        console.log('Department head ID:', emp.department?.head?.id);
        console.log('Supervisor employeeId:', user.employeeId);
        console.log('Supervisor ID:', user.id);

        // Coba berbagai kemungkinan pencocokan
        const matchById = emp.department?.head?.id === parseInt(user.id || '0');
        const matchByEmployeeId = emp.department?.head?.employeeId === user.employeeId;

        return matchById || matchByEmployeeId;
      });

      logger.debug(`Found ${finalData.length} employees under supervisor`);
    } else if (user?.role === 'HEAD') {
      logger.debug('Filtering employees for HEAD role');

      // Filter berdasarkan posisi dengan title yang mengandung "Chief", "Kepala", atau "Psikolog"
      finalData = filteredEmployees.filter(emp => {
        const positionTitle = emp.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        return isChiefOrKepalaOrPsikolog;
      });

      logger.debug(`Found ${finalData.length} employees with Chief or Kepala positions`);
    }

    const startIndex = (currentPage - 1) * employeesPerPage;
    const endIndex = startIndex + employeesPerPage;
    return finalData.slice(startIndex, endIndex);
  }, [currentPage, getFilteredEmployees, user]);

  // Hitung total halaman
  const totalPages = useMemo(() => {
    const filteredEmployees = getFilteredEmployees();

    // Jika user adalah SUPERVISOR, filter berdasarkan departemen yang dikepalai
    let finalData = filteredEmployees;

    if (user?.role === 'SUPERVISOR') {
      console.log('Filtering for SUPERVISOR (totalPages):', user.employeeId);

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      finalData = filteredEmployees.filter(emp => {
        // Cek apakah departemen karyawan dikepalai oleh supervisor yang login
        const matchById = emp.department?.head?.id === parseInt(user.id || '0');
        const matchByEmployeeId = emp.department?.head?.employeeId === user.employeeId;

        return matchById || matchByEmployeeId;
      });
    } else if (user?.role === 'HEAD') {
      console.log('Filtering for HEAD (totalPages)');

      // Filter berdasarkan posisi dengan title yang mengandung "Chief", "Kepala", atau "Psikolog"
      finalData = filteredEmployees.filter(emp => {
        const positionTitle = emp.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        return isChiefOrKepalaOrPsikolog;
      });
    }

    return Math.max(1, Math.ceil(finalData.length / employeesPerPage));
  }, [getFilteredEmployees, employeesPerPage, user]);

  // Reset halaman ke 1 dan update summary ketika filter berubah
  useEffect(() => {
    setCurrentPage(1);

    // Hitung ulang summary berdasarkan data yang difilter
    if (user?.role === 'SUPERVISOR') {
      // Untuk SUPERVISOR, hitung berdasarkan karyawan yang berada di bawah departemen yang dikepalai
      const filteredEmployees = getFilteredEmployees();
      const supervisorEmployees = filteredEmployees.filter(emp => {
        const matchById = emp.department?.head?.id === parseInt(user.id || '0');
        const matchByEmployeeId = emp.department?.head?.employeeId === user.employeeId;

        return matchById || matchByEmployeeId;
      });
      calculateSummary(supervisorEmployees);
    } else if (user?.role === 'HEAD') {
      // Untuk HEAD, hitung berdasarkan karyawan dengan posisi Chief, Kepala, atau Psikolog
      const filteredEmployees = getFilteredEmployees();
      const headEmployees = filteredEmployees.filter(emp => {
        const positionTitle = emp.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        return isChiefOrKepalaOrPsikolog;
      });
      calculateSummary(headEmployees);
    } else {
      // Untuk ADMIN, hitung berdasarkan semua karyawan yang difilter
      calculateSummary(getFilteredEmployees());
    }
  }, [searchQuery, filterDepartment, filterStatus, getFilteredEmployees, calculateSummary, user]);

  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={user?.role} />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Employees</h1>
          <p className="text-muted-foreground mt-2">
            {user?.role === "SUPERVISOR" ? "View employee data and information" : "Manage employee data and information"}
          </p>
        </div>

        {/* Summary Labels */}
        <div className="flex flex-wrap gap-4 mb-6 text-sm">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-blue-600" />
            <span className="font-medium">Total:</span>
            <span>{summary.total}</span>
          </div>
          <div className="flex items-center gap-2">
            <UserCheck className="h-4 w-4 text-green-600" />
            <span className="font-medium">Tetap:</span>
            <span>{summary.tetap}</span>
          </div>
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-orange-600" />
            <span className="font-medium">Kontrak:</span>
            <span>{summary.kontrak}</span>
          </div>
          <div className="flex items-center gap-2">
            <UserMinus className="h-4 w-4 text-purple-600" />
            <span className="font-medium">Honor:</span>
            <span>{summary.honor}</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search employees..."
              className="pl-9 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex flex-wrap gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="p-2">
                  <Label className="text-xs">Department</Label>
                  <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue placeholder="All Departments" />
                    </SelectTrigger>
                    <SelectContent>
                      <ScrollArea className="h-[200px]">
                        <SelectItem value="all">All Departments</SelectItem>
                        {getSortedDepartments().map((dept) => (
                          <SelectItem key={dept.id} value={dept.id.toString()}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </ScrollArea>
                    </SelectContent>
                  </Select>
                </div>
                <div className="p-2">
                  <Label className="text-xs">Status</Label>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      {statusOptions.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            {isAdmin && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={handleExportExcel}
                >
                  <Download className="h-4 w-4" />
                  Export
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={() => setIsImportDialogOpen(true)}
                >
                  <Upload className="h-4 w-4" />
                  Import
                </Button>
              </>
            )}

            {isAdmin && (
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Employee
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[85vh] overflow-hidden">
                  <DialogHeader>
                    <DialogTitle>Add New Employee</DialogTitle>
                  </DialogHeader>
                  <EmployeeForm
                    onSubmit={handleEmployeeSubmit}
                    onCancel={() => setIsDialogOpen(false)}
                  />
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>

        <div className="bg-card rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Employee List</h2>
          {/* Hapus div berikut yang berisi tombol duplikat */}
          {/* <div className="flex gap-2 mb-4">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filter
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div> */}

          <div className="overflow-x-auto">
            {user?.role === "SUPERVISOR" ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead className="text-center">Actions</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center">
                          Loading...
                        </TableCell>
                      </TableRow>
                    ) : getPaginatedData().length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center">
                          No employees found under your supervision
                        </TableCell>
                      </TableRow>
                    ) : (
                      getPaginatedData().map((emp) => (
                        <TableRow key={emp.id}>
                          <TableCell>
                            {`${emp.firstName} ${emp.lastName || ''}`}
                          </TableCell>
                          <TableCell className="text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
                              onClick={() => handleView(emp.id)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Button>
                          </TableCell>
                          <TableCell>{emp.department?.name || '-'}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                emp.status === 'Tetap' ? 'success' :
                                emp.status === 'Kontrak' ? 'warning' :
                                'secondary'
                              }
                            >
                              {emp.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </>
            ) : (
              // Tabel original untuk admin
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Head</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : getPaginatedData().length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center">
                        No employees found
                      </TableCell>
                    </TableRow>
                  ) : (
                    getPaginatedData().map((emp) => (
                      <TableRow key={emp.id}>
                        <TableCell>{emp.employeeId}</TableCell>
                        <TableCell>
                          {`${emp.firstName} ${emp.lastName || ''}`}
                        </TableCell>
                        <TableCell>{emp.department?.name || '-'}</TableCell>
                        <TableCell>{emp.position?.title || '-'}</TableCell>
                        <TableCell>
                          {emp.department?.head ? (
                            <div>
                              <div>{`${emp.department.head.firstName} ${emp.department.head.lastName || ''}`}</div>
                              <div className="text-xs text-muted-foreground">
                                {emp.department.head.position?.title || ''}
                              </div>
                            </div>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell>
                          <Select
                            defaultValue={emp.status}
                            onValueChange={(value) => handleStatusChange(emp.id, value)}
                          >
                            <SelectTrigger className="w-[130px]">
                              <SelectValue>
                                <Badge
                                  variant={
                                    emp.status === 'Tetap' ? 'success' :
                                    emp.status === 'Kontrak' ? 'warning' :
                                    'secondary'
                                  }
                                >
                                  {emp.status}
                                </Badge>
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {statusOptions.map(status => (
                                <SelectItem key={status} value={status}>
                                  <Badge
                                    variant={
                                      status === 'Tetap' ? 'success' :
                                      status === 'Kontrak' ? 'warning' :
                                      'secondary'
                                    }
                                  >
                                    {status}
                                  </Badge>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell className="text-center">
                          {renderActions(emp)}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            )}
          </div>

          {/* Single Pagination Component */}
          {!loading && getPaginatedData().length > 0 && (
            <div className="flex items-center justify-center py-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </main>

      {/* Add View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex justify-between items-center">
              <span>Employee Details</span>
              <div className="flex gap-2">
                {isAdmin && selectedEmployee && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportPDF}
                  >
                    <FileDown className="h-4 w-4 mr-2" />
                    Export PDF
                  </Button>
                )}
              </div>
            </DialogTitle>
          </DialogHeader>

          {selectedEmployee && (
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 bg-muted p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Personal Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Employee ID</Label>
                    <div className="text-sm font-medium">{selectedEmployee.employeeId}</div>
                  </div>
                  <div>
                    <Label>Full Name</Label>
                    <div className="text-sm font-medium">
                      {`${selectedEmployee.firstName} ${selectedEmployee.lastName || ''}`}
                    </div>
                  </div>
                  <div>
                    <Label>Birth Date</Label>
                    <div className="text-sm font-medium">
                      {selectedEmployee.birthDate ? new Date(selectedEmployee.birthDate).toLocaleDateString() : '-'}
                    </div>
                  </div>
                  <div>
                    <Label>Gender</Label>
                    <div className="text-sm font-medium">{selectedEmployee.gender || '-'}</div>
                  </div>
                  <div>
                    <Label>Marital Status</Label>
                    <div className="text-sm font-medium">{selectedEmployee.maritalStatus || '-'}</div>
                  </div>
                </div>
              </div>

              <div className="col-span-2 bg-muted p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Employment Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Department</Label>
                    <div className="text-sm font-medium">{selectedEmployee.department?.name || '-'}</div>
                  </div>
                  <div>
                    <Label>Position</Label>
                    <div className="text-sm font-medium">{selectedEmployee.position?.title || '-'}</div>
                  </div>
                  <div>
                    <Label>Status</Label>
                    <div className="text-sm font-medium">
                      <Badge variant={selectedEmployee.status === 'Active' ? 'success' : 'secondary'}>
                        {selectedEmployee.status}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label>Hire Date</Label>
                    <div className="text-sm font-medium">
                      {selectedEmployee.hireDate ? new Date(selectedEmployee.hireDate).toLocaleDateString() : '-'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-span-2 bg-muted p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Contact Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Email</Label>
                    <div className="text-sm font-medium">{selectedEmployee.email || '-'}</div>
                  </div>
                  <div>
                    <Label>Phone</Label>
                    <div className="text-sm font-medium">{selectedEmployee.phone || '-'}</div>
                  </div>
                  <div className="col-span-2">
                    <Label>Address</Label>
                    <div className="text-sm font-medium">{selectedEmployee.address || '-'}</div>
                  </div>
                  <div>
                    <Label>City</Label>
                    <div className="text-sm font-medium">{selectedEmployee.city || '-'}</div>
                  </div>
                  <div>
                    <Label>State/Province</Label>
                    <div className="text-sm font-medium">{selectedEmployee.state || '-'}</div>
                  </div>
                  <div>
                    <Label>Postal Code</Label>
                    <div className="text-sm font-medium">{selectedEmployee.postalCode || '-'}</div>
                  </div>
                  <div>
                    <Label>Country</Label>
                    <div className="text-sm font-medium">{selectedEmployee.country || '-'}</div>
                  </div>
                </div>
              </div>

              <div className="col-span-2 bg-muted p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Emergency Contact</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Name</Label>
                    <div className="text-sm font-medium">{selectedEmployee.emergencyContactName || '-'}</div>
                  </div>
                  <div>
                    <Label>Phone</Label>
                    <div className="text-sm font-medium">{selectedEmployee.emergencyContactPhone || '-'}</div>
                  </div>
                </div>
              </div>

              {selectedEmployee.user && (
                <div className="col-span-2 bg-muted p-4 rounded-lg">
                  <h3 className="font-semibold mb-2">System Access</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Username</Label>
                      <div className="text-sm font-medium">{selectedEmployee.user.username}</div>
                    </div>
                    <div>
                      <Label>Role</Label>
                      <div className="text-sm font-medium">
                        <Badge variant="outline">{selectedEmployee.user.role}</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Edit Employee</DialogTitle>
          </DialogHeader>
          <EmployeeForm
            onSubmit={handleEditSubmit}
            onCancel={() => setIsEditDialogOpen(false)}
            defaultValues={selectedEmployee}
          />
        </DialogContent>
      </Dialog>

      <ImportEmployeeDialog
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        onImportSuccess={fetchEmployees}
      />
    </div>
  );
}


