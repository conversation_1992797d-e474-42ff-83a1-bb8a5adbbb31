/**
 * API Route: /api/leave-management/[id]
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    // Redirect ke endpoint GET yang sebenarnya
    const response = await fetch(new URL(`/api/leave-management/${id}/get`, request.url), {
      method: 'GET',
      headers: request.headers
    });

    return response;
  } catch (error) {
    console.error('Error in GET /api/leave-management/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    // Redirect ke endpoint PUT yang sebenarnya
    const response = await fetch(new URL(`/api/leave-management/${id}/update`, request.url), {
      method: 'PUT',
      headers: request.headers,
      body: request.body
    });

    return response;
  } catch (error) {
    console.error('Error in PUT /api/leave-management/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    // Redirect ke endpoint DELETE yang sebenarnya
    const response = await fetch(new URL(`/api/leave-management/${id}/delete`, request.url), {
      method: 'DELETE',
      headers: request.headers
    });

    return response;
  } catch (error) {
    console.error('Error in DELETE /api/leave-management/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
