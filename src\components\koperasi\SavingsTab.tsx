import { useState } from "react";
import { KoperasiSaving } from "@/lib/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, MoreHorizontal, Eye, Pencil, Trash2, ChevronLeft, ChevronRight, Calendar, AlertCircle } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import { SavingsTransactionForm } from "@/components/forms/savings-transaction-form";
import { BulkMonthlyContributionForm } from "@/components/forms/bulk-monthly-contribution-form";
import { format, parseISO } from "date-fns";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";

interface SavingsTabProps {
  savings: KoperasiSaving[];
  members: any[];
  userRole?: string;
  onSavingsSubmit: (data: any) => Promise<void>;
  onRefresh: () => Promise<void>;
}

export function SavingsTab({ savings, members, userRole, onSavingsSubmit, onRefresh }: SavingsTabProps) {
  const [isAddTransactionOpen, setIsAddTransactionOpen] = useState(false);
  const [isBulkTransactionOpen, setIsBulkTransactionOpen] = useState(false);
  const [isViewDetailsOpen, setIsViewDetailsOpen] = useState(false);
  const [isEditTransactionOpen, setIsEditTransactionOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<KoperasiSaving | null>(null);
  const [savingsPage, setSavingsPage] = useState(1);
  const itemsPerPage = 18;

  // Helper function untuk pagination
  const paginateData = (data: any[], page: number, itemsPerPage: number) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  // Helper function untuk format tanggal
  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return "-";
    try {
      const date = typeof dateString === "string" ? parseISO(dateString) : dateString;
      return format(date, "dd MMM yyyy");
    } catch (error) {
      console.error("Error formatting date:", error);
      return "-";
    }
  };

  const handleSavingsSubmit = async (data: any) => {
    await onSavingsSubmit(data);
    setIsAddTransactionOpen(false);
  };

  const handleViewDetails = async (saving: KoperasiSaving) => {
    setSelectedTransaction(saving);
    setIsViewDetailsOpen(true);
  };

  const handleEditTransaction = async (saving: KoperasiSaving) => {
    setSelectedTransaction(saving);
    setIsEditTransactionOpen(true);
  };

  const handleEditSubmit = async (data: any) => {
    try {
      const response = await fetch(`/api/koperasi/savings/${selectedTransaction?.id}/update`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('API error response:', responseData);
        throw new Error(responseData.error || "Failed to update savings transaction");
      }

      console.log('Savings transaction updated successfully:', responseData);
      await onRefresh();
      setIsEditTransactionOpen(false);
    } catch (error) {
      console.error("Error updating savings transaction:", error);
      alert(error instanceof Error ? error.message : 'Failed to update savings transaction');
    }
  };

  const handleDeleteTransaction = async (saving: KoperasiSaving) => {
    setSelectedTransaction(saving);
    setIsDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    try {
      // Gunakan endpoint delete secara langsung untuk menghindari masalah pengalihan
      const response = await fetch(`/api/koperasi/savings/${selectedTransaction?.id}/delete`, {
        method: "DELETE",
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('API error response:', responseData);
        throw new Error(responseData.error || "Failed to delete savings transaction");
      }

      console.log('Savings transaction deleted successfully:', responseData);
      await onRefresh();
      setIsDeleteConfirmOpen(false);
    } catch (error) {
      console.error("Error deleting savings transaction:", error);
      alert(error instanceof Error ? error.message : 'Failed to delete savings transaction');
    }
  };

  const handleBulkMonthlySubmit = async (data: any) => {
    try {
      const response = await fetch("/api/koperasi/savings/bulk-monthly", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('API error response:', responseData);
        throw new Error(responseData.error || "Failed to create bulk monthly contributions");
      }

      console.log('Bulk monthly contributions created successfully:', responseData);
      await onRefresh();
      setIsBulkTransactionOpen(false);
      alert(`Successfully created ${responseData.count} monthly contribution transactions`);
    } catch (error) {
      console.error("Error creating bulk monthly contributions:", error);
      alert(error instanceof Error ? error.message : 'Failed to create bulk monthly contributions');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Transaction</h2>
        <div className="flex space-x-2">
          {(userRole === 'ADMIN' || userRole === 'OPERATOR_KOP') && (
            <Dialog open={isBulkTransactionOpen} onOpenChange={setIsBulkTransactionOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Bulk Monthly Contribution
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Bulk Monthly Contributions</DialogTitle>
                </DialogHeader>
                <BulkMonthlyContributionForm
                  onSubmit={handleBulkMonthlySubmit}
                  onCancel={() => setIsBulkTransactionOpen(false)}
                />
              </DialogContent>
            </Dialog>
          )}
          <Dialog open={isAddTransactionOpen} onOpenChange={setIsAddTransactionOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Transaction
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Savings Transaction</DialogTitle>
              </DialogHeader>
              <SavingsTransactionForm
                onSubmit={handleSavingsSubmit}
                onCancel={() => setIsAddTransactionOpen(false)}
                members={members}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction ID</TableHead>
              <TableHead>Member Name</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Contribution Type</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Notes</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {savings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center">
                  No savings data available
                </TableCell>
              </TableRow>
            ) : (
              paginateData(savings, savingsPage, itemsPerPage).map((saving) => (
                <TableRow key={saving.id}>
                  <TableCell>S{String(saving.id).padStart(3, "0")}</TableCell>
                  <TableCell>{saving.member_name}</TableCell>
                  <TableCell>
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(saving.amount)}
                  </TableCell>
                  <TableCell>
                    <span className={saving.type === "deposit" ? "text-green-600" : "text-red-600"}>
                      {saving.type ? saving.type.charAt(0).toUpperCase() + saving.type.slice(1) : 'Unknown'}
                    </span>
                  </TableCell>
                  <TableCell>
                    {saving.type === "deposit" && saving.contributionType ?
                      (saving.contributionType === 'one_time' ? 'One Time' :
                       saving.contributionType === 'monthly' ? 'Monthly' :
                       'Optional') :
                      "-"}
                  </TableCell>
                  <TableCell>{formatDate(saving.date)}</TableCell>
                  <TableCell>{saving.notes || "-"}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(saving)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        {/* Edit action hanya untuk ADMIN */}
                        {userRole === "ADMIN" && (
                          <DropdownMenuItem onClick={() => handleEditTransaction(saving)}>
                            <Pencil className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {/* Delete action untuk ADMIN dan OPERATOR_KOP */}
                        {(userRole === "ADMIN" || userRole === "OPERATOR_KOP") && (
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteTransaction(saving)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <div className="flex items-center justify-center py-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setSavingsPage(prev => Math.max(prev - 1, 1))}
                  disabled={savingsPage <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </PaginationItem>
              {(() => {
                const totalPages = Math.ceil(savings.length / itemsPerPage) || 1;
                const maxVisiblePages = 5; // Jumlah halaman yang ditampilkan

                let startPage = Math.max(1, savingsPage - Math.floor(maxVisiblePages / 2));
                let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                // Adjust startPage if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                const pages = [];

                // Add first page and ellipsis if needed
                if (startPage > 1) {
                  pages.push(
                    <PaginationItem key="first">
                      <Button
                        variant={savingsPage === 1 ? "default" : "outline"}
                        size="icon"
                        onClick={() => setSavingsPage(1)}
                      >
                        1
                      </Button>
                    </PaginationItem>
                  );

                  if (startPage > 2) {
                    pages.push(
                      <PaginationItem key="ellipsis-start">
                        <span className="px-2">...</span>
                      </PaginationItem>
                    );
                  }
                }

                // Add visible pages
                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <PaginationItem key={i}>
                      <Button
                        variant={savingsPage === i ? "default" : "outline"}
                        size="icon"
                        onClick={() => setSavingsPage(i)}
                      >
                        {i}
                      </Button>
                    </PaginationItem>
                  );
                }

                // Add last page and ellipsis if needed
                if (endPage < totalPages) {
                  if (endPage < totalPages - 1) {
                    pages.push(
                      <PaginationItem key="ellipsis-end">
                        <span className="px-2">...</span>
                      </PaginationItem>
                    );
                  }

                  pages.push(
                    <PaginationItem key="last">
                      <Button
                        variant={savingsPage === totalPages ? "default" : "outline"}
                        size="icon"
                        onClick={() => setSavingsPage(totalPages)}
                      >
                        {totalPages}
                      </Button>
                    </PaginationItem>
                  );
                }

                return pages;
              })()}
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setSavingsPage(prev => Math.min(prev + 1, Math.ceil(savings.length / itemsPerPage) || 1))}
                  disabled={savingsPage >= (Math.ceil(savings.length / itemsPerPage) || 1)}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>

      {/* View Details Dialog */}
      <Dialog open={isViewDetailsOpen} onOpenChange={setIsViewDetailsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Transaction Details</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Transaction ID</Label>
                <div className="font-medium">S{String(selectedTransaction?.id || '').padStart(3, "0")}</div>
              </div>
              <div>
                <Label>Member Name</Label>
                <div className="font-medium">{selectedTransaction?.member_name}</div>
              </div>
              <div>
                <Label>Amount</Label>
                <div className="font-medium">
                  {selectedTransaction ? new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                  }).format(selectedTransaction.amount) : '-'}
                </div>
              </div>
              <div>
                <Label>Type</Label>
                <div className={`font-medium ${selectedTransaction?.type === "deposit" ? "text-green-600" : "text-red-600"}`}>
                  {selectedTransaction?.type ? selectedTransaction.type.charAt(0).toUpperCase() + selectedTransaction.type.slice(1) : 'Unknown'}
                </div>
              </div>
              <div>
                <Label>Contribution Type</Label>
                <div className="font-medium">
                  {selectedTransaction?.type === "deposit" && selectedTransaction?.contributionType ?
                    (selectedTransaction.contributionType === 'one_time' ? 'One Time' :
                     selectedTransaction.contributionType === 'monthly' ? 'Monthly' :
                     'Optional') :
                    "-"}
                </div>
              </div>
              <div>
                <Label>Date</Label>
                <div className="font-medium">{selectedTransaction?.date ? formatDate(selectedTransaction.date) : '-'}</div>
              </div>
            </div>
            <div>
              <Label>Notes</Label>
              <div className="font-medium whitespace-pre-wrap">{selectedTransaction?.notes || "-"}</div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDetailsOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Transaction Dialog */}
      <Dialog open={isEditTransactionOpen} onOpenChange={setIsEditTransactionOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Savings Transaction</DialogTitle>
          </DialogHeader>
          {selectedTransaction && (
            <SavingsTransactionForm
              initialData={{
                memberId: String(selectedTransaction.member_id),
                amount: String(selectedTransaction.amount),
                type: selectedTransaction.type,
                contributionType: selectedTransaction.contributionType,
                date: selectedTransaction.date ? new Date(selectedTransaction.date).toISOString().split('T')[0] : '',
                notes: selectedTransaction.notes
              }}
              onSubmit={handleEditSubmit}
              onCancel={() => setIsEditTransactionOpen(false)}
              members={members}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this transaction? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={confirmDelete}>Delete</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
