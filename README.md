# Employee Management System (EMS)

A comprehensive employee management system with features for managing employees, leave requests, payroll, and cooperative services.

## System Requirements

- Node.js 22.x or higher
- MySQL 8.0 or higher
- npm 10.x or higher

## Development Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables by copying `.env.example` to `.env` and updating the values
4. Run the development server:
   ```bash
   npm run dev
   ```

## Docker Setup

### Prerequisites

- Docker and Docker Compose installed on your system
- Basic knowledge of Docker commands

### Getting Started

1. **Configure Environment Variables**

   Copy the `.env.docker` file to `.env`:

   ```bash
   cp .env.docker .env
   ```

   Edit the `.env` file and set secure values for:
   - `MYSQL_ROOT_PASSWORD` (use a strong password)
   - `JWT_SECRET` (use a long, random string)

2. **Build and Start the Containers**

   ```bash
   docker-compose up -d
   ```

   This command will:
   - Build the Next.js application image
   - Start the MySQL database
   - Start the application container
   - Connect the containers via a Docker network

3. **Access the Application**

   Once the containers are running, you can access the application at:

   ```
   http://localhost:3000
   ```

4. **Database Initialization**

   The database will be initialized with the schema defined in your Prisma migrations.

   If you need to run additional migrations or seed the database, you can use:

   ```bash
   # Connect to the app container
   docker-compose exec app sh

   # Run Prisma commands
   npx prisma migrate deploy
   npx prisma db seed
   ```

### Managing the Application

- **Stop the Application**
  ```bash
  docker-compose down
  ```

- **View Logs**
  ```bash
  docker-compose logs -f
  ```

- **Rebuild the Application**
  If you make changes to the application code, you'll need to rebuild the Docker image:
  ```bash
  docker-compose build app
  docker-compose up -d
  ```

## Project Structure

```
EMSv3/
├── prisma/                  # Database configuration and migrations
│   ├── schema.prisma        # Database schema
│   └── migrations/          # Database migration files
├── public/                  # Static assets
├── src/
│   ├── app/                 # Application routing and pages
│   │   ├── api/             # API endpoints
│   │   │   ├── auth/        # Authentication API
│   │   │   ├── employees/   # Employee API
│   │   │   ├── koperasi/    # Cooperative API
│   │   │   ├── leave/       # Leave management API
│   │   │   └── salary/      # Payroll API
│   │   ├── dashboard/       # Dashboard pages
│   │   ├── employees/       # Employee pages
│   │   ├── koperasi/        # Cooperative pages
│   │   ├── leave-management/ # Leave management pages
│   │   ├── login/           # Login page
│   │   ├── salary/          # Payroll pages
│   │   └── page.tsx         # Main page (landing page)
│   ├── components/          # Reusable React components
│   │   ├── auth/            # Authentication components
│   │   ├── dashboard/       # Dashboard components
│   │   ├── employees/       # Employee components
│   │   ├── koperasi/        # Cooperative components
│   │   ├── layout/          # Layout components (Navbar, Sidebar, etc.)
│   │   ├── leave/           # Leave management components
│   │   ├── salary/          # Payroll components
│   │   ├── sections/        # Section components for landing page
│   │   └── ui/              # Basic UI components
│   ├── lib/                 # Utilities and configuration
│   │   ├── auth.tsx         # Authentication context
│   │   ├── db.ts            # Database configuration
│   │   ├── logger.ts        # Logging utilities
│   │   └── utils.ts         # Common utility functions
│   └── styles/              # CSS and styling files
└── package.json             # Dependencies and scripts
```

## Technologies Used

- **Frontend**: Next.js 15.2.4, React, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API Routes
- **Database**: MySQL
- **ORM**: Prisma
- **Authentication**: Custom auth with JWT
- **PDF Generation**: React-PDF
- **Deployment**: Docker / Self-hosted