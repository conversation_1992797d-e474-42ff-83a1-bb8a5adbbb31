"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import Navbar from "@/components/layout/Navbar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Pencil, Plus, Trash2, Users } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Checkbox,
  CheckboxIndicator,
} from "@/components/ui/checkbox";

// Definisi tipe untuk Academic Year
type AcademicYear = {
  id: number;
  ta: string;
  description: string | null;
  currentEmployeeCount?: number;
  historicalActiveCount?: number;
  historicalExitedCount?: number;
  createdAt: string;
  updatedAt: string;
  employees?: {
    id: number;
    employeeId: string;
    firstName: string;
    lastName: string | null;
    isDeleted: boolean;
  }[];
};

// Definisi tipe untuk Department
type Department = {
  id: number;
  name: string;
};

// Definisi tipe untuk Employee
type Employee = {
  id: number;
  employeeId: string;
  firstName: string;
  lastName: string | null;
  departmentId: number;
  department?: {
    id: number;
    name: string;
  };
  academicYearId: number | null;
  academicYear?: {
    id: number;
    ta: string;
  };
  isDeleted: boolean;
  status: string;
};

// Schema validasi form
const academicYearSchema = z.object({
  ta: z.string().min(1, "Tahun Akademik wajib diisi"),
  description: z.string().optional(),
  includeExitedEmployees: z.boolean().optional(),
});

// Schema validasi form bulk assign
const bulkAssignSchema = z.object({
  academicYearId: z.string().min(1, "Tahun Akademik wajib dipilih"),
  departmentId: z.string().optional(),
  status: z.string().optional(),
  includeDeleted: z.boolean().optional(),
});

export default function AcademicYearSettingsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingEmployees, setLoadingEmployees] = useState(false);
  const [bulkAssignLoading, setBulkAssignLoading] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<AcademicYear | null>(null);
  const [activeTab, setActiveTab] = useState("manage");

  // Form untuk tambah academic year
  const addForm = useForm<z.infer<typeof academicYearSchema>>({
    resolver: zodResolver(academicYearSchema),
    defaultValues: {
      ta: "",
      description: "",
      includeExitedEmployees: false,
    },
  });

  // Form untuk edit academic year
  const editForm = useForm<z.infer<typeof academicYearSchema>>({
    resolver: zodResolver(academicYearSchema),
    defaultValues: {
      ta: "",
      description: "",
    },
  });

  // Form untuk bulk assign academic year
  const bulkAssignForm = useForm<z.infer<typeof bulkAssignSchema>>({
    resolver: zodResolver(bulkAssignSchema),
    defaultValues: {
      academicYearId: "",
      departmentId: "all",
      status: "all",
      includeDeleted: false,
    },
  });

  // Redirect jika bukan ADMIN
  useEffect(() => {
    if (user && user.role !== "ADMIN") {
      router.push("/");
    }
  }, [user, router]);

  // Fetch data academic year
  const fetchAcademicYears = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/academic-years");

      if (!response.ok) {
        throw new Error("Failed to fetch academic years");
      }

      const data = await response.json();
      setAcademicYears(Array.isArray(data) ? data : []);
    } catch (error) {
      logger.error('Academic Year Settings - Failed to fetch academic years:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Gagal mengambil data tahun akademik",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch data departments
  const fetchDepartments = async () => {
    try {
      const response = await fetch("/api/departments");

      if (!response.ok) {
        throw new Error("Failed to fetch departments");
      }

      const data = await response.json();
      // Urutkan departments berdasarkan nama
      const sortedDepartments = [...data].sort((a, b) =>
        a.name.localeCompare(b.name)
      );
      setDepartments(sortedDepartments);
    } catch (error) {
      logger.error('Academic Year Settings - Failed to fetch departments:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Gagal mengambil data departemen",
      });
    }
  };

  // Fetch data employees
  const fetchEmployees = async () => {
    try {
      setLoadingEmployees(true);
      // Gunakan endpoint khusus untuk mendapatkan data lengkap karyawan dengan academic year
      // Dan sertakan parameter includeDeleted=true untuk mendapatkan semua karyawan termasuk yang sudah di-softdelete
      const response = await fetch("/api/employees?include=academicYear&includeDeleted=true");

      if (!response.ok) {
        throw new Error("Failed to fetch employees");
      }

      const data = await response.json();
      // Filter out admin web and external members
      const filteredData = data.filter((emp: Employee) =>
        !emp.employeeId.startsWith("EMP") && !emp.employeeId.startsWith("EXT")
      );
      setEmployees(filteredData);

      // Terapkan filter berdasarkan form saat ini
      const currentValues = bulkAssignForm.getValues();
      handleFilterEmployees(currentValues);
    } catch (error) {
      logger.error('Academic Year Settings - Failed to fetch employees:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Gagal mengambil data karyawan",
      });
    } finally {
      setLoadingEmployees(false);
    }
  };

  useEffect(() => {
    fetchAcademicYears();
    fetchDepartments();
    fetchEmployees();
  }, []);

  // Effect untuk filter employees ketika form berubah
  useEffect(() => {
    const subscription = bulkAssignForm.watch((values) => {
      handleFilterEmployees(values as z.infer<typeof bulkAssignSchema>);
    });

    return () => subscription.unsubscribe();
  }, [bulkAssignForm, employees]);

  // Handler untuk tambah academic year
  const handleAddAcademicYear = async (values: z.infer<typeof academicYearSchema>) => {
    try {
      const response = await fetch("/api/academic-years", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add academic year");
      }

      toast({
        title: "Success",
        description: "Tahun akademik berhasil ditambahkan",
      });

      addForm.reset();
      setIsAddDialogOpen(false);
      fetchAcademicYears();
    } catch (error: any) {
      logger.error('Academic Year Settings - Failed to add academic year:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal menambahkan tahun akademik",
      });
    }
  };

  // Handler untuk edit academic year
  const handleEditAcademicYear = async (values: z.infer<typeof academicYearSchema>) => {
    if (!selectedAcademicYear) return;

    try {
      const response = await fetch(`/api/academic-years/${selectedAcademicYear.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update academic year");
      }

      toast({
        title: "Success",
        description: "Tahun akademik berhasil diperbarui",
      });

      editForm.reset();
      setIsEditDialogOpen(false);
      setSelectedAcademicYear(null);
      fetchAcademicYears();
    } catch (error: any) {
      logger.error('Academic Year Settings - Failed to update academic year:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal memperbarui tahun akademik",
      });
    }
  };

  // Handler untuk delete academic year
  const handleDeleteAcademicYear = async () => {
    if (!selectedAcademicYear) return;

    try {
      const response = await fetch(`/api/academic-years/${selectedAcademicYear.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete academic year");
      }

      toast({
        title: "Success",
        description: "Tahun akademik berhasil dihapus",
      });

      setIsDeleteDialogOpen(false);
      setSelectedAcademicYear(null);
      fetchAcademicYears();
    } catch (error: any) {
      logger.error('Academic Year Settings - Failed to delete academic year:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal menghapus tahun akademik",
      });
    }
  };

  // Handler untuk filter employees
  const handleFilterEmployees = (values: z.infer<typeof bulkAssignSchema>) => {
    let filtered = [...employees];

    // Filter berdasarkan departemen
    if (values.departmentId && values.departmentId !== "all") {
      filtered = filtered.filter(emp => emp.departmentId === parseInt(values.departmentId));
    }

    // Filter berdasarkan status
    if (values.status && values.status !== "all") {
      filtered = filtered.filter(emp => emp.status === values.status);
    }

    // Filter berdasarkan isDeleted
    if (!values.includeDeleted) {
      filtered = filtered.filter(emp => !emp.isDeleted);
    }

    setFilteredEmployees(filtered);
    // Reset selected employees
    setSelectedEmployees([]);
  };

  // Handler untuk toggle select employee
  const toggleSelectEmployee = (id: number) => {
    setSelectedEmployees(prev => {
      if (prev.includes(id)) {
        return prev.filter(empId => empId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Handler untuk select all employees
  const toggleSelectAll = () => {
    if (selectedEmployees.length === filteredEmployees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(filteredEmployees.map(emp => emp.id));
    }
  };

  // Handler untuk bulk assign academic year
  const handleBulkAssign = async (values: z.infer<typeof bulkAssignSchema>) => {
    try {
      setBulkAssignLoading(true);

      // Jika ada karyawan yang dipilih, gunakan itu
      // Jika tidak, gunakan filter
      const payload = selectedEmployees.length > 0
        ? {
            academicYearId: values.academicYearId,
            employeeIds: selectedEmployees
          }
        : {
            academicYearId: values.academicYearId,
            filter: {
              departmentId: (values.departmentId && values.departmentId !== "all") ? values.departmentId : undefined,
              status: (values.status && values.status !== "all") ? values.status : undefined,
              isDeleted: values.includeDeleted || undefined
            }
          };

      const response = await fetch("/api/academic-years/bulk-assign", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to bulk assign academic year");
      }

      const data = await response.json();

      toast({
        title: "Success",
        description: `Berhasil mengatur tahun akademik untuk ${data.count} karyawan`,
      });

      // Refresh data
      fetchEmployees();
      fetchAcademicYears();
    } catch (error: any) {
      logger.error('Academic Year Settings - Failed to bulk assign academic year:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal mengatur tahun akademik secara masal",
      });
    } finally {
      setBulkAssignLoading(false);
    }
  };

  // Handler untuk membuka dialog edit
  const openEditDialog = (academicYear: AcademicYear) => {
    setSelectedAcademicYear(academicYear);
    editForm.setValue("ta", academicYear.ta);
    editForm.setValue("description", academicYear.description || "");
    setIsEditDialogOpen(true);
  };

  // Handler untuk membuka dialog delete
  const openDeleteDialog = (academicYear: AcademicYear) => {
    setSelectedAcademicYear(academicYear);
    setIsDeleteDialogOpen(true);
  };

  if (user && user.role !== "ADMIN") {
    return null; // Tidak render apapun jika bukan ADMIN
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={user?.role} />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Academic Year Settings</h1>
          <p className="text-muted-foreground mt-2">
            Manage academic years for employee grouping
          </p>
        </div>

        <Tabs defaultValue="manage" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="manage">Manage Academic Years</TabsTrigger>
            <TabsTrigger value="bulk-assign">Bulk Assign</TabsTrigger>
          </TabsList>

          <TabsContent value="manage">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Academic Years</CardTitle>
                  <CardDescription>
                    List of all academic years in the system
                  </CardDescription>
                </div>
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      Add Academic Year
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add New Academic Year</DialogTitle>
                      <DialogDescription>
                        Create a new academic year for employee grouping
                      </DialogDescription>
                    </DialogHeader>
                    <Form {...addForm}>
                      <form onSubmit={addForm.handleSubmit(handleAddAcademicYear)} className="space-y-4">
                        <FormField
                          control={addForm.control}
                          name="ta"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tahun Akademik (TA)</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g. 2023/2024" {...field} />
                              </FormControl>
                              <FormDescription>
                                Format tahun akademik, contoh: 2023/2024
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={addForm.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea placeholder="Description of the academic year" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={addForm.control}
                          name="includeExitedEmployees"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Include Exited Employees</FormLabel>
                                <FormDescription>
                                  Include employees who have left the organization when creating this academic year
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />
                        <DialogFooter>
                          <Button type="submit">Save</Button>
                        </DialogFooter>
                      </form>
                    </Form>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tahun Akademik</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Current Employees</TableHead>
                        <TableHead>Historical Count</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center">
                            Loading...
                          </TableCell>
                        </TableRow>
                      ) : academicYears.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center">
                            No academic years found
                          </TableCell>
                        </TableRow>
                      ) : (
                        academicYears.map((academicYear) => {
                          const totalEmployees = academicYear.employees?.length || 0;
                          const activeEmployees = academicYear.employees?.filter(emp => !emp.isDeleted).length || 0;
                          const exitedEmployees = academicYear.employees?.filter(emp => emp.isDeleted).length || 0;

                          return (
                            <TableRow key={academicYear.id}>
                              <TableCell className="font-medium">{academicYear.ta}</TableCell>
                              <TableCell>{academicYear.description || "-"}</TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  <div className="font-medium">
                                    {totalEmployees} Total
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {activeEmployees} Active, {exitedEmployees} Exited
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {academicYear.currentEmployeeCount !== undefined && academicYear.currentEmployeeCount !== null ? (
                                  <div className="space-y-1">
                                    <div className="font-medium">
                                      {academicYear.currentEmployeeCount} Total
                                    </div>
                                    {(academicYear.historicalActiveCount !== undefined && academicYear.historicalExitedCount !== undefined) ? (
                                      <div className="text-sm text-muted-foreground">
                                        {academicYear.historicalActiveCount} Active, {academicYear.historicalExitedCount} Exited
                                      </div>
                                    ) : (
                                      <div className="text-sm text-muted-foreground">
                                        Legacy data
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  "-"
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => openEditDialog(academicYear)}
                                  >
                                    <Pencil className="h-4 w-4" />
                                    <span className="sr-only">Edit</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => openDeleteDialog(academicYear)}
                                  >
                                    <Trash2 className="h-4 w-4 text-destructive" />
                                    <span className="sr-only">Delete</span>
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bulk-assign">
            <Card>
              <CardHeader>
                <CardTitle>Bulk Assign Academic Year</CardTitle>
                <CardDescription>
                  Assign academic year to multiple employees at once
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...bulkAssignForm}>
                  <form onSubmit={bulkAssignForm.handleSubmit(handleBulkAssign)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <FormField
                        control={bulkAssignForm.control}
                        name="academicYearId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Academic Year</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select academic year" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="none">None</SelectItem>
                                {academicYears.map((year) => (
                                  <SelectItem key={year.id} value={year.id.toString()}>
                                    {year.ta} {year.description ? `- ${year.description}` : ''}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={bulkAssignForm.control}
                        name="departmentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Department</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="All departments" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="all">All departments</SelectItem>
                                {departments.map((dept) => (
                                  <SelectItem key={dept.id} value={dept.id.toString()}>
                                    {dept.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Filter by department
                            </FormDescription>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={bulkAssignForm.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="All statuses" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="all">All statuses</SelectItem>
                                <SelectItem value="Tetap">Tetap</SelectItem>
                                <SelectItem value="Kontrak">Kontrak</SelectItem>
                                <SelectItem value="Honor">Honor</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Filter by employee status
                            </FormDescription>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={bulkAssignForm.control}
                        name="includeDeleted"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Include Exited Employees</FormLabel>
                              <FormDescription>
                                Include employees who have left the organization
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        disabled={bulkAssignLoading || !bulkAssignForm.getValues().academicYearId}
                        className="flex items-center gap-2"
                      >
                        {bulkAssignLoading ? "Processing..." : "Assign Academic Year"}
                      </Button>
                    </div>
                  </form>
                </Form>

                <div className="mt-8">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Employees ({filteredEmployees.length})</h3>
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0}
                        onCheckedChange={toggleSelectAll}
                        disabled={filteredEmployees.length === 0}
                      />
                      <span className="text-sm">Select All</span>
                    </div>
                  </div>

                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">Select</TableHead>
                          <TableHead>ID</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Department</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Current Academic Year</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {loadingEmployees ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center">
                              Loading employees...
                            </TableCell>
                          </TableRow>
                        ) : filteredEmployees.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center">
                              No employees found matching the criteria
                            </TableCell>
                          </TableRow>
                        ) : (
                          filteredEmployees.map((employee) => (
                            <TableRow key={employee.id} className={employee.isDeleted ? "bg-muted/50" : ""}>
                              <TableCell>
                                <Checkbox
                                  checked={selectedEmployees.includes(employee.id)}
                                  onCheckedChange={() => toggleSelectEmployee(employee.id)}
                                />
                              </TableCell>
                              <TableCell>{employee.employeeId}</TableCell>
                              <TableCell>
                                {`${employee.firstName} ${employee.lastName || ''}`}
                                {employee.isDeleted && <span className="ml-2 text-xs text-destructive">(Exited)</span>}
                              </TableCell>
                              <TableCell>{employee.department?.name || '-'}</TableCell>
                              <TableCell>{employee.status}</TableCell>
                              <TableCell>{employee.academicYear?.ta || 'None'}</TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Academic Year</DialogTitle>
              <DialogDescription>
                Update the academic year information
              </DialogDescription>
            </DialogHeader>
            <Form {...editForm}>
              <form onSubmit={editForm.handleSubmit(handleEditAcademicYear)} className="space-y-4">
                <FormField
                  control={editForm.control}
                  name="ta"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tahun Akademik (TA)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. 2023/2024" {...field} />
                      </FormControl>
                      <FormDescription>
                        Format tahun akademik, contoh: 2023/2024
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Description of the academic year" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit">Save Changes</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Delete Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Academic Year</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this academic year?
                {selectedAcademicYear?.employees && selectedAcademicYear.employees.length > 0 && (
                  <p className="text-destructive mt-2">
                    This academic year has {selectedAcademicYear.employees.length} associated employees.
                    You cannot delete it until you remove all associated employees.
                  </p>
                )}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteAcademicYear}
                disabled={selectedAcademicYear?.employees && selectedAcademicYear.employees.length > 0}
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
}
