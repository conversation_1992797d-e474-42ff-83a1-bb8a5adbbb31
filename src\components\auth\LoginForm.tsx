"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { logger } from "@/lib/logger";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { LockIcon, UserIcon, Loader2, Eye, EyeOff } from "lucide-react";
import { useAuth } from "@/lib/auth";

interface LoginFormProps {
  onSubmit?: (data: {
    username: string;
    password: string;
    rememberMe: boolean;
  }) => void;
  isLoading?: boolean;
  error?: string;
}

const LoginForm = ({
  onSubmit,
  isLoading: externalIsLoading = false,
  error: externalError = "",
}: LoginFormProps) => {
  logger.debug('LoginForm rendered');
  const router = useRouter();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState(externalError);
  const [isLoading, setIsLoading] = useState(externalIsLoading);

  // Update internal loading state when external loading state changes
  useEffect(() => {
    logger.debug('LoginForm: loading state updated');
    setIsLoading(externalIsLoading);
  }, [externalIsLoading]);
  const [showPassword, setShowPassword] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted, isLoading:', isLoading);

    if (isLoading) {
      console.log('Form submission ignored because isLoading is true');
      return;
    }

    const data = {
      username,
      password,
      rememberMe,
    };

    if (onSubmit) {
      console.log('Calling external onSubmit function');
      onSubmit(data);
    } else {
      console.log('Calling internal handleLogin function');
      handleLogin(data);
    }
  };

  const handleLogin = async (data: {
    username: string;
    password: string;
    rememberMe: boolean;
  }) => {
    if (isLoading) return;

    setIsLoading(true);
    setError("");
    let loginSuccess = false;

    try {
      const result = await login(data.username, data.password);
      loginSuccess = result.success;

      if (result.success && result.role) {
        let redirectPath;

        switch (result.role) {
          case "ADMIN":
            redirectPath = "/dashboard";
            break;
          case "SUPERVISOR":
            redirectPath = "/employees";
            break;
          case "HEAD":
            redirectPath = "/employees";
            break;
          case "EMPLOYEE":
            redirectPath = "/leave-management";
            break;
          case "OPERATOR_KOP":
            redirectPath = "/koperasi";
            break;
          default:
            redirectPath = "/";
        }

        // Log untuk debugging (tanpa data sensitif)
        logger.debug('Login successful, redirecting user');

        // Tunggu sebentar sebelum redirect untuk menampilkan loading state
        await new Promise(resolve => setTimeout(resolve, 1000));
        window.location.href = redirectPath; // Menggunakan window.location.href untuk full page reload
      } else {
        setError("Invalid username or password");
      }
    } catch (err) {
      logger.error("Login error:", err);
      setError("An error occurred during login");
    } finally {
      // Hanya nonaktifkan loading state jika login gagal (tidak ada redirect)
      if (!loginSuccess) {
        logger.debug('Login failed, disabling loading state');
        setIsLoading(false);
      }
    }
  };

  // Quick login buttons removed for security

  return (
    <Card className="w-full max-w-md mx-auto bg-white shadow-lg">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">Login</CardTitle>
        <CardDescription className="text-center">
          Enter your credentials to access your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        {(error || externalError) && (
          <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4 text-sm">
            {error || externalError}
          </div>
        )}
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Label htmlFor="username">Username</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <UserIcon className="h-5 w-5" />
                </div>
                <Input
                  id="username"
                  placeholder="Enter your username"
                  className="pl-10"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                />
              </div>
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                  <LockIcon className="h-5 w-5" />
                </div>
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  className="pl-10 pr-10"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute inset-y-0 right-0 flex items-center px-3 focus:outline-none"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </Button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked as boolean)}
              />
              <Label
                htmlFor="remember"
                className="text-sm font-normal cursor-pointer"
              >
                Remember me
              </Label>
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
              onClick={() => logger.debug('Login button clicked')}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <span>Logging in...</span>
                </div>
              ) : (
                "Log in"
              )}
            </Button>
            {/* Quick login buttons removed for security */}
          </div>
        </form>
      </CardContent>
      {/* Sign up option - hidden */}
    </Card>
  );
};

export default LoginForm;





