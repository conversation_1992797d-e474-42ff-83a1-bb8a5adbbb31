/**
 * API Route: DELETE /api/leave-management/[id]/delete
 *
 * Deskripsi: Menghapus permintaan cuti
 * Penggunaan: Tombol hapus permintaan cuti
 *
 * Path Parameters:
 * - id: ID permintaan cuti (number)
 *
 * Response:
 * - 200: Permintaan cuti berhasil dihapus
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Permintaan cuti tidak ditemukan
 * - 409: Permintaan cuti tidak dapat dihapus (sudah disetujui/ditolak)
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';



export async function DELETE(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const isAdmin = userData.role === 'ADMIN';

    // Get ID from params
    const id = params.id;

    // Check if leave request exists
    const leaveRequest = await prisma.leaveRequest.findUnique({
      where: { id: parseInt(id) }
    });

    if (!leaveRequest) {
      return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
    }

    // Only admin can delete any leave request
    // Regular users can only delete their own pending requests
    if (!isAdmin) {
      if (leaveRequest.employeeId !== parseInt(userData.id)) {
        return NextResponse.json(
          { error: 'You can only delete your own leave requests' },
          { status: 403 }
        );
      }

      if (leaveRequest.status !== 'pending') {
        return NextResponse.json(
          { error: 'You can only delete pending leave requests' },
          { status: 409 }
        );
      }
    }

    // Delete leave request
    await prisma.leaveRequest.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Leave request deleted successfully' });
  } catch (error) {
    console.error('Error deleting leave request:', error);
    return NextResponse.json(
      { error: 'Failed to delete leave request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
