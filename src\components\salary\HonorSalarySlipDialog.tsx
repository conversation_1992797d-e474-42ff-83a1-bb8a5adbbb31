import { Sal<PERSON>Honor } from "@/lib/types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { HonorSalarySlip } from "./HonorSalarySlip";
import { useRef, useState } from "react";
import { Download } from "lucide-react";
import { toast } from "../ui/use-toast";
// Note: You'll need to install these packages:
// npm install jspdf html2canvas
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { logger } from "@/lib/logger";

interface HonorSalarySlipDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  data: SalaryHonor | null;
}

export function HonorSalarySlipDialog({
  open,
  onOpenChange,
  data,
}: HonorSalarySlipDialogProps) {
  const printRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);

  const exportToPDF = async () => {
    if (!printRef.current) return;

    setIsExporting(true);
    try {
      // Notify user that export is starting
      toast({
        title: "Exporting to PDF",
        description: "Please wait while we generate your PDF..."
      });

      const element = printRef.current;

      // Apply a temporary style to reduce top padding for the period section
      const periodElements = element.querySelectorAll('.period-row');
      periodElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.style.marginTop = '-4px';
        }
      });

      // Create a canvas with the element
      const canvas = await html2canvas(element, {
        scale: 2, // Balance between quality and file size
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff'
      });

      // Reset the temporary style
      periodElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.style.marginTop = '';
        }
      });

      const imgData = canvas.toDataURL('image/png');

      // F4 dimensions (215 x 330 mm in portrait mode)
      const pageWidth = 215; // F4 width in mm
      const pageHeight = 330; // F4 height in mm

      // Calculate image dimensions to fit F4 page while maintaining aspect ratio
      const imgWidth = pageWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: [pageWidth, pageHeight]
      });

      // Add image to PDF with margins
      const leftMargin = 10; // 10mm left margin
      const topMargin = 10;  // 10mm top margin

      // Calculate scale to fit content properly
      const contentWidth = imgWidth - (leftMargin * 2);
      const contentHeight = imgHeight;

      pdf.addImage(imgData, 'PNG', leftMargin, topMargin, contentWidth, contentHeight);

      // Add footer text - right-aligned and italicized
      pdf.setFont(undefined, 'italic');
      pdf.setFontSize(8);
      const footerText = 'Untuk legalitas perlu ditambahkan cap basah Yayasan';
      const footerWidth = pdf.getStringUnitWidth(footerText) * 8 / pdf.internal.scaleFactor;
      const footerX = pageWidth - footerWidth - 15; // 15mm from right edge
      const footerY = pageHeight - 15; // 15mm from bottom
      pdf.text(footerText, footerX, footerY);

      // Generate filename
      const filename = `Slip_Gaji_${data?.nama.replace(/\s+/g, '_') || 'Honor'}_${data?.period ? new Date(data.period).toLocaleDateString('id-ID', { month: 'long', year: 'numeric' }).replace(/\s+/g, '_') : ''}.pdf`;

      // Save PDF
      pdf.save(filename);

      toast({
        title: "Export Successful",
        description: "Your PDF has been downloaded."
      });
    } catch (error) {
      logger.error('Error exporting to PDF:', error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting to PDF. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  if (!data) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Slip Gaji Honor</DialogTitle>
        </DialogHeader>

        <div ref={printRef}>
          <HonorSalarySlip data={data} />
        </div>

        <div className="flex justify-end mt-4">
          <Button onClick={exportToPDF} disabled={isExporting}>
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? "Exporting..." : "Export to PDF"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
