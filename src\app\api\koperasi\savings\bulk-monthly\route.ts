/**
 * API Route: POST /api/koperasi/savings/bulk-monthly
 *
 * Deskripsi: Membuat transaksi simpanan bulanan secara massal (Rp 50,000 per anggota) untuk semua anggota koperasi aktif
 * Penggunaan: Fitur transaksi massal untuk monthly_contribution
 *
 * Body:
 * - date: <PERSON>gal simpanan (string, format: YYYY-MM-DD)
 * - notes: Catatan (string, opsional)
 *
 * Response:
 * - 201: Transaksi massal berhasil dibuat
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { db } from '@/lib/db';

// Use the shared Prisma client instance
const prisma = db;

export async function POST(request: Request) {
  try {
    // Verify admin access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    // Only ADMIN and OPERATOR_KOP can perform bulk transactions
    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP') {
      return NextResponse.json({ error: 'Only administrators and koperasi operators can perform bulk transactions' }, { status: 403 });
    }

    const data = await request.json();
    const { date, notes } = data;

    // Validate required fields
    if (!date) {
      return NextResponse.json(
        { error: 'Date is required' },
        { status: 400 }
      );
    }

    // Get all active members
    const activeMembers = await prisma.koperasiMember.findMany({
      where: {
        status: 'active'
      }
    });

    if (activeMembers.length === 0) {
      return NextResponse.json(
        { error: 'No active members found' },
        { status: 400 }
      );
    }

    console.log(`Processing bulk monthly contribution (Rp 50,000 per member) for ${activeMembers.length} active members`);

    // Create transactions in a database transaction
    const results = await prisma.$transaction(async (tx) => {
      const transactions = [];
      const monthNames = [
        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
      ];

      const transactionDate = new Date(date);
      const monthName = monthNames[transactionDate.getMonth()];
      const year = transactionDate.getFullYear();
      const defaultNote = `Iuran Wajib Bulan ${monthName} ${year} (Rp 50,000)`;

      // Variabel untuk melacak statistik
      let processedCount = 0;
      let skippedCount = 0;

      for (const member of activeMembers) {
        // Verifikasi ulang status member untuk memastikan masih aktif
        const currentMember = await tx.koperasiMember.findUnique({
          where: { id: member.id },
          select: {
            status: true,
            oneTimeContribution: true,
            monthlyContribution: true,
            optionalContribution: true
          }
        });

        // Skip member yang tidak aktif
        if (!currentMember || currentMember.status !== 'active') {
          console.log(`Skipping member ID ${member.id}: not active`);
          skippedCount++;
          continue;
        }

        // Tetapkan jumlah kontribusi bulanan ke 50,000 untuk semua member
        const monthlyContributionAmount = 50000; // Fixed amount for all members
        let newOneTimeContribution = Number(currentMember?.oneTimeContribution || 0);
        let newMonthlyContribution = Number(currentMember?.monthlyContribution || 0) + monthlyContributionAmount;
        let newOptionalContribution = Number(currentMember?.optionalContribution || 0);

        // Calculate new total savings as sum of all contributions
        const newTotalSavings = newOneTimeContribution + newMonthlyContribution + newOptionalContribution;

        // Create saving record
        const transaction = await tx.koperasiSaving.create({
          data: {
            member: { connect: { id: member.id } },
            amount: monthlyContributionAmount,
            type: 'deposit',
            contributionType: 'monthly',
            date: new Date(date),
            notes: notes || defaultNote
          }
        });

        // Update member's contributions
        await tx.koperasiMember.update({
          where: { id: member.id },
          data: {
            monthlyContribution: newMonthlyContribution,
            totalSavings: newTotalSavings,
            updatedAt: new Date()
          }
        });

        transactions.push(transaction);
        processedCount++;
      }

      return {
        transactions,
        processedCount,
        skippedCount
      };
    });

    return NextResponse.json({
      message: `Successfully created ${results.transactions.length} monthly contribution transactions of Rp 50,000 each (${results.skippedCount} members skipped)`,
      count: results.transactions.length,
      processed: results.processedCount,
      skipped: results.skippedCount
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating bulk monthly contributions:', error);
    return NextResponse.json(
      { error: 'Failed to create bulk monthly contributions' },
      { status: 500 }
    );
  }
}
