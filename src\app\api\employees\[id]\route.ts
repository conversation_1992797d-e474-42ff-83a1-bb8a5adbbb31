/**
 * API Route: /api/employees/[id]
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Extract ID from URL path
    const id = parseInt(request.nextUrl.pathname.split('/')[3] || '0');

    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        department: true,
        position: true,
        user: true
      }
    });

    if (!employee || employee.isDeleted) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(employee);
  } catch (error: any) {
    console.error('API - Error fetching employee:', error);
    return NextResponse.json(
      { error: 'Failed to fetch employee' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Extract ID from URL path
    const id = parseInt(request.nextUrl.pathname.split('/')[3] || '0');
    const data = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid employee ID' },
        { status: 400 }
      );
    }

    // Cek apakah employee ada dan tidak di-soft-delete
    const existingEmployee = await prisma.employee.findUnique({
      where: { id }
    });

    if (!existingEmployee || existingEmployee.isDeleted) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Convert departmentId and positionId to integers
    const departmentId = data.departmentId ? parseInt(data.departmentId) : undefined;
    const positionId = data.positionId ? parseInt(data.positionId) : undefined;

    const updatedEmployee = await prisma.employee.update({
      where: { id },
      data: {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        birthDate: data.birthDate ? new Date(data.birthDate) : undefined,
        birthPlace: data.birthPlace || undefined,
        gender: data.gender,
        religion: data.religion || undefined,
        maritalStatus: data.maritalStatus,
        educationLevel: data.educationLevel || undefined,
        educationMajor: data.educationMajor || undefined,
        educationInstitution: data.educationInstitution || undefined,
        identityType: data.identityType || undefined,
        identityNumber: data.identityNumber || undefined,
        npwp: data.npwp || undefined,
        bpjsKesehatan: data.bpjsKesehatan || undefined,
        bpjsKetenagakerjaan: data.bpjsKetenagakerjaan || undefined,
        bankName: data.bankName || undefined,
        bankAccount: data.bankAccount || undefined,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        country: data.country,
        emergencyContactName: data.emergencyContactName,
        emergencyContactPhone: data.emergencyContactPhone,
        departmentId: departmentId, // Use the converted integer
        positionId: positionId, // Use the converted integer
        status: data.status || undefined,
        contractType: data.contractType || undefined,
      },
      include: {
        department: {
          select: {
            name: true,
          },
        },
        position: {
          select: {
            title: true,
          },
        },
        user: {
          select: {
            username: true,
            role: true
          }
        }
      },
    });

    return NextResponse.json(updatedEmployee);
  } catch (error: any) {
    console.error('API - Error updating employee:', error);
    return NextResponse.json(
      { error: 'Failed to update employee' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Extract ID from URL path
    const id = request.nextUrl.pathname.split('/')[3] || '0';
    // Redirect ke endpoint DELETE yang sebenarnya
    const response = await fetch(new URL(`/api/employees/${id}/delete`, request.url), {
      method: 'DELETE',
      headers: request.headers
    });

    return response;
  } catch (error: any) {
    console.error('Error in DELETE /api/employees/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
