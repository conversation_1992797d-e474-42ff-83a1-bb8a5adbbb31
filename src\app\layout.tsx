import type { Metadata, Viewport } from "next";
import "./globals.css";
import { AuthProvider } from "@/lib/auth";
import { ThemeProvider } from "@/components/theme-provider";
import { DatabaseStatus } from "@/components/DatabaseStatus";
import { Toaster } from "@/components/ui/toaster";
import { AdsterraNavTrigger } from "@/components/AdsterraNavTrigger";

export const metadata: Metadata = {
  title: "Employee Management System",
  description: "A comprehensive employee management system for organizations",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "EMS"
  },
  manifest: "/manifest.json"
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#ffffff"
};

type RootLayoutProps = {
  children: React.ReactNode;
};

export default function RootLayout({ children }: Readonly<RootLayoutProps>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="font-sans antialiased">
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            {children}
            <DatabaseStatus />
            <Toaster />
            <AdsterraNavTrigger />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}


