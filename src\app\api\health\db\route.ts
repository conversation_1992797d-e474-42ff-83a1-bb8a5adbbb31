import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { logger } from '@/lib/logger';

export async function GET() {
  try {
    // Test connection with a simple query
    await prisma.$queryRaw`SELECT 1`;

    return NextResponse.json({
      status: 'connected',
      message: 'Database connection is healthy'
    });
  } catch (error) {
    logger.error('Database health check failed:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: 'Database connection failed'
      },
      { status: 500 }
    );
  } finally {
    // Ensure we disconnect from the database
    await prisma.$disconnect();
  }
}

