interface Employee {
  id: number;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string | null;
  phone: string | null;
  address: string | null;
  birthDate: Date | null;
  birthPlace: string | null;
  gender: string | null;
  religion: string | null;
  maritalStatus: string | null;
  educationLevel: string | null;
  educationMajor: string | null;
  educationInstitution: string | null;
  joinDate: Date | null;
  status: string;
  contractType: string | null;
  identityType: string | null;
  identityNumber: string | null;
  npwp: string | null;
  bpjsKesehatan: string | null;
  bpjsKetenagakerjaan: string | null;
  bankName: string | null;
  bankAccount: string | null;
  department?: {
    id: number;
    name: string;
  };
  position?: {
    id: number;
    title: string;
  };
}
