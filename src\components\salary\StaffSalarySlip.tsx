import { SalaryStaff } from "@/lib/types";
import { format } from "date-fns";
import { id } from "date-fns/locale";

interface StaffSalarySlipProps {
  data: SalaryStaff;
}

export function StaffSalarySlip({ data }: StaffSalarySlipProps) {
  // Format currency
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return "-";

    // Handle NaN values
    if (isNaN(Number(amount))) return "-";

    // Convert to number to ensure proper formatting
    const numAmount = Number(amount);

    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numAmount);
  };

  // Format date
  const formatDate = (date: Date) => {
    return format(new Date(date), "MMMM yyyy", { locale: id });
  };

  // Format boolean or number for absensi
  const formatAbsensiValue = (value: boolean | number | null | undefined, isMonetary: boolean = false) => {
    if (value === null || value === undefined) return "-";

    // For numeric values, return "Tidak Ada" if 0
    if (typeof value === 'number') {
      // If it's explicitly marked as monetary or likely a monetary value, format as currency
      if (isMonetary || value >= 1000) {
        return formatCurrency(value);
      }
      return value === 0 ? "Tidak Ada" : value.toString();
    }

    // For boolean values
    if (typeof value === 'boolean') {
      return value ? "Ya" : "Tidak Ada";
    }

    // For any other type, convert to string
    return String(value);
  };

  return (
    <div className="p-1 px-8 bg-white text-xs">
      {/* Top separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5"></div>

      {/* Header */}
      <div className="mb-1">
        <div className="flex items-center w-full mb-0.5">
          <div className="mr-2">
            <img
              src="/images/LOGO%20Medium.jpg"
              alt="Sekolah Darma Bangsa Logo"
              className="h-8 w-auto object-contain"
            />
          </div>
          <div className="text-center flex-1">
            <h1 className="text-base font-bold">SEKOLAH DARMA BANGSA</h1>
            <h2 className="text-sm font-bold">SLIP GAJI</h2>
          </div>
        </div>
        <div className="border-t border-b border-dashed border-gray-400 py-0.5 w-full mt-1 mb-1"></div>
      </div>

      {/* Employee Info */}
      <div className="mb-1">
        <div className="flex items-start">
          <div className="w-28 min-w-[7rem] flex-shrink-0 pl-1">Periode</div>
          <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
          <div className="flex-1">{formatDate(data.period)}</div>
        </div>
        <div className="flex items-start">
          <div className="w-28 min-w-[7rem] flex-shrink-0 pl-1">Nama Karyawan</div>
          <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
          <div className="flex-1">{data.nama}</div>
        </div>
      </div>
      <div className="border-t border-b border-dashed border-gray-400 py-0.5"></div>

      {/* Two column layout for main content */}
      <div className="flex w-full">
        {/* Left Column */}
        <div className="w-1/2 pr-3 border-r border-dashed border-green-500" style={{ borderRightWidth: '1px', minWidth: '50%' }}>
          {/* Gaji Pokok */}
          <div className="border-b border-dashed border-gray-400 py-0.5">
            <div className="flex items-start">
              <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Gaji Pokok</div>
              <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
              <div className="flex-1 pr-2">{formatCurrency(data.gp)}</div>
            </div>
          </div>
          <div className="border-b border-dashed border-gray-400 py-0.5"></div>

          {/* Tunjangan */}
          <div className="border-b border-dashed border-gray-400 py-0.5">
            <div className="pl-1">Tunjangan</div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-2">Golongan</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-4">Kontrak</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1 pr-2">{formatCurrency(data.gol_kontrak)}</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-4">Tetap</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.gol_tetap)}</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-2">Beban Kerja</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.beban_kerja)}</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-2">Keahlian</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.tunj_keahlian)}</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-2">Jabatan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.tunj_jab)}</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-2">Insentif</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.insentif)}</div>
            </div>
          </div>
          <div className="border-b border-dashed border-gray-400 py-0.5"></div>

          {/* Total Gaji Pokok + Tunjangan */}
          <div className="border-b border-dashed border-gray-400 py-0.5">
            <div className="flex items-start">
              <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Total Gaji Pokok + Tunjangan</div>
              <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
              <div className="flex-1">{formatCurrency(data.tot_gross)}</div>
            </div>
          </div>
          <div className="border-b border-dashed border-gray-400 py-0.5"></div>

          {/* Iuran Yayasan */}
          <div className="border-b border-dashed border-gray-400 py-0.5">
            <div>Iuran yang dibayarkan Yayasan (Khusus Security):</div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-4">BPJS TK</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.bpjstku_yay)}</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-4">BPJS KES</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.bpjskes_yay)}</div>
            </div>
            <div className="flex">
              <div className="w-36 min-w-[9rem] pl-4">PPh21</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.pph_yay)}</div>
            </div>
          </div>
          <div className="border-b border-dashed border-gray-400 py-0.5"></div>

          {/* Total Gaji Pokok + Tunjangan Yayasan */}
          <div className="border-b border-dashed border-gray-400 py-0.5">
            <div className="flex items-start">
              <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Total Gaji Pokok + Tunjangan</div>
              <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
              <div className="flex-1">{formatCurrency(data.tot_gross_yay)}</div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-1/2 pl-3" style={{ minWidth: '50%' }}>
          <div className="border-b border-dashed border-gray-400 py-0.5">
            <div className="pl-1">Potongan</div>
            <div className="pl-4">Absensi</div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Tanpa Keterangan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatAbsensiValue(data.awol)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Nominal Potongan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatAbsensiValue(data.jlh_awol, true)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Terlambat Frequensi</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatAbsensiValue(data.freq)} kali</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Nominal Potongan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatAbsensiValue(data.jlh_freq, true)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Terlambat Menit</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatAbsensiValue(data.minute)} menit</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Nominal Potongan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatAbsensiValue(data.jlh_minute, true)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Total Potongan Absensi</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.pot_abs)}</div>
            </div>

            <div className="pl-4 mt-1">BPJS</div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">BPJS Ketenagakerjaan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.bpjstku)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">BPJS Kesehatan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.bpjskes)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Total Potongan BPJS</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(Number(data.bpjstku || 0) + Number(data.bpjskes || 0))}</div>
            </div>

            <div className="pl-4 mt-1">Lain-lain</div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">PPh21</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.pph)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Pinjaman lain (bila ada)</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.pinj_lain)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Koperasi</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-12">Iuran Wajib</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.iuran_wajib)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-12">Pinjaman Koperasi</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.pinj_kop)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Potongan Bank</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.pot_bank)}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Total Potongan Lain-lain</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(Number(data.pph || 0) + Number(data.pinj_lain || 0) + Number(data.iuran_wajib || 0) + Number(data.pinj_kop || 0) + Number(data.pot_bank || 0))}</div>
            </div>
            <div className="flex">
              <div className="w-44 min-w-[11rem] pl-8">Total Potongan</div>
              <div className="w-6 min-w-[1.5rem] text-center">:</div>
              <div className="flex-1">{formatCurrency(data.tot_pot)}</div>
            </div>
          </div>
          <div className="border-b border-dashed border-gray-400 py-0.5"></div>
        </div>
      </div>

      {/* Footer - Piutang and Gaji Nett */}
      <div className="border-t border-b border-dashed border-gray-400 py-0.5 mt-1">
        <div className="flex items-start">
          <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Piutang</div>
          <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
          <div className="flex-1">{formatCurrency(data.piutang)}</div>
        </div>
      </div>

      <div className="border-t border-b border-dashed border-gray-400 py-0.5 mt-1">
        <div className="flex items-start font-bold">
          <div className="w-36 min-w-[9rem] flex-shrink-0 pl-1">Gaji Nett</div>
          <div className="w-6 min-w-[1.5rem] flex-shrink-0 text-center">:</div>
          <div className="flex-1">{formatCurrency(data.gaji_netto)}</div>
        </div>
      </div>

      {/* Bottom separator line */}
      <div className="border-t border-dashed border-gray-400 py-0.5 mt-1"></div>

      {/* Footer */}
      <div className="text-[7px] text-right italic text-gray-500 mt-1 pr-2">
        Dicetak secara elektronik pada {new Date().getDate()}/{new Date().getMonth() + 1}/{new Date().getFullYear()} sehingga tidak membutuhkan tanda tangan. Untuk legalitas perlu ditambahkan cap basah Yayasan
      </div>
    </div>
  );
}
