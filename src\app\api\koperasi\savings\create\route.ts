/**
 * API Route: POST /api/koperasi/savings/create
 *
 * Deskripsi: Membuat transaksi simpanan koperasi baru
 * Penggunaan: Form tambah simpanan koperasi
 *
 * Body:
 * - memberId: ID anggota koperasi (number)
 * - amount: <PERSON><PERSON><PERSON> simpanan (number)
 * - type: <PERSON><PERSON><PERSON> simpanan ('deposit' atau 'withdrawal')
 * - date: Tanggal simpanan (string, format: YYYY-MM-DD)
 * - notes: <PERSON>atan (string, opsional)
 *
 * Response:
 * - 201: Simpanan koperasi berhasil dibuat
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Anggota koperasi tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';

// Use the shared Prisma client instance
const prisma = db;

export async function POST(request: Request) {
  try {
    // Verify user access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;
    const userEmployeeId = userData.employeeId;

    if (!['ADMIN', 'SUPERVISOR', 'EMPLOYEE', 'OPERATOR_KOP'].includes(userRole)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { memberId, amount, type, date, notes, contributionType } = data;

    logger.debug('Processing savings transaction request');

    // Pastikan memberId adalah string dan bisa dikonversi ke number
    if (!memberId || isNaN(Number(memberId))) {
      return NextResponse.json({ error: 'Invalid member ID' }, { status: 400 });
    }

    // Get the member data to check if the user is authorized to create a transaction for this member
    const memberData = await prisma.koperasiMember.findUnique({
      where: { id: parseInt(memberId.toString()) },
      select: { employeeId: true }
    });

    // If not ADMIN or OPERATOR_KOP, check if the user is creating a transaction for themselves
    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP' && memberData && memberData.employeeId !== userEmployeeId) {
      return NextResponse.json({ error: 'You can only create transactions for yourself' }, { status: 403 });
    }

    // Validate required fields
    if (!memberId || !amount || !type || !date) {
      return NextResponse.json(
        { error: 'Member ID, amount, type, and date are required' },
        { status: 400 }
      );
    }

    // Validate contributionType for deposits
    if (type === 'deposit' && !contributionType) {
      return NextResponse.json(
        { error: 'Contribution type is required for deposits' },
        { status: 400 }
      );
    }

    // Validate type
    if (!['deposit', 'withdrawal'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be either deposit or withdrawal' },
        { status: 400 }
      );
    }

    // Validate amount
    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      return NextResponse.json(
        { error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // Check if member exists
    const member = await prisma.koperasiMember.findUnique({
      where: { id: parseInt(memberId.toString()) }
    });

    if (!member) {
      return NextResponse.json({ error: 'Koperasi member not found' }, { status: 404 });
    }

    // For withdrawals, check if member has enough optional_contribution
    if (type === 'withdrawal') {
      const optionalContribution = Number(member.optionalContribution) || 0;
      if (amountValue > optionalContribution) {
        return NextResponse.json(
          { error: 'Insufficient optional contribution for withdrawal. Withdrawals can only be made from optional contribution.' },
          { status: 400 }
        );
      }
    }

    logger.debug('Creating saving transaction');

    // Create transaction in a database transaction
    try {
      const result = await prisma.$transaction(async (tx) => {
        // Create saving record
        logger.debug('Processing database transaction');
        const saving = await tx.koperasiSaving.create({
          data: {
            member: { connect: { id: parseInt(memberId.toString()) } },
            amount: amountValue,
            type,
            contributionType: type === 'deposit' ? contributionType : null,
            date: new Date(date),
            notes: notes || null
          }
        });

      // Get current member data to calculate new total savings
      const currentMember = await tx.koperasiMember.findUnique({
        where: { id: parseInt(memberId.toString()) },
        select: {
          oneTimeContribution: true,
          monthlyContribution: true,
          optionalContribution: true
        }
      });

      // Update member's contributions based on transaction type
      if (type === 'deposit') {
        // For deposits, update the selected contribution type
        const updateData: any = {
          updatedAt: new Date()
        };

        // Calculate new contribution values
        let newOneTimeContribution = Number(currentMember?.oneTimeContribution || 0);
        let newMonthlyContribution = Number(currentMember?.monthlyContribution || 0);
        let newOptionalContribution = Number(currentMember?.optionalContribution || 0);

        // Update the specific contribution type based on selection
        if (contributionType === 'one_time') {
          newOneTimeContribution += amountValue;
          updateData.oneTimeContribution = newOneTimeContribution;
        } else if (contributionType === 'monthly') {
          newMonthlyContribution += amountValue;
          updateData.monthlyContribution = newMonthlyContribution;
        } else if (contributionType === 'optional') {
          newOptionalContribution += amountValue;
          updateData.optionalContribution = newOptionalContribution;
        }

        // Calculate new total savings as sum of all contributions
        const newTotalSavings = newOneTimeContribution + newMonthlyContribution + newOptionalContribution;
        updateData.totalSavings = newTotalSavings;

        logger.debug('Processing deposit transaction');

        await tx.koperasiMember.update({
          where: { id: parseInt(memberId.toString()) },
          data: updateData
        });
      } else {
        // For withdrawals, update optional_contribution
        // Calculate new contribution values
        let newOneTimeContribution = Number(currentMember?.oneTimeContribution || 0);
        let newMonthlyContribution = Number(currentMember?.monthlyContribution || 0);
        let newOptionalContribution = Number(currentMember?.optionalContribution || 0) - amountValue;

        // Calculate new total savings as sum of all contributions
        const newTotalSavings = newOneTimeContribution + newMonthlyContribution + newOptionalContribution;

        logger.debug('Processing withdrawal transaction');

        await tx.koperasiMember.update({
          where: { id: parseInt(memberId.toString()) },
          data: {
            // Decrease optional_contribution by the amount
            optionalContribution: newOptionalContribution,
            // Update total savings to the new calculated value
            totalSavings: newTotalSavings,
            updatedAt: new Date()
          }
        });
      }

        return saving;
      });

      return NextResponse.json(result, { status: 201 });
    } catch (error) {
      logger.error('Database transaction error');
      throw error;
    }
  } catch (error) {
    logger.error('Error creating koperasi saving');
    console.error('Error details:', error);
    return NextResponse.json(
      { error: 'Failed to create koperasi saving' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
