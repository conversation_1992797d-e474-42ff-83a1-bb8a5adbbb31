"use client";

import React, { useState, useEffect } from "react";
import Navbar from "@/components/layout/Navbar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  CalendarIcon,
  Clock,
  FileText,
  Plus,
  Filter,
  Download,
  MoreHorizontal,
  Eye,
  Pencil,
  Trash2,
  Check,
  X,
  Edit
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { addDays } from "date-fns";
import { useAuth } from "@/lib/auth";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useToast } from "@/components/ui/use-toast";
import { Toast } from "@/components/ui/toast";
import { LeaveTypeForm } from "@/components/forms/leave-type-form";
import { LeaveType } from "@/types/leave";

// Component untuk menampilkan Leave Types (hanya untuk ADMIN)
const LeaveTypesSection = () => {
  // ... kode yang sudah ada ...
  return (
    <div>
      {/* ... kode yang sudah ada ... */}
    </div>
  );
};

export default function LeaveManagementPage() {
  const { user } = useAuth();
  const userRole = user?.role;
  const { toast } = useToast();

  // State untuk data
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [leaveRequests, setLeaveRequests] = useState<any[]>([]);
  const [lateRequests, setLateRequests] = useState<any[]>([]);
  const [exitRequests, setExitRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [leaveTypes, setLeaveTypes] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);

  // State untuk statistik
  const [stats, setStats] = useState({
    pendingLeaveRequests: 0,
    pendingLateRequests: 0,
    pendingExitRequests: 0
  });

  // State untuk form leave request
  const [leaveRequestForm, setLeaveRequestForm] = useState({
    employeeId: '',
    leaveTypeId: '',
    startDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    endDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    reason: ''
  });

  // State untuk dialog leave request
  const [isAddLeaveRequestOpen, setIsAddLeaveRequestOpen] = useState(false);
  const [selectedLeaveRequest, setSelectedLeaveRequest] = useState<any>(null);
  const [isViewLeaveRequestOpen, setIsViewLeaveRequestOpen] = useState(false);
  const [isEditLeaveRequestOpen, setIsEditLeaveRequestOpen] = useState(false);

  // State untuk form edit leave request
  const [editLeaveForm, setEditLeaveForm] = useState({
    id: '',
    leaveTypeId: '',
    startDate: '',
    endDate: '',
    reason: ''
  });

  // State untuk form late request
  const [lateRequestForm, setLateRequestForm] = useState({
    employeeId: '',
    lateType: '',
    lateDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    estimatedTime: '08:00', // Set default ke jam 8 pagi
    reason: ''
  });

  // State untuk dialog late request
  const [isAddLateRequestOpen, setIsAddLateRequestOpen] = useState(false);
  const [selectedLateRequest, setSelectedLateRequest] = useState<any>(null);
  const [isViewLateRequestOpen, setIsViewLateRequestOpen] = useState(false);
  const [isEditLateRequestOpen, setIsEditLateRequestOpen] = useState(false);

  // State untuk form edit late request
  const [editLateForm, setEditLateForm] = useState({
    id: '',
    lateType: '',
    lateDate: '',
    estimatedTime: '',
    reason: ''
  });

  // State untuk form exit request
  const [exitRequestForm, setExitRequestForm] = useState({
    employeeId: '',
    exitType: '',
    exitDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    exitTime: '08:00', // Set default ke jam 8 pagi
    comebackTime: '16:00', // Set default ke jam 4 sore
    notComeback: false,
    reason: ''
  });

  // State untuk dialog exit request
  const [isAddExitRequestOpen, setIsAddExitRequestOpen] = useState(false);
  const [selectedExitRequest, setSelectedExitRequest] = useState<any>(null);
  const [isViewExitRequestOpen, setIsViewExitRequestOpen] = useState(false);
  const [isEditExitRequestOpen, setIsEditExitRequestOpen] = useState(false);

  // State untuk form edit exit request
  const [editExitForm, setEditExitForm] = useState({
    id: '',
    exitType: '',
    exitDate: '',
    exitTime: '',
    comebackTime: '',
    notComeback: false,
    reason: ''
  });

  // State untuk calendar
  const [leaveEvents, setLeaveEvents] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fungsi-fungsi untuk mengambil data, menangani form, dll.
  // ... kode yang sudah ada ...

  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={userRole} />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Leave Management</h1>
          <p className="text-muted-foreground mt-2">
            Track and manage employee leave requests
          </p>
          {userRole === 'SUPERVISOR' && (
            <div className="flex gap-4 mt-4 bg-card p-4 rounded-lg shadow-sm">
              <div className="text-sm">
                <span className="font-medium">Pending Leave Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingLeaveRequests}
                </span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Pending Late Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingLateRequests}
                </span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Pending Exit Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingExitRequests}
                </span>
              </div>
            </div>
          )}
        </div>

        <Tabs defaultValue="leave-requests" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="leave-requests">Leave Requests</TabsTrigger>
            <TabsTrigger value="late">Late Requests</TabsTrigger>
            <TabsTrigger value="exit">Exit From School</TabsTrigger>
            <TabsTrigger value="calendar">Calendar</TabsTrigger>
            <TabsTrigger value="leave-types">Leave Types</TabsTrigger>
          </TabsList>

          <TabsContent value="leave-requests" className="space-y-4">
            <div className="bg-card rounded-lg shadow-sm p-6">
              <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                <div className="p-4 flex justify-between items-center border-b border-border">
                  <div>
                    <h2 className="text-lg font-semibold">Leave Requests</h2>
                    {userRole === 'EMPLOYEE' && (
                      <p className="text-xs text-muted-foreground">Showing your leave requests</p>
                    )}
                    {userRole === 'SUPERVISOR' && (
                      <p className="text-xs text-muted-foreground">Showing leave requests from your department</p>
                    )}
                    {userRole === 'ADMIN' && (
                      <p className="text-xs text-muted-foreground">Showing all leave requests</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Dialog open={isAddLeaveRequestOpen} onOpenChange={setIsAddLeaveRequestOpen}>
                      <DialogTrigger asChild>
                        <Button className="flex items-center gap-2">
                          <Plus className="h-4 w-4" /> Add Leave Request
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        {/* ... konten dialog leave request ... */}
                      </DialogContent>
                    </Dialog>
                    {userRole === "ADMIN" && (
                      <>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                <div className="overflow-x-auto">
                  {/* ... tabel leave requests ... */}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="late" className="space-y-4">
            <div className="bg-card rounded-lg shadow-sm p-6">
              <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                <div className="p-4 flex justify-between items-center border-b border-border">
                  <div>
                    <h2 className="text-lg font-semibold">Late Requests</h2>
                    {userRole === 'EMPLOYEE' && (
                      <p className="text-xs text-muted-foreground">Showing your late requests</p>
                    )}
                    {userRole === 'SUPERVISOR' && (
                      <p className="text-xs text-muted-foreground">Showing late requests from your department</p>
                    )}
                    {userRole === 'ADMIN' && (
                      <p className="text-xs text-muted-foreground">Showing all late requests</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Dialog open={isAddLateRequestOpen} onOpenChange={setIsAddLateRequestOpen}>
                      <DialogTrigger asChild>
                        <Button className="flex items-center gap-2">
                          <Plus className="h-4 w-4" /> Add Late Request
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        {/* ... konten dialog late request ... */}
                      </DialogContent>
                    </Dialog>
                    {userRole === "ADMIN" && (
                      <>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                <div className="overflow-x-auto">
                  {/* ... tabel late requests ... */}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="exit" className="space-y-4">
            <div className="bg-card rounded-lg shadow-sm p-6">
              <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                <div className="p-4 flex justify-between items-center border-b border-border">
                  <div>
                    <h2 className="text-lg font-semibold">Exit From School Requests</h2>
                    {userRole === 'EMPLOYEE' && (
                      <p className="text-xs text-muted-foreground">Showing your exit from school requests</p>
                    )}
                    {userRole === 'SUPERVISOR' && (
                      <p className="text-xs text-muted-foreground">Showing exit from school requests from your department</p>
                    )}
                    {userRole === 'ADMIN' && (
                      <p className="text-xs text-muted-foreground">Showing all exit from school requests</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Dialog open={isAddExitRequestOpen} onOpenChange={setIsAddExitRequestOpen}>
                      <DialogTrigger asChild>
                        <Button className="flex items-center gap-2">
                          <Plus className="h-4 w-4" /> Add Exit Request
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        {/* ... konten dialog exit request ... */}
                      </DialogContent>
                    </Dialog>
                    {userRole === "ADMIN" && (
                      <>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                <div className="overflow-x-auto">
                  {/* ... tabel exit requests ... */}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-4">
            {/* ... konten tab calendar ... */}
          </TabsContent>

          {userRole === "ADMIN" && (
            <TabsContent value="leave-types" className="space-y-4">
              <LeaveTypesSection />
            </TabsContent>
          )}
        </Tabs>
      </main>
    </div>
  );
}
