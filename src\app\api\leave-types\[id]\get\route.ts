/**
 * API Route: GET /api/leave-types/[id]/get
 *
 * Deskripsi: Mengambil detail tipe cuti berdasarkan ID
 * Penggunaan: Halaman detail tipe cuti, form edit tipe cuti
 *
 * Path Parameters:
 * - id: ID tipe cuti (number)
 *
 * Response:
 * - 200: Detail tipe cuti
 * - 401: Tidak terautentikasi
 * - 404: Tipe cuti tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function GET(request: Request) {
  // Get the ID from the URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 2]; // Get the ID from the URL path

  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const leaveType = await prisma.leaveType.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: { leaveRequests: true }
        }
      }
    });

    if (!leaveType) {
      return NextResponse.json({ error: 'Leave type not found' }, { status: 404 });
    }

    return NextResponse.json(leaveType);
  } catch (error) {
    console.error('Error fetching leave type:', error);
    return NextResponse.json(
      { error: 'Failed to fetch leave type' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
