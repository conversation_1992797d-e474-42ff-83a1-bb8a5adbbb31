import { <PERSON><PERSON><PERSON><PERSON> } from "@/lib/types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "../ui/dialog";
import { <PERSON><PERSON> } from "../ui/button";
import { Guru<PERSON>alarySlip } from "./GuruSalarySlip";
import { useRef, useState } from "react";
import { Download } from "lucide-react";
import { toast } from "../ui/use-toast";
// Note: You'll need to install these packages:
// npm install jspdf html2canvas
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface GuruSalarySlipDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  data: SalaryGuru | null;
}

export function GuruSalarySlipDialog({
  open,
  onOpenChange,
  data,
}: GuruSalarySlipDialogProps) {
  const printRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);

  const exportToPDF = async () => {
    if (!printRef.current) return;

    setIsExporting(true);
    try {
      // Notify user that export is starting
      toast({
        title: "Exporting to PDF",
        description: "Please wait while we generate your PDF..."
      });

      const element = printRef.current;
      const canvas = await html2canvas(element, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff'
      });

      const imgData = canvas.toDataURL('image/png');

      // F4 dimensions (215 x 330 mm in portrait mode)
      const pageWidth = 215; // F4 width in mm
      const pageHeight = 330; // F4 full height in mm

      // Calculate image dimensions to fit F4 page while maintaining aspect ratio
      const imgWidth = pageWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: [pageWidth, pageHeight]
      });
      let position = 0;

      // Add image to PDF with margins
      const leftMargin = 15; // 15mm left margin
      const topMargin = 15;  // 15mm top margin

      // Calculate scale to fit content properly
      const contentWidth = imgWidth - (leftMargin * 2);
      const contentHeight = Math.min(imgHeight, pageHeight - (topMargin * 2));

      pdf.addImage(imgData, 'PNG', leftMargin, topMargin, contentWidth, contentHeight);

      // Generate filename
      const filename = `Slip_Gaji_${data?.nama.replace(/\s+/g, '_') || 'Guru'}_${data?.period ? new Date(data.period).toLocaleDateString('id-ID', { month: 'long', year: 'numeric' }).replace(/\s+/g, '_') : ''}.pdf`;

      // Save PDF
      pdf.save(filename);

      toast({
        title: "Export Successful",
        description: "Your PDF has been downloaded."
      });
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting to PDF. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  if (!data) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Slip Gaji Guru</DialogTitle>
        </DialogHeader>

        <div ref={printRef}>
          <GuruSalarySlip data={data} />
        </div>

        <div className="flex justify-end mt-4">
          <Button onClick={exportToPDF} disabled={isExporting}>
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? "Exporting..." : "Export to PDF"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
