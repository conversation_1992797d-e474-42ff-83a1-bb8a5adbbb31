/**
 * API Route: /api/koperasi/members
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { logger } from '@/lib/logger';



export async function GET(request: Request) {
  // Ambil parameter query dari URL
  const { searchParams } = new URL(request.url);
  const employeeId = searchParams.get('employeeId');
  try {
    // Buat filter berdasarkan employeeId jika ada
    // Tambahkan log untuk debugging (tanpa menampilkan data sensitif)
    logger.debug(employeeId ? 'Filtering members by employee ID' : 'Fetching all members');

    let whereClause = {};

    if (employeeId) {
      // Filter berdasarkan employeeId
      // Konversi employeeId ke number untuk filter
      const employeeIdNumber = parseInt(employeeId);
      if (!isNaN(employeeIdNumber)) {
        whereClause = {
          employeeId: employeeIdNumber
        };
        logger.debug('Using employee ID filter');
      } else {
        logger.debug('Invalid employee ID format provided');
      }
    }

    logger.debug('Executing member query with filters');

    const members = await prisma.koperasiMember.findMany({
      include: {
        employee: {
          select: {
            id: true,
            employeeId: true,
            firstName: true,
            lastName: true,
            user: {
              select: {
                username: true
              }
            }
          }
        }
      },
      where: whereClause
    });

    logger.debug(`Found ${members.length} members matching criteria`);

    const formattedMembers = members.map(member => ({
      id: member.id,
      employee_id: member.employee.id,
      employee_code: member.employee.employeeId, // string employee ID dari model Employee
      employee_name: `${member.employee.firstName} ${member.employee.lastName}`,
      join_date: member.joinDate,
      one_time_contribution: member.oneTimeContribution ? Number(member.oneTimeContribution) : 0,
      monthly_contribution: Number(member.monthlyContribution),
      optional_contribution: member.optionalContribution ? Number(member.optionalContribution) : 0,
      total_savings: Number(member.totalSavings),
      status: member.status,
      notes: member.notes,
      created_at: member.createdAt,
      updated_at: member.updatedAt
    }));

    logger.debug(`Returning ${formattedMembers.length} formatted members`); // untuk debugging
    return NextResponse.json(formattedMembers);
  } catch (error) {
    logger.error('Failed to fetch koperasi members:', error);
    return NextResponse.json(
      { error: 'Failed to fetch koperasi members' },
      { status: 500 }
    );
  }
}


