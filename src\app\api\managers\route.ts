import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { Role } from '@prisma/client';

export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();
    const { employeeId, subordinateIds } = data;

    if (!employeeId) {
      return NextResponse.json(
        { error: 'Employee ID is required' },
        { status: 400 }
      );
    }

    if (!subordinateIds || !Array.isArray(subordinateIds) || subordinateIds.length === 0) {
      return NextResponse.json(
        { error: 'Subordinate IDs are required' },
        { status: 400 }
      );
    }

    // Cek apakah manager ada dan tidak di-soft-delete
    const manager = await prisma.employee.findUnique({
      where: {
        id: parseInt(employeeId),
        isDeleted: false
      }
    });

    if (!manager) {
      return NextResponse.json(
        { error: 'Manager not found or has been deleted' },
        { status: 404 }
      );
    }

    // Cek apakah semua subordinate ada dan tidak di-soft-delete
    const subordinates = await prisma.employee.findMany({
      where: {
        id: {
          in: subordinateIds.map(id => parseInt(id))
        },
        isDeleted: false
      }
    });

    if (subordinates.length !== subordinateIds.length) {
      return NextResponse.json(
        { error: 'One or more subordinates not found or have been deleted' },
        { status: 404 }
      );
    }

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update role menjadi manager
      await tx.user.update({
        where: {
          employeeId: parseInt(employeeId)
        },
        data: {
          role: Role.SUPERVISOR
        }
      });

      // Update managerId untuk semua subordinate
      await tx.employee.updateMany({
        where: {
          id: {
            in: subordinateIds.map(id => parseInt(id))
          }
        },
        data: {
          managerId: parseInt(employeeId)
        }
      });

      // Ambil data manager dengan subordinates untuk response
      const managerWithSubordinates = await tx.employee.findUnique({
        where: {
          id: parseInt(employeeId)
        },
        include: {
          subordinates: {
            where: {
              isDeleted: false // Mengecualikan subordinates yang sudah di-soft-delete
            },
            include: {
              position: true,
              department: true
            }
          },
          position: true,
          department: true,
          user: {
            select: {
              role: true
            }
          }
        }
      });

      return managerWithSubordinates;
    });

    return NextResponse.json({
      message: 'Manager and subordinates updated successfully',
      data: result
    });

  } catch (error) {
    console.error('Error updating manager and subordinates:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update manager and subordinates' },
      { status: 500 }
    );
  }
}

// GET endpoint untuk mengambil semua manager dengan subordinates mereka
export async function GET() {
  try {
    const managers = await prisma.employee.findMany({
      where: {
        user: {
          role: Role.SUPERVISOR
        },
        isDeleted: false // Mengecualikan manager yang sudah di-soft-delete
      },
      include: {
        subordinates: {
          where: {
            isDeleted: false // Mengecualikan subordinates yang sudah di-soft-delete
          },
          include: {
            position: true,
            department: true
          }
        },
        position: true,
        department: true,
        user: {
          select: {
            role: true
          }
        }
      }
    });

    return NextResponse.json(managers);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch managers' },
      { status: 500 }
    );
  }
}

