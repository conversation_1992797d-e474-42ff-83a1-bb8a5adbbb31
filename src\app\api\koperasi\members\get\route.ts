/**
 * API Route: GET /api/koperasi/members/get
 *
 * Deskripsi: Mengambil daftar anggota koperasi
 * Penggunaan: Halaman daftar anggota koperasi
 *
 * Response:
 * - 200: Daftar anggota koperasi
 * - 401: Tidak terautentikasi
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { logger } from '@/lib/logger';



export async function GET() {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const members = await prisma.koperasiMember.findMany({
      include: {
        employee: {
          select: {
            id: true,
            employeeId: true,
            firstName: true,
            lastName: true
          }
        }
      },
      where: {
        // Opsional: filter berdasarkan status jika diperlukan
        // status: 'active'
      }
    });

    const formattedMembers = members.map(member => ({
      id: member.id,
      employee_id: member.employee.id,
      employee_code: member.employee.employeeId, // string employee ID dari model Employee
      employee_name: `${member.employee.firstName} ${member.employee.lastName}`,
      join_date: member.joinDate,
      monthly_contribution: Number(member.monthlyContribution),
      total_savings: Number(member.totalSavings),
      status: member.status,
      created_at: member.createdAt,
      updated_at: member.updatedAt
    }));

    return NextResponse.json(formattedMembers);
  } catch (error) {
    logger.error('Error fetching koperasi members:', error);
    return NextResponse.json(
      { error: 'Failed to fetch koperasi members' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
