/**
 * API Route: /api/departments/[id]
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const id = request.nextUrl.pathname.split('/').pop() || '0';
    const data = await request.json();

    // Validate the department exists
    const existingDepartment = await prisma.department.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingDepartment) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    // Prepare update data
    const updateData: { name: string; description: string; headId?: number | null } = {
      name: data.name,
      description: data.description
    };

    // Only include headId if it's not "none"
    if (data.head && data.head !== 'none') {
      updateData.headId = parseInt(data.head);
    } else {
      updateData.headId = null;
    }

    const updatedDepartment = await prisma.department.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        head: {
          select: {
            firstName: true,
            lastName: true,
          }
        }
      }
    });

    // Transform the response
    const transformedDepartment = {
      id: updatedDepartment.id,
      name: updatedDepartment.name,
      description: updatedDepartment.description,
      headId: updatedDepartment.headId,
      headName: updatedDepartment.head
        ? `${updatedDepartment.head.firstName} ${updatedDepartment.head.lastName}`.trim()
        : null
    };

    return NextResponse.json(transformedDepartment);
  } catch (error) {
    console.error('Error updating department:', error);
    return NextResponse.json(
      { error: 'Failed to update department' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// Add DELETE handler
export async function DELETE(_request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const id = _request.nextUrl.pathname.split('/').pop() || '0';

    // Check if department has employees
    const departmentWithEmployees = await prisma.department.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: { employees: true }
        }
      }
    });

    if (!departmentWithEmployees) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    if (departmentWithEmployees._count.employees > 0) {
      return NextResponse.json(
        { error: 'Cannot delete department with existing employees' },
        { status: 400 }
      );
    }

    await prisma.department.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Department deleted successfully' });
  } catch (error) {
    console.error('Error deleting department:', error);
    return NextResponse.json(
      { error: 'Failed to delete department' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
