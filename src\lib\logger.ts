/**
 * Utilitas logger yang aman untuk digunakan di seluruh aplikasi
 * - Di development: menampilkan semua log
 * - Di production: menyembunyikan log sensitif, hanya menampilkan error penting
 */

// Menentukan level log
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Fungsi untuk menyamarkan data sensitif
const maskSensitiveData = (data: any): any => {
  if (typeof data === 'string') {
    // Mask email addresses
    data = data.replace(/([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9_-]+)/gi, '***@***.***');
    
    // Mask potential passwords
    data = data.replace(/password["']?\s*:\s*["']([^"']+)["']/gi, 'password:"***"');
    
    // Mask potential tokens
    data = data.replace(/(token|jwt|api[_-]?key)["']?\s*:\s*["']([^"']+)["']/gi, '$1:"***"');
    
    // Mask potential database connection strings
    data = data.replace(/(mongodb|mysql|postgres|database)(:\/\/|=)[^\s&]+/gi, '$1$2***');
    
    return data;
  } else if (data === null || data === undefined) {
    return data;
  } else if (Array.isArray(data)) {
    return data.map(item => maskSensitiveData(item));
  } else if (typeof data === 'object') {
    const masked: Record<string, any> = {};
    for (const key in data) {
      // Mask sensitive keys completely
      if (['password', 'token', 'secret', 'apiKey', 'api_key', 'key', 'jwt'].includes(key.toLowerCase())) {
        masked[key] = '***';
      } else {
        masked[key] = maskSensitiveData(data[key]);
      }
    }
    return masked;
  }
  
  return data;
};

// Fungsi untuk menentukan apakah log harus ditampilkan berdasarkan level
const shouldLog = (level: LogLevel): boolean => {
  // Di development, tampilkan semua log
  if (process.env.NODE_ENV !== 'production') {
    return true;
  }
  
  // Di production, hanya tampilkan error
  return level === 'error';
};

// Logger yang aman
export const logger = {
  debug: (message: string, ...args: any[]) => {
    if (shouldLog('debug')) {
      if (process.env.NODE_ENV === 'production') {
        // Di production, jangan log debug sama sekali
        return;
      }
      console.debug(message, ...args);
    }
  },
  
  info: (message: string, ...args: any[]) => {
    if (shouldLog('info')) {
      if (process.env.NODE_ENV === 'production') {
        // Di production, jangan log info sama sekali
        return;
      }
      console.info(message, ...args);
    }
  },
  
  warn: (message: string, ...args: any[]) => {
    if (shouldLog('warn')) {
      if (process.env.NODE_ENV === 'production') {
        // Di production, jangan log warning sama sekali
        return;
      }
      console.warn(message, ...args);
    }
  },
  
  error: (message: string, ...args: any[]) => {
    if (shouldLog('error')) {
      // Di production, samarkan data sensitif
      if (process.env.NODE_ENV === 'production') {
        const maskedArgs = args.map(arg => {
          if (arg instanceof Error) {
            // Untuk error, samarkan stack trace dan pesan
            const maskedError = new Error(maskSensitiveData(arg.message));
            if (arg.stack) {
              maskedError.stack = maskSensitiveData(arg.stack);
            }
            return maskedError;
          }
          return maskSensitiveData(arg);
        });
        
        console.error(maskSensitiveData(message), ...maskedArgs);
      } else {
        // Di development, tampilkan semua
        console.error(message, ...args);
      }
    }
  },
  
  // Fungsi khusus untuk log API yang aman
  api: (endpoint: string, method: string, statusCode?: number) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`API ${method} ${endpoint} ${statusCode ? `- Status: ${statusCode}` : ''}`);
    }
    // Di production, tidak perlu log API calls
  }
};

export default logger;
