/**
 * API Route: /api/departments
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

export async function GET() {
  try {
    // Dapatkan semua departemen dengan jumlah karyawan (tidak termasuk yang diawali EMP dan EXT)
    const departments = await prisma.department.findMany({
      include: {
        head: {
          select: {
            firstName: true,
            lastName: true,
          }
        },
        _count: {
          select: {
            employees: {
              where: {
                NOT: {
                  OR: [
                    { employeeId: { startsWith: 'EMP' } },
                    { employeeId: { startsWith: 'EXT' } }
                  ]
                },
                isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
              }
            }
          }
        },
        employees: {
          where: {
            OR: [
              { employeeId: { startsWith: 'EMP' } },
              { employeeId: { startsWith: 'EXT' } }
            ],
            isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
          },
          select: {
            id: true
          }
        }
      },
      orderBy: {
        name: 'asc',
      },
    });

    const transformedDepartments = departments.map(dept => {
      // Jumlah karyawan sudah dikurangi yang diawali EMP dan EXT dalam query
      return {
        id: dept.id,
        name: dept.name,
        description: dept.description,
        headId: dept.headId,
        headName: dept.head ? `${dept.head.firstName} ${dept.head.lastName}`.trim() : null,
        employeeCount: dept._count.employees,
        createdAt: dept.createdAt,
        updatedAt: dept.updatedAt,
      };
    });

    return NextResponse.json(transformedDepartments);
  } catch (error) {
    console.error('Error fetching departments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch departments' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = JSON.parse(userCookie.value);
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    const data = await request.json();

    const department = await prisma.department.create({
      data: {
        name: data.name,
        description: data.description,
        headId: data.head === 'none' ? null : parseInt(data.head)
      }
    });

    return NextResponse.json(department);

  } catch (error) {
    console.error('Error creating department:', error);
    return NextResponse.json(
      { error: 'Failed to create department' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}




