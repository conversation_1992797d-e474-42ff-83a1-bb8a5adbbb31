import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;
    // Get ID from params
    const id = params.id;

    // Check if late request exists
    const lateRequest = await prisma.lateRequest.findUnique({
      where: { id: parseInt(id) },
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            departmentId: true,
          },
        },
      },
    });

    if (!lateRequest) {
      return NextResponse.json({ error: 'Late request not found' }, { status: 404 });
    }

    // Admin dapat melihat semua late requests
    if (userRole === 'ADMIN') {
      return NextResponse.json(lateRequest);
    }

    // Supervisor hanya dapat melihat late request dari bawahannya
    if (userRole === 'SUPERVISOR') {
      // Ambil departemen supervisor
      const supervisor = await prisma.employee.findUnique({
        where: { id: parseInt(userData.id) },
        select: { departmentId: true },
      });

      if (!supervisor) {
        return NextResponse.json({ error: 'Supervisor not found' }, { status: 404 });
      }

      // Cek apakah karyawan adalah bawahan dari supervisor
      if (lateRequest.employee.departmentId !== supervisor.departmentId) {
        return NextResponse.json(
          { error: 'You can only view late requests from your department' },
          { status: 403 }
        );
      }

      return NextResponse.json(lateRequest);
    }

    // Employee hanya dapat melihat late request miliknya sendiri
    if (lateRequest.employeeId !== parseInt(userData.id)) {
      return NextResponse.json(
        { error: 'You can only view your own late requests' },
        { status: 403 }
      );
    }

    return NextResponse.json(lateRequest);
  } catch (error) {
    console.error('Error fetching late request:', error);
    return NextResponse.json(
      { error: 'Failed to fetch late request' },
      { status: 500 }
    );
  }
}
