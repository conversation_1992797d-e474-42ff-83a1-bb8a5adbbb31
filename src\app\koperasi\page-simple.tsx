"use client";

import { useState } from "react";
import { useAuth } from "@/lib/auth";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ProtectedRoute from "@/components/ProtectedRoute";
import Navbar from "@/components/layout/Navbar";

export default function KoperasiPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          <div className="flex flex-col space-y-8">
            <div className="flex flex-col space-y-2">
              <h1 className="text-3xl font-bold tracking-tight">Koperasi</h1>
              <p className="text-muted-foreground">
                Manage cooperative savings, loans, and memberships.
              </p>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="savings">Transaction</TabsTrigger>
                <TabsTrigger value="loans">Loans</TabsTrigger>
                <TabsTrigger value="memberships">Memberships</TabsTrigger>
              </TabsList>

              <TabsContent value="overview">
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold">Overview</h2>
                  <p>Overview content will go here.</p>
                </div>
              </TabsContent>

              <TabsContent value="savings">
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold">Transaction</h2>
                  <p>Savings content will go here.</p>
                </div>
              </TabsContent>

              <TabsContent value="loans">
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold">Loans</h2>
                  <p>Loans content will go here.</p>
                </div>
              </TabsContent>

              <TabsContent value="memberships">
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold">Memberships</h2>
                  <p>Memberships content will go here.</p>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
