import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

interface Department {
  id: number;
  name: string;
}

interface Position {
  id: number;
  title: string;
}

interface AcademicYear {
  id: number;
  ta: string;
  description: string | null;
}

interface EmployeeFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  defaultValues?: any; // Tambahkan prop defaultValues
}

const employeeSchema = z.object({
  employeeId: z.string().min(1, "Employee ID is required"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().optional(),
  hireDate: z.string(),
  departmentId: z.string().min(1, "Department is required"),
  positionId: z.string().min(1, "Position is required"),
  academicYearId: z.string().optional(),
  status: z.string().min(1, "Status is required"),
  role: z.string().min(1, "Role is required"),
});

export function EmployeeForm({ onSubmit, onCancel, defaultValues }: EmployeeFormProps) {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([]);

  const form = useForm({
    defaultValues: defaultValues || {
      employeeId: "",
      firstName: "",
      lastName: "",
      hireDate: new Date().toISOString().split('T')[0],
      departmentId: "",
      positionId: "",
      academicYearId: "",
      status: "Kontrak",
      role: "EMPLOYEE",
    },
    resolver: zodResolver(employeeSchema),
  });

  useEffect(() => {
    // Fetch departments
    fetch('/api/departments')
      .then(res => res.json())
      .then(data => {
        // Urutkan departments berdasarkan nama
        const sortedDepartments = [...data].sort((a, b) =>
          a.name.localeCompare(b.name)
        );
        setDepartments(sortedDepartments);
      })
      .catch(err => console.error('Failed to fetch departments:', err));

    // Fetch positions
    fetch('/api/positions')
      .then(res => res.json())
      .then(data => {
        // Urutkan positions berdasarkan title
        const sortedPositions = [...data].sort((a, b) =>
          a.title.localeCompare(b.title)
        );
        setPositions(sortedPositions);
      })
      .catch(err => console.error('Failed to fetch positions:', err));

    // Fetch academic years
    fetch('/api/academic-years')
      .then(res => res.json())
      .then(data => {
        // Urutkan academic years berdasarkan ta (descending)
        const sortedAcademicYears = [...data].sort((a, b) =>
          b.ta.localeCompare(a.ta)
        );
        setAcademicYears(sortedAcademicYears);
      })
      .catch(err => console.error('Failed to fetch academic years:', err));
  }, []);

  return (
    <Form {...form}>
      <ScrollArea className="h-[70vh] max-h-[500px] pr-4">
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="employeeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Employee ID *</FormLabel>
                  <FormControl>
                    <Input {...field} tabIndex={100} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name *</FormLabel>
                  <FormControl>
                    <Input {...field} tabIndex={101} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input {...field} tabIndex={102} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hireDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hire Date *</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} tabIndex={103} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="departmentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger tabIndex={104}>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <ScrollArea className="h-[200px]">
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id.toString()}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </ScrollArea>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="positionId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Position *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger tabIndex={105}>
                        <SelectValue placeholder="Select position" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <ScrollArea className="h-[200px]">
                        {positions.map((pos) => (
                          <SelectItem key={pos.id} value={pos.id.toString()}>
                            {pos.title}
                          </SelectItem>
                        ))}
                      </ScrollArea>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="academicYearId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Academic Year</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger tabIndex={106}>
                        <SelectValue placeholder="Select academic year" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <ScrollArea className="h-[200px]">
                        {academicYears.map((year) => (
                          <SelectItem key={year.id} value={year.id.toString()}>
                            {year.ta} {year.description ? `- ${year.description}` : ''}
                          </SelectItem>
                        ))}
                      </ScrollArea>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger tabIndex={107}>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Tetap">Tetap</SelectItem>
                      <SelectItem value="Kontrak">Kontrak</SelectItem>
                      <SelectItem value="Honor">Honor</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger tabIndex={108}>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ADMIN">ADMIN</SelectItem>
                      <SelectItem value="SUPERVISOR">SUPERVISOR</SelectItem>
                      <SelectItem value="EMPLOYEE">EMPLOYEE</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="p-4 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-600">Note: Fields marked with * are required. Other employee details can be added later after creating the employee.</p>
          </div>

          <div className="flex justify-end gap-4 mt-6">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">Save</Button>
          </div>
        </form>
      </ScrollArea>
    </Form>
  );
}


