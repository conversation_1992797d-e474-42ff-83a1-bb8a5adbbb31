"use client";

import React, { useState, useEffect, useMemo } from "react";
import Navbar from "@/components/layout/Navbar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  CalendarIcon,
  Clock,
  FileText,
  Plus,
  Filter,
  Download,
  MoreHorizontal,
  Eye,
  Pencil,
  Trash2,
  Check,
  X,
  Edit
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { addDays } from "date-fns";
import { useAuth } from "@/lib/auth";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useToast } from "@/components/ui/use-toast";
import { Toast } from "@/components/ui/toast";
import { LeaveTypeForm } from "@/components/forms/leave-type-form";
import { LeaveType } from "@/types/leave";

// Component untuk menampilkan Leave Types (hanya untuk ADMIN)
const LeaveTypesSection = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [leaveTypes, setLeaveTypes] = useState<any[]>([]);
  const [isAddLeaveTypeOpen, setIsAddLeaveTypeOpen] = useState(false);
  const [isEditLeaveTypeOpen, setIsEditLeaveTypeOpen] = useState(false);
  const [selectedLeaveType, setSelectedLeaveType] = useState<any>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/leave-types');
      if (!response.ok) throw new Error('Failed to fetch leave types');
      const data = await response.json();
      setLeaveTypes(data);
    } catch (error) {
      console.error('Error fetching leave types:', error);
      toast({
        title: "Error",
        description: "Failed to fetch leave types",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load leave types when component mounts
  useEffect(() => {
    fetchLeaveTypes();
  }, []);



  const handleAddLeaveType = async (data: any) => {
    try {
      const response = await fetch('/api/leave-types/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create leave type');
      }

      toast({
        title: "Success",
        description: "Leave type created successfully",
      });

      setIsAddLeaveTypeOpen(false);
      fetchLeaveTypes();
    } catch (error: any) {
      console.error('Error creating leave type:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create leave type",
        variant: "destructive",
      });
    }
  };

  const handleEditLeaveType = async (data: any) => {
    if (!selectedLeaveType) return;

    try {
      const response = await fetch(`/api/leave-types/${selectedLeaveType.id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update leave type');
      }

      toast({
        title: "Success",
        description: "Leave type updated successfully",
      });

      setIsEditLeaveTypeOpen(false);
      fetchLeaveTypes();
    } catch (error: any) {
      console.error('Error updating leave type:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update leave type",
        variant: "destructive",
      });
    }
  };

  const handleDeleteLeaveType = async () => {
    if (!selectedLeaveType) return;

    try {
      const response = await fetch(`/api/leave-types/${selectedLeaveType.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete leave type');
      }

      toast({
        title: "Success",
        description: "Leave type deleted successfully",
      });

      setIsDeleteConfirmOpen(false);
      fetchLeaveTypes();
    } catch (error: any) {
      console.error('Error deleting leave type:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete leave type",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-card rounded-lg shadow-sm p-6">
      <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
        <div className="p-4 flex justify-between items-center border-b border-border">
          <div>
            <h2 className="text-lg font-semibold">Leave Types</h2>
            <p className="text-xs text-muted-foreground">Manage leave types for the organization</p>
          </div>
          <div className="flex gap-2">
            <Dialog open={isAddLeaveTypeOpen} onOpenChange={setIsAddLeaveTypeOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" /> Add Leave Type
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Leave Type</DialogTitle>
                </DialogHeader>
                <LeaveTypeForm
                  onSubmit={handleAddLeaveType}
                  onCancel={() => setIsAddLeaveTypeOpen(false)}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="overflow-x-auto">
          {loading ? (
            <div className="text-center py-8">
              <p>Loading leave types...</p>
            </div>
          ) : leaveTypes.length === 0 ? (
            <div className="text-center py-8">
              <p>No leave types found. Create your first leave type.</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Days Allowed</TableHead>
                  <TableHead>Requires Approval</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {leaveTypes.map((leaveType) => (
                  <TableRow key={leaveType.id}>
                    <TableCell>{leaveType.name}</TableCell>
                    <TableCell>{leaveType.daysAllowed}</TableCell>
                    <TableCell>{leaveType.requiresApproval ? 'Yes' : 'No'}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedLeaveType(leaveType);
                              setIsEditLeaveTypeOpen(true);
                            }}
                          >
                            <Pencil className="h-4 w-4 mr-2" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedLeaveType(leaveType);
                              setIsDeleteConfirmOpen(true);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      {/* Edit Leave Type Dialog */}
      <Dialog open={isEditLeaveTypeOpen} onOpenChange={setIsEditLeaveTypeOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Leave Type</DialogTitle>
          </DialogHeader>
          {selectedLeaveType && (
            <LeaveTypeForm
              initialData={{
                name: selectedLeaveType.name,
                description: selectedLeaveType.description || '',
                daysAllowed: selectedLeaveType.daysAllowed,
                requiresApproval: selectedLeaveType.requiresApproval,
              }}
              onSubmit={handleEditLeaveType}
              onCancel={() => setIsEditLeaveTypeOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the leave type "{selectedLeaveType?.name}"?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteLeaveType}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default function LeaveManagementPage() {
  const { user } = useAuth();
  const userRole = user?.role;
  const { toast } = useToast();

  // State untuk data
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [leaveRequests, setLeaveRequests] = useState<any[]>([]);
  const [lateRequests, setLateRequests] = useState<any[]>([]);
  const [exitRequests, setExitRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [leaveTypes, setLeaveTypes] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);

  // State untuk statistik
  const [stats, setStats] = useState({
    pendingLeaveRequests: 0,
    pendingLateRequests: 0,
    pendingExitRequests: 0
  });

  // State untuk form leave request
  const [leaveRequestForm, setLeaveRequestForm] = useState({
    employeeId: '',
    leaveTypeId: '',
    startDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    endDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    duration: '1', // Default durasi 1 hari
    reason: ''
  });

  // State untuk file attachment
  const [attachment, setAttachment] = useState<File | null>(null);

  // State untuk calculated end date
  const [calculatedEndDate, setCalculatedEndDate] = useState<string | null>(null);

  // State untuk dialog leave request
  const [isAddLeaveRequestOpen, setIsAddLeaveRequestOpen] = useState(false);
  const [selectedLeaveRequest, setSelectedLeaveRequest] = useState<any>(null);
  const [isViewLeaveRequestOpen, setIsViewLeaveRequestOpen] = useState(false);
  const [isEditLeaveRequestOpen, setIsEditLeaveRequestOpen] = useState(false);

  // State untuk form edit leave request
  const [editLeaveForm, setEditLeaveForm] = useState({
    id: '',
    leaveTypeId: '',
    startDate: '',
    endDate: '',
    reason: ''
  });

  // State untuk form late request
  const [lateRequestForm, setLateRequestForm] = useState({
    employeeId: '',
    lateType: '',
    lateDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    estimatedTime: '08:00', // Set default ke jam 8 pagi
    reason: ''
  });

  // State untuk dialog late request
  const [isAddLateRequestOpen, setIsAddLateRequestOpen] = useState(false);
  const [selectedLateRequest, setSelectedLateRequest] = useState<any>(null);
  const [isViewLateRequestOpen, setIsViewLateRequestOpen] = useState(false);
  const [isEditLateRequestOpen, setIsEditLateRequestOpen] = useState(false);

  // State untuk form edit late request
  const [editLateForm, setEditLateForm] = useState({
    id: '',
    lateType: '',
    lateDate: '',
    estimatedTime: '',
    reason: ''
  });

  // State untuk form exit request
  const [exitRequestForm, setExitRequestForm] = useState({
    employeeId: '',
    exitType: '',
    exitDate: new Date().toISOString().split('T')[0], // Set default ke tanggal hari ini
    exitTime: '08:00', // Set default ke jam 8 pagi
    comebackTime: '16:00', // Set default ke jam 4 sore
    notComeback: false,
    reason: ''
  });

  // State untuk dialog exit request
  const [isAddExitRequestOpen, setIsAddExitRequestOpen] = useState(false);
  const [selectedExitRequest, setSelectedExitRequest] = useState<any>(null);
  const [isViewExitRequestOpen, setIsViewExitRequestOpen] = useState(false);
  const [isEditExitRequestOpen, setIsEditExitRequestOpen] = useState(false);

  // State untuk form edit exit request
  const [editExitForm, setEditExitForm] = useState({
    id: '',
    exitType: '',
    exitDate: '',
    exitTime: '',
    comebackTime: '',
    notComeback: false,
    reason: ''
  });

  // State untuk calendar
  const [leaveEvents, setLeaveEvents] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Filter late requests berdasarkan departemen yang dikepalai oleh supervisor
  const filteredLateRequests = useMemo(() => {
    let filtered = lateRequests;

    if (userRole === 'SUPERVISOR' && user) {
      console.log('Filtering late requests for SUPERVISOR:', user.username);
      console.log('Available late requests before filter:', lateRequests.length);

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      filtered = lateRequests.filter(request => {
        // Cek apakah request.employee.department.head.employeeId === user.username
        const isEmployeeUnderSupervisor =
          request.employee?.department?.head?.employeeId === user.username;

        if (isEmployeeUnderSupervisor) {
          console.log('Found late request under supervisor:',
            `${request.employee.firstName} ${request.employee.lastName} in ${request.employee.department?.name}`);
        }

        return isEmployeeUnderSupervisor ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user.name);
      });

      console.log('Late requests after filter:', filtered.length);
    }

    return filtered;
  }, [lateRequests, userRole, user]);

  // Filter exit requests berdasarkan departemen yang dikepalai oleh supervisor
  const filteredExitRequests = useMemo(() => {
    let filtered = exitRequests;

    if (userRole === 'SUPERVISOR' && user) {
      console.log('Filtering exit requests for SUPERVISOR:', user.username);
      console.log('Available exit requests before filter:', exitRequests.length);

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      filtered = exitRequests.filter(request => {
        // Cek apakah request.employee.department.head.employeeId === user.username
        const isEmployeeUnderSupervisor =
          request.employee?.department?.head?.employeeId === user.username;

        if (isEmployeeUnderSupervisor) {
          console.log('Found exit request under supervisor:',
            `${request.employee.firstName} ${request.employee.lastName} in ${request.employee.department?.name}`);
        }

        return isEmployeeUnderSupervisor ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user.name);
      });

      console.log('Exit requests after filter:', filtered.length);
    }

    return filtered;
  }, [exitRequests, userRole, user]);

  // Format leave requests untuk tampilan tabel
  const formattedLeaveRequests = useMemo(() => {
    // Filter leave requests berdasarkan departemen yang dikepalai oleh supervisor
    let filteredRequests = leaveRequests;

    if (userRole === 'SUPERVISOR' && user) {
      console.log('Filtering leave requests for SUPERVISOR:', user.username);
      console.log('Available leave requests before filter:', leaveRequests.length);

      // Filter berdasarkan departemen yang dikepalai oleh supervisor
      filteredRequests = leaveRequests.filter(request => {
        // Cek apakah request.employee.department.head.employeeId === user.username
        const isEmployeeUnderSupervisor =
          request.employee?.department?.head?.employeeId === user.username;

        if (isEmployeeUnderSupervisor) {
          console.log('Found leave request under supervisor:',
            `${request.employee.firstName} ${request.employee.lastName} in ${request.employee.department?.name}`);
        }

        return isEmployeeUnderSupervisor ||
               (request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user.name);
      });

      console.log('Leave requests after filter:', filteredRequests.length);
    }

    return filteredRequests.map(request => {
      // Pastikan tanggal diformat dengan benar
      const fromDate = new Date(request.startDate);
      fromDate.setHours(0, 0, 0, 0);

      const toDate = new Date(request.endDate);
      toDate.setHours(0, 0, 0, 0);

      return {
        id: `LV${String(request.id).padStart(3, '0')}`,
        employee: request.employee ? `${request.employee.firstName} ${request.employee.lastName}` : 'Unknown',
        type: request.leaveType?.name || 'Unknown',
        from: fromDate,
        to: toDate,
        status: request.status,
        originalId: request.id,
        department: request.employee?.department?.name || 'Unknown'
      };
    });
  }, [leaveRequests, userRole, user]);

  // Fungsi untuk mengambil data leave types
  const fetchLeaveTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/leave-types');
      if (!response.ok) throw new Error('Failed to fetch leave types');
      const data = await response.json();
      setLeaveTypes(data);
    } catch (error) {
      console.error('Error fetching leave types:', error);
      toast({
        title: "Error",
        description: "Failed to fetch leave types",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk mengambil data leave requests
  const fetchLeaveRequests = async () => {
    try {
      const response = await fetch('/api/leave-management');
      if (!response.ok) {
        throw new Error(`Failed to fetch leave requests: ${response.status}`);
      }
      const data = await response.json();
      console.log('Leave requests data:', data);
      console.log(`Received ${data.length} leave requests from API`);

      if (userRole === 'SUPERVISOR') {
        console.log('SUPERVISOR: Checking if leave requests are filtered correctly');
        const uniqueEmployees = new Set(data.map((req: any) =>
          req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : 'Unknown'
        ));
        console.log('Unique employees in leave requests:', Array.from(uniqueEmployees));
      }

      setLeaveRequests(data);

      // Filter data untuk supervisor
      let filteredData = data;

      if (userRole === 'SUPERVISOR' && user) {
        console.log('Filtering calendar events for SUPERVISOR:', user.username);

        // Filter berdasarkan departemen yang dikepalai oleh supervisor
        filteredData = data.filter((request: any) => {
          // Cek apakah request.employee.department.head.employeeId === user.username
          const isEmployeeUnderSupervisor =
            request.employee?.department?.head?.employeeId === user.username;

          // Juga tampilkan permintaan dari supervisor sendiri
          const isOwnRequest =
            request.employee && `${request.employee.firstName} ${request.employee.lastName}` === user.name;

          return isEmployeeUnderSupervisor || isOwnRequest;
        });

        console.log('Calendar events after filter:', filteredData.length);
      }

      // Format data untuk calendar
      const formattedEvents = filteredData.map((request: any) => {
        // Pastikan tanggal diformat dengan benar
        const fromDate = new Date(request.startDate);
        fromDate.setHours(0, 0, 0, 0);

        const toDate = new Date(request.endDate);
        toDate.setHours(0, 0, 0, 0);

        return {
          id: `LV${String(request.id).padStart(3, '0')}`,
          employee: request.employee ? `${request.employee.firstName} ${request.employee.lastName}` : 'Unknown',
          type: request.leaveType?.name || 'Unknown',
          from: fromDate,
          to: toDate,
          status: request.status
        };
      });

      setLeaveEvents(formattedEvents);

      // Hitung jumlah leave requests yang pending untuk supervisor
      if (userRole === 'SUPERVISOR') {
        // Gunakan filteredData yang sudah difilter berdasarkan departemen
        const pendingRequests = filteredData.filter(req =>
          req.status === 'pending' &&
          req.employee && `${req.employee.firstName} ${req.employee.lastName}` !== user?.name
        );
        setStats(prev => ({ ...prev, pendingLeaveRequests: pendingRequests.length }));
        console.log('Pending leave requests for supervisor:', pendingRequests.length);
      }
    } catch (error) {
      console.error('Failed to fetch leave requests:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data leave requests",
        variant: "destructive",
      });
      // Set empty array to prevent UI issues
      setLeaveRequests([]);
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk mengambil data employees (untuk admin)
  const fetchEmployees = async () => {
    if (userRole !== 'ADMIN') return;

    try {
      const response = await fetch('/api/employees');
      if (!response.ok) throw new Error('Failed to fetch employees');
      const data = await response.json();
      setEmployees(data);
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  // Fungsi untuk mengambil data late requests
  const fetchLateRequests = async () => {
    try {
      const response = await fetch('/api/late-requests');
      if (!response.ok) {
        throw new Error(`Failed to fetch late requests: ${response.status}`);
      }
      const data = await response.json();
      console.log('Late requests data:', data);
      console.log(`Received ${data.length} late requests from API`);

      if (userRole === 'SUPERVISOR') {
        console.log('SUPERVISOR: Checking if late requests are filtered correctly');
        const uniqueEmployees = new Set(data.map((req: any) =>
          req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : 'Unknown'
        ));
        console.log('Unique employees in late requests:', Array.from(uniqueEmployees));
      }

      setLateRequests(data);

      // Hitung jumlah late requests yang pending untuk supervisor
      if (userRole === 'SUPERVISOR') {
        // Filter data berdasarkan departemen yang dikepalai oleh supervisor
        const filteredData = data.filter(req => {
          // Cek apakah req.employee.department.head.employeeId === user.username
          const isEmployeeUnderSupervisor =
            req.employee?.department?.head?.employeeId === user.username;

          // Juga tampilkan permintaan dari supervisor sendiri
          const isOwnRequest =
            req.employee && `${req.employee.firstName} ${req.employee.lastName}` === user.name;

          return isEmployeeUnderSupervisor || isOwnRequest;
        });

        // Hitung jumlah permintaan pending dari data yang sudah difilter
        const pendingRequests = filteredData.filter(req =>
          req.status === 'pending' &&
          req.employee &&
          `${req.employee.firstName} ${req.employee.lastName}` !== user?.name
        );

        setStats(prev => ({ ...prev, pendingLateRequests: pendingRequests.length }));
        console.log('Pending late requests for supervisor:', pendingRequests.length);
      }
    } catch (error) {
      console.error('Failed to fetch late requests:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data late requests",
        variant: "destructive",
      });
      // Set empty array to prevent UI issues
      setLateRequests([]);
    }
  };

  // Function to check if a date has events with specific status
  const isDayWithApprovedEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'approved';
    });
  };

  const isDayWithPendingEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'pending';
    });
  };

  const isDayWithRejectedEvent = (date: Date) => {
    return leaveEvents.some(event => {
      const eventStart = new Date(event.from);
      eventStart.setHours(0, 0, 0, 0);
      const eventEnd = new Date(event.to);
      eventEnd.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      return checkDate >= eventStart && checkDate <= eventEnd && event.status === 'rejected';
    });
  };

  // Custom modifiers for the calendar
  const modifiers = {
    hasApprovedEvent: (date: Date) => isDayWithApprovedEvent(date),
    hasPendingEvent: (date: Date) => isDayWithPendingEvent(date),
    hasRejectedEvent: (date: Date) => isDayWithRejectedEvent(date),
  };

  // Custom modifiers styles
  const modifiersStyles = {
    hasApprovedEvent: {
      backgroundColor: "rgba(34, 197, 94, 0.2)", // Green for approved
      borderRadius: "0",
    },
    hasPendingEvent: {
      backgroundColor: "rgba(234, 179, 8, 0.2)", // Yellow for pending
      borderRadius: "0",
    },
    hasRejectedEvent: {
      backgroundColor: "rgba(239, 68, 68, 0.2)", // Red for rejected
      borderRadius: "0",
    }
  };

  // Fungsi untuk mengambil data exit requests
  const fetchExitRequests = async () => {
    try {
      const response = await fetch('/api/exit-requests');
      if (!response.ok) {
        throw new Error(`Failed to fetch exit requests: ${response.status}`);
      }
      const data = await response.json();
      console.log('Exit requests data:', data);

      // Pastikan data adalah array
      const exitRequestsArray = Array.isArray(data) ? data : [];
      console.log(`Received ${exitRequestsArray.length} exit requests from API`);

      if (userRole === 'SUPERVISOR') {
        console.log('SUPERVISOR: Checking if exit requests are filtered correctly');
        const uniqueEmployees = new Set(exitRequestsArray.map((req: any) =>
          req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : 'Unknown'
        ));
        console.log('Unique employees in exit requests:', Array.from(uniqueEmployees));
      }

      setExitRequests(exitRequestsArray);

      // Hitung jumlah exit requests yang pending untuk supervisor
      if (userRole === 'SUPERVISOR') {
        // Filter data berdasarkan departemen yang dikepalai oleh supervisor
        const filteredData = exitRequestsArray.filter((req: any) => {
          // Cek apakah req.employee.department.head.employeeId === user.username
          const isEmployeeUnderSupervisor =
            req.employee?.department?.head?.employeeId === user.username;

          // Juga tampilkan permintaan dari supervisor sendiri
          const isOwnRequest =
            req.employee && `${req.employee.firstName} ${req.employee.lastName}` === user.name;

          return isEmployeeUnderSupervisor || isOwnRequest;
        });

        // Hitung jumlah permintaan pending dari data yang sudah difilter
        const pendingRequests = filteredData.filter((req: any) =>
          req.status === 'pending' &&
          req.employee &&
          `${req.employee.firstName} ${req.employee.lastName}` !== user?.name
        );

        setStats(prev => ({ ...prev, pendingExitRequests: pendingRequests.length }));
        console.log('Pending exit requests for supervisor:', pendingRequests.length);
      }
    } catch (error) {
      console.error('Failed to fetch exit requests:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data exit requests",
        variant: "destructive",
      });
      // Set empty array to prevent UI issues
      setExitRequests([]);
    }
  };

  // Fungsi untuk mengambil data leave events untuk calendar
  const fetchLeaveEvents = async () => {
    try {
      // Jika endpoint calendar belum tersedia, gunakan data dari leave requests
      try {
        const response = await fetch('/api/leave-management/calendar');
        if (!response.ok) {
          throw new Error(`Failed to fetch leave events: ${response.status}`);
        }
        const data = await response.json();
        console.log('Calendar events data:', data);
        setLeaveEvents(data);
      } catch (calendarError) {
        console.error('Error fetching from calendar endpoint:', calendarError);
        console.log('Falling back to leave requests data for calendar');

        // Gunakan data leave requests sebagai fallback
        if (leaveRequests.length > 0) {
          const events = leaveRequests.map(request => ({
            id: `LV${String(request.id).padStart(3, '0')}`,
            title: request.leaveType?.name || 'Leave',
            employee: request.employee ? `${request.employee.firstName} ${request.employee.lastName}` : 'Unknown',
            from: request.startDate,
            to: request.endDate,
            status: request.status,
            type: request.leaveType?.name || 'Leave'
          }));
          setLeaveEvents(events);
        }
      }
    } catch (error) {
      console.error('Error in fetchLeaveEvents:', error);
      toast({
        title: "Error",
        description: "Gagal mengambil data kalender cuti",
        variant: "destructive",
      });
      // Set empty array to prevent UI issues
      setLeaveEvents([]);
    }
  };

  // Ambil data saat komponen dimuat
  useEffect(() => {
    // Hanya fetch data jika user sudah login
    if (user) {
      setLoading(true);

      // Ambil data secara berurutan untuk memastikan dependensi terpenuhi
      const fetchData = async () => {
        try {
          // Ambil data leave requests terlebih dahulu
          await fetchLeaveRequests();
          await fetchLeaveTypes();
          await fetchEmployees();
          await fetchLateRequests();
          await fetchExitRequests();

          // Ambil data calendar terakhir karena mungkin bergantung pada leave requests
          await fetchLeaveEvents();
        } catch (error) {
          console.error('Error fetching data:', error);
          toast({
            title: "Error",
            description: "Terjadi kesalahan saat mengambil data",
            variant: "destructive",
          });
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, userRole]);

  // Hitung end date saat dialog dibuka atau saat startDate/duration berubah
  useEffect(() => {
    if (isAddLeaveRequestOpen) {
      calculateEndDate(leaveRequestForm.startDate, leaveRequestForm.duration);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAddLeaveRequestOpen, leaveRequestForm.startDate, leaveRequestForm.duration]);

  // Fungsi untuk melihat detail leave request
  const handleView = (id: number) => {
    const leaveRequest = leaveRequests.find(req => req.id === id);
    setSelectedLeaveRequest(leaveRequest);
    setIsViewLeaveRequestOpen(true);
  };

  // Fungsi untuk mengedit leave request
  const handleEdit = (id: number) => {
    const leaveRequest = leaveRequests.find(req => req.id === id);
    if (leaveRequest) {
      setEditLeaveForm({
        id: leaveRequest.id.toString(),
        leaveTypeId: leaveRequest.leaveTypeId.toString(),
        startDate: new Date(leaveRequest.startDate).toISOString().split('T')[0],
        endDate: new Date(leaveRequest.endDate).toISOString().split('T')[0],
        reason: leaveRequest.reason || ''
      });
      setIsEditLeaveRequestOpen(true);
    }
  };

  // Fungsi untuk menyetujui leave request
  const handleApprove = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/leave-management/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve leave request');
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan cuti berhasil disetujui",
      });
    } catch (error) {
      console.error('Error approving leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menyetujui permintaan cuti',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menolak leave request
  const handleReject = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/leave-management/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject leave request');
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan cuti berhasil ditolak",
      });
    } catch (error) {
      console.error('Error rejecting leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menolak permintaan cuti',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk melihat detail late request
  const handleViewLate = (id: number) => {
    const lateRequest = lateRequests.find(req => req.id === id);
    setSelectedLateRequest(lateRequest);
    setIsViewLateRequestOpen(true);
  };

  // Fungsi untuk mengedit late request
  const handleEditLate = (id: number) => {
    const lateRequest = lateRequests.find(req => req.id === id);
    if (lateRequest) {
      setEditLateForm({
        id: lateRequest.id.toString(),
        lateType: lateRequest.lateType,
        lateDate: new Date(lateRequest.lateDate).toISOString().split('T')[0],
        estimatedTime: lateRequest.estimatedTime,
        reason: lateRequest.reason || ''
      });
      setIsEditLateRequestOpen(true);
    }
  };

  // Fungsi untuk menyetujui late request
  const handleApproveLate = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/late-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve late request');
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil disetujui",
      });
    } catch (error) {
      console.error('Error approving late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menyetujui permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menolak late request
  const handleRejectLate = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/late-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject late request');
      }

      // Refresh data setelah berhasil mengubah status
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil ditolak",
      });
    } catch (error) {
      console.error('Error rejecting late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menolak permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk melihat detail exit request
  const handleViewExit = (id: number) => {
    const exitRequest = exitRequests.find(req => req.id === id);
    setSelectedExitRequest(exitRequest);
    setIsViewExitRequestOpen(true);
  };

  // Fungsi untuk mengedit exit request
  const handleEditExit = (id: number) => {
    const exitRequest = exitRequests.find(req => req.id === id);
    if (exitRequest) {
      setEditExitForm({
        id: exitRequest.id.toString(),
        exitType: exitRequest.exitType,
        exitDate: new Date(exitRequest.exitDate).toISOString().split('T')[0],
        exitTime: exitRequest.exitTime,
        comebackTime: exitRequest.comebackTime || '',
        notComeback: exitRequest.notComeback || false,
        reason: exitRequest.reason || ''
      });
      setIsEditExitRequestOpen(true);
    }
  };

  // Fungsi untuk menyetujui exit request
  const handleApproveExit = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/exit-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          approvedById: approvedById,
          notes: 'Approved by ' + user?.name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve exit request');
      }

      // Refresh data setelah berhasil mengubah status
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keluar sekolah berhasil disetujui",
      });
    } catch (error) {
      console.error('Error approving exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menyetujui permintaan keluar sekolah',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menolak exit request
  const handleRejectExit = async (id: number) => {
    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const approvedById = user?.employeeId || user?.id;

      const response = await fetch(`/api/exit-requests/${id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          approvedById: approvedById,
          notes: 'Rejected by ' + user?.name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject exit request');
      }

      // Refresh data setelah berhasil mengubah status
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keluar sekolah berhasil ditolak",
      });
    } catch (error) {
      console.error('Error rejecting exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menolak permintaan keluar sekolah',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menghapus leave request
  const handleDelete = async (id: number) => {
    // Konfirmasi penghapusan
    if (!confirm('Apakah Anda yakin ingin menghapus permintaan cuti ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/leave-management/${id}/delete`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete leave request');
      }

      // Refresh data setelah berhasil menghapus
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan cuti berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menghapus permintaan cuti',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menyimpan perubahan setelah mengedit leave request
  const handleSaveLeaveRequest = async () => {
    try {
      // Validasi form
      if (!editLeaveForm.leaveTypeId) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe leave",
          variant: "destructive",
        });
        return;
      }

      if (!editLeaveForm.startDate) {
        toast({
          title: "Error",
          description: "Tanggal mulai harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editLeaveForm.endDate) {
        toast({
          title: "Error",
          description: "Tanggal selesai harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editLeaveForm.reason) {
        toast({
          title: "Error",
          description: "Alasan harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Siapkan data untuk dikirim ke API
      const requestData = {
        leaveTypeId: parseInt(editLeaveForm.leaveTypeId),
        startDate: editLeaveForm.startDate,
        endDate: editLeaveForm.endDate,
        reason: editLeaveForm.reason
      };

      // Kirim data ke API
      const response = await fetch(`/api/leave-management/${editLeaveForm.id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update leave request');
      }

      // Tutup dialog
      setIsEditLeaveRequestOpen(false);

      // Refresh data
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan cuti berhasil diperbarui",
      });
    } catch (error) {
      console.error('Error updating leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal memperbarui permintaan cuti',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menghapus late request
  const handleDeleteLate = async (id: number) => {
    // Konfirmasi penghapusan
    if (!confirm('Apakah Anda yakin ingin menghapus permintaan keterlambatan ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/late-requests/${id}/delete`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete late request');
      }

      // Refresh data setelah berhasil menghapus
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menghapus permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menyimpan perubahan setelah mengedit late request
  const handleSaveLateRequest = async () => {
    try {
      // Validasi form
      if (!editLateForm.lateType) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe keterlambatan",
          variant: "destructive",
        });
        return;
      }

      if (!editLateForm.lateDate) {
        toast({
          title: "Error",
          description: "Tanggal keterlambatan harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editLateForm.estimatedTime) {
        toast({
          title: "Error",
          description: "Estimasi waktu datang harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editLateForm.reason) {
        toast({
          title: "Error",
          description: "Alasan harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Siapkan data untuk dikirim ke API
      const requestData = {
        lateType: editLateForm.lateType,
        lateDate: editLateForm.lateDate,
        estimatedTime: editLateForm.estimatedTime,
        reason: editLateForm.reason
      };

      // Kirim data ke API
      const response = await fetch(`/api/late-requests/${editLateForm.id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update late request');
      }

      // Tutup dialog
      setIsEditLateRequestOpen(false);

      // Refresh data
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil diperbarui",
      });
    } catch (error) {
      console.error('Error updating late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal memperbarui permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menghapus exit request
  const handleDeleteExit = async (id: number) => {
    // Konfirmasi penghapusan
    if (!confirm('Apakah Anda yakin ingin menghapus permintaan keluar sekolah ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/exit-requests/${id}/delete`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete exit request');
      }

      // Refresh data setelah berhasil menghapus
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keluar sekolah berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal menghapus permintaan keluar sekolah',
        variant: "destructive",
      });
    }
  };

  // Fungsi untuk menyimpan perubahan setelah mengedit exit request
  const handleSaveExitRequest = async () => {
    try {
      // Validasi form
      if (!editExitForm.exitType) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe exit",
          variant: "destructive",
        });
        return;
      }

      if (!editExitForm.exitDate) {
        toast({
          title: "Error",
          description: "Tanggal exit harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editExitForm.exitTime) {
        toast({
          title: "Error",
          description: "Waktu exit harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!editExitForm.notComeback && !editExitForm.comebackTime) {
        toast({
          title: "Error",
          description: "Waktu kembali harus diisi atau pilih Not Comeback",
          variant: "destructive",
        });
        return;
      }

      if (!editExitForm.reason) {
        toast({
          title: "Error",
          description: "Alasan harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Siapkan data untuk dikirim ke API
      const requestData = {
        exitType: editExitForm.exitType,
        exitDate: editExitForm.exitDate,
        exitTime: editExitForm.exitTime,
        comebackTime: editExitForm.notComeback ? null : editExitForm.comebackTime,
        notComeback: editExitForm.notComeback,
        reason: editExitForm.reason
      };

      // Kirim data ke API
      const response = await fetch(`/api/exit-requests/${editExitForm.id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update exit request');
      }

      // Tutup dialog
      setIsEditExitRequestOpen(false);

      // Refresh data
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keluar sekolah berhasil diperbarui",
      });
    } catch (error) {
      console.error('Error updating exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal memperbarui permintaan keluar sekolah',
        variant: "destructive",
      });
    }
  };

  // Handler untuk perubahan input pada form leave request
  const handleLeaveInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setLeaveRequestForm(prev => ({
      ...prev,
      [id]: value
    }));

    // Jika yang berubah adalah startDate atau duration, hitung ulang endDate
    if (id === 'startDate' || id === 'duration') {
      calculateEndDate(
        id === 'startDate' ? value : leaveRequestForm.startDate,
        id === 'duration' ? value : leaveRequestForm.duration
      );
    }
  };

  // Handler untuk perubahan file attachment
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setAttachment(e.target.files[0]);
    } else {
      setAttachment(null);
    }
  };

  // Fungsi untuk menghitung end date berdasarkan start date dan duration
  const calculateEndDate = (startDateStr: string, durationStr: string) => {
    if (!startDateStr || !durationStr) {
      setCalculatedEndDate(null);
      return;
    }

    const startDate = new Date(startDateStr);
    const duration = parseInt(durationStr);

    if (isNaN(duration) || duration < 1) {
      setCalculatedEndDate(null);
      return;
    }

    // Hitung end date dengan menambahkan durasi (tidak termasuk Sabtu dan Minggu)
    let endDate = new Date(startDate);
    let daysAdded = 0;

    while (daysAdded < duration) {
      endDate.setDate(endDate.getDate() + 1);
      const dayOfWeek = endDate.getDay();

      // Skip Sabtu (6) dan Minggu (0)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        daysAdded++;
      }
    }

    // Format end date untuk input date
    const formattedEndDate = endDate.toISOString().split('T')[0];
    setCalculatedEndDate(formattedEndDate);

    // Update form dengan end date yang baru
    setLeaveRequestForm(prev => ({
      ...prev,
      endDate: formattedEndDate
    }));
  };

  // Handler untuk perubahan select pada form leave request
  const handleLeaveSelectChange = (name: string, value: string) => {
    setLeaveRequestForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk submit form leave request
  const handleSubmitLeaveRequest = async () => {
    try {
      // Validasi form
      if (!leaveRequestForm.leaveTypeId) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe leave",
          variant: "destructive",
        });
        return;
      }

      if (!leaveRequestForm.startDate) {
        toast({
          title: "Error",
          description: "Tanggal mulai harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!leaveRequestForm.duration || parseInt(leaveRequestForm.duration) < 1) {
        toast({
          title: "Error",
          description: "Durasi harus diisi dengan nilai minimal 1 hari",
          variant: "destructive",
        });
        return;
      }

      if (!leaveRequestForm.endDate) {
        toast({
          title: "Error",
          description: "Tanggal selesai harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!leaveRequestForm.reason) {
        toast({
          title: "Error",
          description: "Alasan harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Siapkan data untuk dikirim ke API
      const requestData = {
        ...leaveRequestForm,
        employeeId: userRole === 'ADMIN' ? parseInt(leaveRequestForm.employeeId) : parseInt(user?.id || '0'),
        leaveTypeId: parseInt(leaveRequestForm.leaveTypeId),
        duration: parseInt(leaveRequestForm.duration),
        durationDays: parseInt(leaveRequestForm.duration), // API mengharapkan parameter durationDays
        status: 'pending'
      };

      // Jika ada attachment, upload file terlebih dahulu
      let attachmentUrl = null;
      if (attachment) {
        // Buat FormData untuk upload file
        const formData = new FormData();
        formData.append('file', attachment);

        // Upload file ke API
        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload attachment');
        }

        const uploadResult = await uploadResponse.json();
        attachmentUrl = uploadResult.url;
      }

      // Tambahkan attachmentUrl ke requestData jika ada
      if (attachmentUrl) {
        requestData.attachmentUrl = attachmentUrl;
      }

      // Kirim data ke API
      const response = await fetch('/api/leave-management/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create leave request');
      }

      // Reset form dan tutup dialog
      setLeaveRequestForm({
        employeeId: '',
        leaveTypeId: '',
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0],
        duration: '1',
        reason: ''
      });
      setAttachment(null);
      setCalculatedEndDate(null);
      setIsAddLeaveRequestOpen(false);

      // Refresh data
      await fetchLeaveRequests();

      toast({
        title: "Sukses",
        description: "Permintaan cuti berhasil dibuat",
      });
    } catch (error) {
      console.error('Error creating leave request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal membuat permintaan cuti',
        variant: "destructive",
      });
    }
  };

  // Handler untuk perubahan input pada form late request
  const handleLateInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setLateRequestForm(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Handler untuk perubahan select pada form late request
  const handleLateSelectChange = (name: string, value: string) => {
    setLateRequestForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk submit form late request
  const handleSubmitLateRequest = async () => {
    try {
      // Validasi form
      if (!lateRequestForm.lateType) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe keterlambatan",
          variant: "destructive",
        });
        return;
      }

      if (!lateRequestForm.lateDate) {
        toast({
          title: "Error",
          description: "Tanggal keterlambatan harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!lateRequestForm.estimatedTime) {
        toast({
          title: "Error",
          description: "Estimasi waktu datang harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!lateRequestForm.reason) {
        toast({
          title: "Error",
          description: "Alasan harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Siapkan data untuk dikirim ke API
      const requestData = {
        ...lateRequestForm,
        employeeId: userRole === 'ADMIN' ? parseInt(lateRequestForm.employeeId) : parseInt(user?.id || '0'),
        status: 'pending'
      };

      // Kirim data ke API
      const response = await fetch('/api/late-requests/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create late request');
      }

      // Reset form dan tutup dialog
      setLateRequestForm({
        employeeId: '',
        lateType: '',
        lateDate: new Date().toISOString().split('T')[0],
        estimatedTime: '08:00',
        reason: ''
      });
      setIsAddLateRequestOpen(false);

      // Refresh data
      await fetchLateRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keterlambatan berhasil dibuat",
      });
    } catch (error) {
      console.error('Error creating late request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal membuat permintaan keterlambatan',
        variant: "destructive",
      });
    }
  };

  // Handler untuk perubahan input pada form exit request
  const handleExitInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setExitRequestForm(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Handler untuk perubahan select pada form exit request
  const handleExitSelectChange = (name: string, value: string) => {
    setExitRequestForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk perubahan checkbox pada form exit request
  const handleExitCheckboxChange = (checked: boolean) => {
    setExitRequestForm(prev => ({
      ...prev,
      notComeback: checked,
      comebackTime: checked ? '' : prev.comebackTime
    }));
  };

  // Handler untuk submit form exit request
  const handleSubmitExitRequest = async () => {
    try {
      // Validasi form
      if (!exitRequestForm.exitType) {
        toast({
          title: "Error",
          description: "Silakan pilih tipe exit",
          variant: "destructive",
        });
        return;
      }

      if (!exitRequestForm.exitDate) {
        toast({
          title: "Error",
          description: "Tanggal exit harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!exitRequestForm.exitTime) {
        toast({
          title: "Error",
          description: "Waktu exit harus diisi",
          variant: "destructive",
        });
        return;
      }

      if (!exitRequestForm.notComeback && !exitRequestForm.comebackTime) {
        toast({
          title: "Error",
          description: "Waktu kembali harus diisi atau pilih Not Comeback",
          variant: "destructive",
        });
        return;
      }

      if (!exitRequestForm.reason) {
        toast({
          title: "Error",
          description: "Alasan harus diisi",
          variant: "destructive",
        });
        return;
      }

      // Siapkan data untuk dikirim ke API
      const requestData = {
        ...exitRequestForm,
        employeeId: userRole === 'ADMIN' ? parseInt(exitRequestForm.employeeId) : parseInt(user?.id || '0'),
        status: 'pending'
      };

      // Kirim data ke API
      const response = await fetch('/api/exit-requests/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create exit request');
      }

      // Reset form dan tutup dialog
      setExitRequestForm({
        employeeId: '',
        exitType: '',
        exitDate: new Date().toISOString().split('T')[0],
        exitTime: '08:00',
        comebackTime: '16:00',
        notComeback: false,
        reason: ''
      });
      setIsAddExitRequestOpen(false);

      // Refresh data
      await fetchExitRequests();

      toast({
        title: "Sukses",
        description: "Permintaan keluar sekolah berhasil dibuat",
      });
    } catch (error) {
      console.error('Error creating exit request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Gagal membuat permintaan keluar sekolah',
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar userRole={userRole} />
      <div>Test</div>
    </div>
  );
}
                      </p>
                    </div>
                    <div>
                      <Label>Leave Type</Label>
                      <p className="text-sm">{selectedLeaveRequest.leaveType?.name || 'Unknown'}</p>
                    </div>
                    <div>
                      <Label>Start Date</Label>
                      <p className="text-sm">
                        {selectedLeaveRequest.startDate ?
                          new Date(selectedLeaveRequest.startDate).toLocaleDateString() :
                          '-'}
                      </p>
                    </div>
                    <div>
                      <Label>End Date</Label>
                      <p className="text-sm">
                        {selectedLeaveRequest.endDate ?
                          new Date(selectedLeaveRequest.endDate).toLocaleDateString() :
                          '-'}
                      </p>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <p className="text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          selectedLeaveRequest.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : selectedLeaveRequest.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                        }`}>
                          {selectedLeaveRequest.status.charAt(0).toUpperCase() + selectedLeaveRequest.status.slice(1)}
                        </span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <Label>Reason</Label>
                    <p className="text-sm">{selectedLeaveRequest.reason || '-'}</p>
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsViewLeaveRequestOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Dialog untuk melihat detail late request */}
          <Dialog open={isViewLateRequestOpen} onOpenChange={setIsViewLateRequestOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Late Request Details</DialogTitle>
              </DialogHeader>
              {selectedLateRequest && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Employee</Label>
                      <p className="text-sm">
                        {selectedLateRequest.employee ?
                          `${selectedLateRequest.employee.firstName} ${selectedLateRequest.employee.lastName}` :
                          'Unknown'}
                      </p>
                    </div>
                    <div>
                      <Label>Type</Label>
                      <p className="text-sm">{selectedLateRequest.lateType || '-'}</p>
                    </div>
                    <div>
                      <Label>Date</Label>
                      <p className="text-sm">
                        {selectedLateRequest.lateDate ?
                          new Date(selectedLateRequest.lateDate).toLocaleDateString() :
                          '-'}
                      </p>
                    </div>
                    <div>
                      <Label>Estimated Time</Label>
                      <p className="text-sm">{selectedLateRequest.estimatedTime || '-'}</p>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <p className="text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          selectedLateRequest.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : selectedLateRequest.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                        }`}>
                          {selectedLateRequest.status.charAt(0).toUpperCase() + selectedLateRequest.status.slice(1)}
                        </span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <Label>Reason</Label>
                    <p className="text-sm">{selectedLateRequest.reason || '-'}</p>
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsViewLateRequestOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Dialog untuk melihat detail exit request */}
          <Dialog open={isViewExitRequestOpen} onOpenChange={setIsViewExitRequestOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Exit Request Details</DialogTitle>
              </DialogHeader>
              {selectedExitRequest && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Employee</Label>
                      <p className="text-sm">
                        {selectedExitRequest.employee ?
                          `${selectedExitRequest.employee.firstName} ${selectedExitRequest.employee.lastName}` :
                          'Unknown'}
                      </p>
                    </div>
                    <div>
                      <Label>Type</Label>
                      <p className="text-sm">{selectedExitRequest.exitType || '-'}</p>
                    </div>
                    <div>
                      <Label>Date</Label>
                      <p className="text-sm">
                        {selectedExitRequest.exitDate ?
                          new Date(selectedExitRequest.exitDate).toLocaleDateString() :
                          '-'}
                      </p>
                    </div>
                    <div>
                      <Label>Exit Time</Label>
                      <p className="text-sm">{selectedExitRequest.exitTime || '-'}</p>
                    </div>
                    <div>
                      <Label>Comeback Time</Label>
                      <p className="text-sm">
                        {selectedExitRequest.notComeback ? 'Not Coming Back' : (selectedExitRequest.comebackTime || '-')}
                      </p>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <p className="text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          selectedExitRequest.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : selectedExitRequest.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                        }`}>
                          {selectedExitRequest.status.charAt(0).toUpperCase() + selectedExitRequest.status.slice(1)}
                        </span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <Label>Reason</Label>
                    <p className="text-sm">{selectedExitRequest.reason || '-'}</p>
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsViewExitRequestOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Dialog untuk mengedit leave request */}
          <Dialog open={isEditLeaveRequestOpen} onOpenChange={setIsEditLeaveRequestOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Leave Request</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
            <div>
              <Label htmlFor="leaveTypeId">Leave Type</Label>
              <Select
                value={editLeaveForm.leaveTypeId}
                onValueChange={(value) => setEditLeaveForm({...editLeaveForm, leaveTypeId: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select leave type" />
                </SelectTrigger>
                <SelectContent>
                  {leaveTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id.toString()}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  type="date"
                  id="startDate"
                  value={editLeaveForm.startDate}
                  onChange={(e) => setEditLeaveForm({...editLeaveForm, startDate: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  type="date"
                  id="endDate"
                  value={editLeaveForm.endDate}
                  onChange={(e) => setEditLeaveForm({...editLeaveForm, endDate: e.target.value})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                value={editLeaveForm.reason}
                onChange={(e) => setEditLeaveForm({...editLeaveForm, reason: e.target.value})}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditLeaveRequestOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveLeaveRequest}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk mengedit late request */}
      <Dialog open={isEditLateRequestOpen} onOpenChange={setIsEditLateRequestOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Late Request</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="lateType">Late Type</Label>
              <Select
                value={editLateForm.lateType}
                onValueChange={(value) => setEditLateForm({...editLateForm, lateType: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select late type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Urgent">Urgent</SelectItem>
                  <SelectItem value="Work">Work</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="lateDate">Date</Label>
                <Input
                  type="date"
                  id="lateDate"
                  value={editLateForm.lateDate}
                  onChange={(e) => setEditLateForm({...editLateForm, lateDate: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="estimatedTime">Estimated Time</Label>
                <Input
                  type="time"
                  id="estimatedTime"
                  value={editLateForm.estimatedTime}
                  onChange={(e) => setEditLateForm({...editLateForm, estimatedTime: e.target.value})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                value={editLateForm.reason}
                onChange={(e) => setEditLateForm({...editLateForm, reason: e.target.value})}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditLateRequestOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveLateRequest}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog untuk mengedit exit request */}
      <Dialog open={isEditExitRequestOpen} onOpenChange={setIsEditExitRequestOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Exit Request</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="exitType">Exit Type</Label>
              <Select
                value={editExitForm.exitType}
                onValueChange={(value) => setEditExitForm({...editExitForm, exitType: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select exit type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Work">Work</SelectItem>
                  <SelectItem value="Personal">Personal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="exitDate">Date</Label>
                <Input
                  type="date"
                  id="exitDate"
                  value={editExitForm.exitDate}
                  onChange={(e) => setEditExitForm({...editExitForm, exitDate: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="exitTime">Exit Time</Label>
                <Input
                  type="time"
                  id="exitTime"
                  value={editExitForm.exitTime}
                  onChange={(e) => setEditExitForm({...editExitForm, exitTime: e.target.value})}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="notComeback"
                checked={editExitForm.notComeback}
                onCheckedChange={(checked) =>
                  setEditExitForm({...editExitForm, notComeback: checked as boolean})
                }
              />
              <Label htmlFor="notComeback">Not Coming Back</Label>
            </div>
            {!editExitForm.notComeback && (
              <div>
                <Label htmlFor="comebackTime">Comeback Time</Label>
                <Input
                  type="time"
                  id="comebackTime"
                  value={editExitForm.comebackTime}
                  onChange={(e) => setEditExitForm({...editExitForm, comebackTime: e.target.value})}
                />
              </div>
            )}
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                value={editExitForm.reason}
                onChange={(e) => setEditExitForm({...editExitForm, reason: e.target.value})}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditExitRequestOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveExitRequest}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Leave Management</h1>
          <p className="text-muted-foreground mt-2">
            Track and manage employee leave requests
          </p>
          {userRole === 'SUPERVISOR' && (
            <div className="flex gap-4 mt-4 bg-card p-4 rounded-lg shadow-sm">
              <div className="text-sm">
                <span className="font-medium">Pending Leave Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingLeaveRequests}
                </span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Pending Late Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingLateRequests}
                </span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Pending Exit Requests: </span>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                  {stats.pendingExitRequests}
                </span>
              </div>
            </div>
          )}
        </div>

        <Tabs defaultValue="leave-requests" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="leave-requests">Leave Requests</TabsTrigger>
            <TabsTrigger value="late">Late Requests</TabsTrigger>
            <TabsTrigger value="exit">Exit From School</TabsTrigger>
            <TabsTrigger value="calendar">Calendar</TabsTrigger>
            <TabsTrigger value="leave-types">Leave Types</TabsTrigger>
          </TabsList>

          <TabsContent value="leave-requests" className="space-y-4">
            <div className="bg-card rounded-lg shadow-sm p-6">
              <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                <div className="p-4 flex justify-between items-center border-b border-border">
                  <div>
                    <h2 className="text-lg font-semibold">Leave Requests</h2>
                    {userRole === 'EMPLOYEE' && (
                      <p className="text-xs text-muted-foreground">Showing your leave requests</p>
                    )}
                    {userRole === 'SUPERVISOR' && (
                      <>
                        <p className="text-xs text-muted-foreground">Showing leave requests from your department (employees you supervise)</p>
                        <p className="text-xs text-red-500 font-semibold">If you see requests from other departments, please contact the administrator to fix your department settings.</p>
                      </>
                    )}
                    {userRole === 'ADMIN' && (
                      <p className="text-xs text-muted-foreground">Showing all leave requests</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Dialog open={isAddLeaveRequestOpen} onOpenChange={setIsAddLeaveRequestOpen}>
                      <DialogTrigger asChild>
                        <Button className="flex items-center gap-2">
                          <Plus className="h-4 w-4" /> Add Leave Request
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Leave Request</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          {userRole === "ADMIN" && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="employeeId" className="text-right">
                                Employee
                              </Label>
                              <Select
                                onValueChange={(value) => handleLeaveSelectChange('employeeId', value)}
                                value={leaveRequestForm.employeeId}
                              >
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select Employee" />
                                </SelectTrigger>
                                <SelectContent>
                                  {employees.map(employee => (
                                    <SelectItem key={employee.id} value={employee.id.toString()}>
                                      {employee.firstName} {employee.lastName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="leaveTypeId" className="text-right">
                              Leave Type
                            </Label>
                            <Select
                              onValueChange={(value) => handleLeaveSelectChange('leaveTypeId', value)}
                              value={leaveRequestForm.leaveTypeId}
                            >
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select Leave Type" />
                              </SelectTrigger>
                              <SelectContent>
                                {leaveTypes.map(leaveType => (
                                  <SelectItem key={leaveType.id} value={leaveType.id.toString()}>
                                    {leaveType.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="startDate" className="text-right">
                              Start Date
                            </Label>
                            <Input
                              id="startDate"
                              type="date"
                              className="col-span-3"
                              value={leaveRequestForm.startDate}
                              onChange={handleLeaveInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="duration" className="text-right">
                              Lama Leave (hari)
                            </Label>
                            <div className="col-span-3">
                              <Input
                                id="duration"
                                type="number"
                                min="1"
                                className="w-full"
                                value={leaveRequestForm.duration}
                                onChange={handleLeaveInputChange}
                                placeholder="Masukkan jumlah hari leave"
                              />
                              <p className="text-xs text-muted-foreground mt-1">
                                *Tidak termasuk hari Sabtu dan Minggu
                              </p>
                            </div>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="endDate" className="text-right">
                              End Date
                            </Label>
                            <div className="col-span-3">
                              <Input
                                id="endDate"
                                type="date"
                                className="w-full"
                                value={leaveRequestForm.endDate}
                                readOnly
                              />
                              {calculatedEndDate && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  Tanggal akhir leave: {new Date(calculatedEndDate).toLocaleDateString('id-ID', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="reason" className="text-right">
                              Reason
                            </Label>
                            <Textarea
                              id="reason"
                              className="col-span-3"
                              value={leaveRequestForm.reason}
                              onChange={handleLeaveInputChange}
                              placeholder="Enter reason for leave"
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="attachment" className="text-right">
                              Attachment
                            </Label>
                            <div className="col-span-3">
                              <Input
                                id="attachment"
                                type="file"
                                className="w-full"
                                onChange={handleFileChange}
                                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                              />
                              <p className="text-xs text-muted-foreground mt-1">
                                *Opsional. Upload surat dokter atau dokumen pendukung lainnya (PDF, JPG, PNG, DOC).
                              </p>
                            </div>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsAddLeaveRequestOpen(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleSubmitLeaveRequest}>
                            Submit
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                    {userRole === "ADMIN" && (
                      <>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Employee</TableHead>
                        <TableHead>Leave Type</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-4">
                            <div className="flex justify-center items-center space-x-2">
                              <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                              <span>Loading leave requests...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : formattedLeaveRequests.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-4">
                            No leave requests found
                          </TableCell>
                        </TableRow>
                      ) : (
                        formattedLeaveRequests.map((leave: any) => (
                          <TableRow key={leave.id}>
                            <TableCell>{leave.id}</TableCell>
                            <TableCell>{leave.employee}</TableCell>
                            <TableCell>{leave.type}</TableCell>
                            <TableCell>{new Date(leave.from).toLocaleDateString()}</TableCell>
                            <TableCell>{new Date(leave.to).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                                leave.status === 'approved'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                  : leave.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              }`}>
                                {leave.status.charAt(0).toUpperCase() + leave.status.slice(1)}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleView(leave.originalId)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  {/* ADMIN dapat approve/reject semua leave request yang masih pending */}
                                  {userRole === "ADMIN" && leave.status === "pending" && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleApprove(leave.originalId)}>
                                        <Check className="mr-2 h-4 w-4" />
                                        Approve
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleReject(leave.originalId)}>
                                        <X className="mr-2 h-4 w-4" />
                                        Reject
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleEdit(leave.originalId)}>
                                        <Pencil className="mr-2 h-4 w-4" />
                                        Edit Permintaan
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleDelete(leave.originalId)} className="text-red-600">
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Delete
                                      </DropdownMenuItem>
                                    </>
                                  )}

                                  {/* SUPERVISOR: Cek apakah leave request adalah milik bawahannya */}
                                  {userRole === "SUPERVISOR" && (
                                    <>
                                      {/* Jika leave request bukan milik supervisor sendiri (milik bawahan) dan masih pending */}
                                      {leave.employee !== user?.name && leave.status === "pending" && (
                                        <>
                                          <DropdownMenuItem onClick={() => handleApprove(leave.originalId)}>
                                            <Check className="mr-2 h-4 w-4" />
                                            Approve
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => handleReject(leave.originalId)}>
                                            <X className="mr-2 h-4 w-4" />
                                            Reject
                                          </DropdownMenuItem>
                                        </>
                                      )}

                                      {/* Jika leave request milik supervisor sendiri dan masih pending */}
                                      {leave.employee === user?.name && leave.status === "pending" && (
                                        <DropdownMenuItem onClick={() => handleEdit(leave.originalId)}>
                                          <Pencil className="mr-2 h-4 w-4" />
                                          Edit Permintaan Saya
                                        </DropdownMenuItem>
                                      )}
                                    </>
                                  )}

                                  {/* EMPLOYEE hanya dapat mengedit permintaan mereka sendiri yang masih pending */}
                                  {userRole === "EMPLOYEE" &&
                                   leave.status === "pending" && (
                                    <DropdownMenuItem onClick={() => handleEdit(leave.originalId)}>
                                      <Pencil className="mr-2 h-4 w-4" />
                                      Edit Permintaan
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                  <div className="flex items-center justify-center py-4">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious href="#" />
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#" isActive>
                            1
                          </PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#">2</PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#">3</PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationNext href="#" />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="late" className="space-y-4">
            <div className="bg-card rounded-lg shadow-sm p-6">
              <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                <div className="p-4 flex justify-between items-center border-b border-border">
                  <div>
                    <h2 className="text-lg font-semibold">Late Requests</h2>
                    {userRole === 'EMPLOYEE' && (
                      <p className="text-xs text-muted-foreground">Showing your late requests</p>
                    )}
                    {userRole === 'SUPERVISOR' && (
                      <>
                        <p className="text-xs text-muted-foreground">Showing late requests from your department (employees you supervise)</p>
                        <p className="text-xs text-red-500 font-semibold">If you see requests from other departments, please contact the administrator to fix your department settings.</p>
                      </>
                    )}
                    {userRole === 'ADMIN' && (
                      <p className="text-xs text-muted-foreground">Showing all late requests</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Dialog open={isAddLateRequestOpen} onOpenChange={setIsAddLateRequestOpen}>
                      <DialogTrigger asChild>
                        <Button className="flex items-center gap-2">
                          <Plus className="h-4 w-4" /> Add Late Request
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Late Request</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          {userRole === "ADMIN" && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="employeeId" className="text-right">
                                Employee
                              </Label>
                              <Select
                                onValueChange={(value) => handleLateSelectChange('employeeId', value)}
                                value={lateRequestForm.employeeId}
                              >
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select Employee" />
                                </SelectTrigger>
                                <SelectContent>
                                  {employees.map(employee => (
                                    <SelectItem key={employee.id} value={employee.id.toString()}>
                                      {employee.firstName} {employee.lastName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="lateType" className="text-right">
                              Late Type
                            </Label>
                            <Select
                              onValueChange={(value) => handleLateSelectChange('lateType', value)}
                              value={lateRequestForm.lateType}
                            >
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select Late Type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Urgent">Urgent</SelectItem>
                                <SelectItem value="Work">Work</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="lateDate" className="text-right">
                              Date
                            </Label>
                            <Input
                              id="lateDate"
                              type="date"
                              className="col-span-3"
                              value={lateRequestForm.lateDate}
                              onChange={handleLateInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="estimatedTime" className="text-right">
                              Estimated Time
                            </Label>
                            <Input
                              id="estimatedTime"
                              type="time"
                              className="col-span-3"
                              value={lateRequestForm.estimatedTime}
                              onChange={handleLateInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="reason" className="text-right">
                              Reason
                            </Label>
                            <Textarea
                              id="reason"
                              className="col-span-3"
                              value={lateRequestForm.reason}
                              onChange={handleLateInputChange}
                              placeholder="Enter reason for late"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsAddLateRequestOpen(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleSubmitLateRequest}>
                            Submit
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                    {userRole === "ADMIN" && (
                      <>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Employee</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Est. Time</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-4">
                            <div className="flex justify-center items-center space-x-2">
                              <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                              <span>Loading late requests...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredLateRequests.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-4">
                            No late requests found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredLateRequests.map((late: any) => (
                          <TableRow key={late.id}>
                            <TableCell>LT{String(late.id).padStart(3, '0')}</TableCell>
                            <TableCell>
                              {late.employee ?
                                `${late.employee.firstName || ''} ${late.employee.lastName || ''}` :
                                'Unknown'}
                            </TableCell>
                            <TableCell>{late.lateType || '-'}</TableCell>
                            <TableCell>{late.lateDate ? new Date(late.lateDate).toLocaleDateString() : '-'}</TableCell>
                            <TableCell>{late.estimatedTime || '-'}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                                late.status === 'approved'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                  : late.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              }`}>
                                {late.status.charAt(0).toUpperCase() + late.status.slice(1)}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleViewLate(late.id)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  {/* ADMIN dapat approve/reject semua late request yang masih pending */}
                                  {userRole === "ADMIN" && late.status === "pending" && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleApproveLate(late.id)}>
                                        <Check className="mr-2 h-4 w-4" />
                                        Approve
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleRejectLate(late.id)}>
                                        <X className="mr-2 h-4 w-4" />
                                        Reject
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleEditLate(late.id)}>
                                        <Pencil className="mr-2 h-4 w-4" />
                                        Edit Permintaan
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleDeleteLate(late.id)} className="text-red-600">
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Delete
                                      </DropdownMenuItem>
                                    </>
                                  )}

                                  {/* SUPERVISOR: Cek apakah late request adalah milik bawahannya */}
                                  {userRole === "SUPERVISOR" && (
                                    <>
                                      {/* Jika late request bukan milik supervisor sendiri (milik bawahan) dan masih pending */}
                                      {late.employee &&
                                       `${late.employee.firstName} ${late.employee.lastName}` !== user?.name &&
                                       late.status === "pending" && (
                                        <>
                                          <DropdownMenuItem onClick={() => handleApproveLate(late.id)}>
                                            <Check className="mr-2 h-4 w-4" />
                                            Approve
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => handleRejectLate(late.id)}>
                                            <X className="mr-2 h-4 w-4" />
                                            Reject
                                          </DropdownMenuItem>
                                        </>
                                      )}

                                      {/* Jika late request milik supervisor sendiri dan masih pending */}
                                      {late.employee &&
                                       `${late.employee.firstName} ${late.employee.lastName}` === user?.name &&
                                       late.status === "pending" && (
                                        <DropdownMenuItem onClick={() => handleEditLate(late.id)}>
                                          <Pencil className="mr-2 h-4 w-4" />
                                          Edit Permintaan Saya
                                        </DropdownMenuItem>
                                      )}
                                    </>
                                  )}

                                  {/* EMPLOYEE hanya dapat mengedit permintaan mereka sendiri yang masih pending */}
                                  {userRole === "EMPLOYEE" &&
                                   late.status === "pending" && (
                                    <DropdownMenuItem onClick={() => handleEditLate(late.id)}>
                                      <Pencil className="mr-2 h-4 w-4" />
                                      Edit Permintaan
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                  <div className="flex items-center justify-center py-4">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious href="#" />
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#" isActive>
                            1
                          </PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#">2</PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#">3</PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationNext href="#" />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="exit" className="space-y-4">
            <div className="bg-card rounded-lg shadow-sm p-6">
              <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
                <div className="p-4 flex justify-between items-center border-b border-border">
                  <div>
                    <h2 className="text-lg font-semibold">Exit From School Requests</h2>
                    {userRole === 'EMPLOYEE' && (
                      <p className="text-xs text-muted-foreground">Showing your exit from school requests</p>
                    )}
                    {userRole === 'SUPERVISOR' && (
                      <>
                        <p className="text-xs text-muted-foreground">Showing exit from school requests from your department (employees you supervise)</p>
                        <p className="text-xs text-red-500 font-semibold">If you see requests from other departments, please contact the administrator to fix your department settings.</p>
                      </>
                    )}
                    {userRole === 'ADMIN' && (
                      <p className="text-xs text-muted-foreground">Showing all exit from school requests</p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Dialog open={isAddExitRequestOpen} onOpenChange={setIsAddExitRequestOpen}>
                      <DialogTrigger asChild>
                        <Button className="flex items-center gap-2">
                          <Plus className="h-4 w-4" /> Add Exit Request
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Exit Request</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          {userRole === "ADMIN" && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="employeeId" className="text-right">
                                Employee
                              </Label>
                              <Select
                                onValueChange={(value) => handleExitSelectChange('employeeId', value)}
                                value={exitRequestForm.employeeId}
                              >
                                <SelectTrigger className="col-span-3">
                                  <SelectValue placeholder="Select Employee" />
                                </SelectTrigger>
                                <SelectContent>
                                  {employees.map(employee => (
                                    <SelectItem key={employee.id} value={employee.id.toString()}>
                                      {employee.firstName} {employee.lastName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exitType" className="text-right">
                              Exit Type
                            </Label>
                            <Select
                              onValueChange={(value) => handleExitSelectChange('exitType', value)}
                              value={exitRequestForm.exitType}
                            >
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select Exit Type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Work">Work</SelectItem>
                                <SelectItem value="Personal">Personal</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exitDate" className="text-right">
                              Date
                            </Label>
                            <Input
                              id="exitDate"
                              type="date"
                              className="col-span-3"
                              value={exitRequestForm.exitDate}
                              onChange={handleExitInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="exitTime" className="text-right">
                              Exit Time
                            </Label>
                            <Input
                              id="exitTime"
                              type="time"
                              className="col-span-3"
                              value={exitRequestForm.exitTime}
                              onChange={handleExitInputChange}
                            />
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <div className="text-right col-span-1">
                              <Label htmlFor="notComeback">
                                Not Coming Back
                              </Label>
                            </div>
                            <div className="col-span-3 flex items-center">
                              <Checkbox
                                id="notComeback"
                                checked={exitRequestForm.notComeback}
                                onCheckedChange={handleExitCheckboxChange}
                              />
                              <Label htmlFor="notComeback" className="ml-2">
                                I will not be coming back to school today
                              </Label>
                            </div>
                          </div>
                          {!exitRequestForm.notComeback && (
                            <div className="grid grid-cols-4 items-center gap-4">
                              <Label htmlFor="comebackTime" className="text-right">
                                Comeback Time
                              </Label>
                              <Input
                                id="comebackTime"
                                type="time"
                                className="col-span-3"
                                value={exitRequestForm.comebackTime}
                                onChange={handleExitInputChange}
                              />
                            </div>
                          )}
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="reason" className="text-right">
                              Reason
                            </Label>
                            <Textarea
                              id="reason"
                              className="col-span-3"
                              value={exitRequestForm.reason}
                              onChange={handleExitInputChange}
                              placeholder="Enter reason for exit"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsAddExitRequestOpen(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleSubmitExitRequest}>
                            Submit
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                    {userRole === "ADMIN" && (
                      <>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Employee</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Exit Time</TableHead>
                        <TableHead>Comeback</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-4">
                            <div className="flex justify-center items-center space-x-2">
                              <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                              <span>Loading exit requests...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredExitRequests.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-4">
                            No exit requests found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredExitRequests.map((exit: any) => (
                          <TableRow key={exit.id}>
                            <TableCell>EX{String(exit.id).padStart(3, '0')}</TableCell>
                            <TableCell>
                              {exit.employee ?
                                `${exit.employee.firstName || ''} ${exit.employee.lastName || ''}` :
                                'Unknown'}
                            </TableCell>
                            <TableCell>{exit.exitType || '-'}</TableCell>
                            <TableCell>{exit.exitDate ? new Date(exit.exitDate).toLocaleDateString() : '-'}</TableCell>
                            <TableCell>{exit.exitTime || '-'}</TableCell>
                            <TableCell>{exit.notComeback ? 'No' : (exit.comebackTime || '-')}</TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                                exit.status === 'approved'
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                  : exit.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              }`}>
                                {exit.status.charAt(0).toUpperCase() + exit.status.slice(1)}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleViewExit(exit.id)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  {/* ADMIN dapat approve/reject semua exit request yang masih pending */}
                                  {userRole === "ADMIN" && exit.status === "pending" && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleApproveExit(exit.id)}>
                                        <Check className="mr-2 h-4 w-4" />
                                        Approve
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleRejectExit(exit.id)}>
                                        <X className="mr-2 h-4 w-4" />
                                        Reject
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleEditExit(exit.id)}>
                                        <Pencil className="mr-2 h-4 w-4" />
                                        Edit Permintaan
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleDeleteExit(exit.id)} className="text-red-600">
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Delete
                                      </DropdownMenuItem>
                                    </>
                                  )}

                                  {/* SUPERVISOR: Cek apakah exit request adalah milik bawahannya */}
                                  {userRole === "SUPERVISOR" && (
                                    <>
                                      {/* Jika exit request bukan milik supervisor sendiri (milik bawahan) dan masih pending */}
                                      {exit.employee &&
                                       `${exit.employee.firstName} ${exit.employee.lastName}` !== user?.name &&
                                       exit.status === "pending" && (
                                        <>
                                          <DropdownMenuItem onClick={() => handleApproveExit(exit.id)}>
                                            <Check className="mr-2 h-4 w-4" />
                                            Approve
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => handleRejectExit(exit.id)}>
                                            <X className="mr-2 h-4 w-4" />
                                            Reject
                                          </DropdownMenuItem>
                                        </>
                                      )}

                                      {/* Jika exit request milik supervisor sendiri dan masih pending */}
                                      {exit.employee &&
                                       `${exit.employee.firstName} ${exit.employee.lastName}` === user?.name &&
                                       exit.status === "pending" && (
                                        <DropdownMenuItem onClick={() => handleEditExit(exit.id)}>
                                          <Pencil className="mr-2 h-4 w-4" />
                                          Edit Permintaan Saya
                                        </DropdownMenuItem>
                                      )}
                                    </>
                                  )}

                                  {/* EMPLOYEE hanya dapat mengedit permintaan mereka sendiri yang masih pending */}
                                  {userRole === "EMPLOYEE" &&
                                   exit.status === "pending" && (
                                    <DropdownMenuItem onClick={() => handleEditExit(exit.id)}>
                                      <Pencil className="mr-2 h-4 w-4" />
                                      Edit Permintaan
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                  <div className="flex items-center justify-center py-4">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious href="#" />
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#" isActive>
                            1
                          </PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#">2</PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationLink href="#">3</PaginationLink>
                        </PaginationItem>
                        <PaginationItem>
                          <PaginationNext href="#" />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-4">
            <div className="flex flex-col lg:flex-row gap-6">
              <div className="bg-card text-card-foreground rounded-lg shadow-md p-6 w-fit">
                <div className="mb-4">
                  <h2 className="text-lg font-semibold">Leave Calendar</h2>
                  {userRole === 'EMPLOYEE' && (
                    <p className="text-xs text-muted-foreground">Showing your leave schedule</p>
                  )}
                  {userRole === 'SUPERVISOR' && (
                    <p className="text-xs text-muted-foreground">Showing department leave schedule</p>
                  )}
                  {userRole === 'ADMIN' && (
                    <p className="text-xs text-muted-foreground">Showing all leave schedules</p>
                  )}
                </div>
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  className="rounded-md border"
                  modifiers={modifiers}
                  modifiersStyles={modifiersStyles}
                />
              </div>
              <div className="bg-card text-card-foreground rounded-lg shadow-md p-6 flex-1">
                <h2 className="text-lg font-semibold mb-4">Leave Events</h2>
                <div className="h-[300px] overflow-hidden relative">
                  <div className="absolute inset-0 overflow-y-auto pr-2">
                    <div className="space-y-4">
                      {loading ? (
                        <div className="text-center py-8">
                          <div className="flex justify-center items-center space-x-2">
                            <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                            <span>Loading leave events...</span>
                          </div>
                        </div>
                      ) : leaveEvents.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          No leave events found for the selected date
                        </div>
                      ) : (
                        leaveEvents.map((event) => (
                          <div
                            key={event.id}
                            className={`p-3 rounded-lg border ${
                              event.status === 'approved'
                                ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                                : event.status === 'pending'
                                  ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
                                  : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                            }`}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{event.employee}</p>
                                <p className="text-sm text-gray-600">{event.type}</p>
                              </div>
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-semibold ${
                                  event.status === 'approved'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                    : event.status === 'pending'
                                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                                }`}
                              >
                                {event.status ? event.status.charAt(0).toUpperCase() + event.status.slice(1) : 'Unknown'}
                              </span>
                            </div>
                            <div className="mt-2 text-sm">
                              <div className="flex items-center">
                                <CalendarIcon className="h-3 w-3 mr-1 text-muted-foreground" />
                                <span>
                                  {event.from ? new Date(event.from).toLocaleDateString() : '-'} -
                                  {event.to ? new Date(event.to).toLocaleDateString() : '-'}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="leave-types" className="space-y-4">
            {userRole === "ADMIN" ? (
              <LeaveTypesSection />
            ) : (
              <div className="bg-card rounded-lg shadow-sm p-6">
                <div className="text-center py-8">
                  <h3 className="text-lg font-medium">Access Restricted</h3>
                  <p className="text-muted-foreground mt-2">
                    Only administrators can access leave types management.
                  </p>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
