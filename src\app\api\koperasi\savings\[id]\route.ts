/**
 * API Route: /api/koperasi/savings/[id]
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    const response = await fetch(new URL(`/api/koperasi/savings/${id}/get`, request.url), {
      method: 'GET',
      headers: request.headers
    });

    return response;
  } catch (error) {
    console.error('Error in GET /api/koperasi/savings/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    const response = await fetch(new URL(`/api/koperasi/savings/${id}/update`, request.url), {
      method: 'PUT',
      headers: request.headers,
      body: request.body
    });

    return response;
  } catch (error) {
    console.error('Error in PUT /api/koperasi/savings/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;
    const response = await fetch(new URL(`/api/koperasi/savings/${id}/delete`, request.url), {
      method: 'DELETE',
      headers: request.headers
    });

    return response;
  } catch (error) {
    console.error('Error in DELETE /api/koperasi/savings/[id]:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
