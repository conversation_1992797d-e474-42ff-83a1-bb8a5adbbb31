/**
 * API Route: PUT /api/leave-types/[id]/update
 *
 * Deskripsi: Memperbarui data tipe cuti berdasarkan ID
 * Penggunaan: Form edit tipe cuti
 *
 * Path Parameters:
 * - id: ID tipe cuti (number)
 *
 * Body:
 * - name: <PERSON><PERSON> tipe cuti (string)
 * - description: <PERSON><PERSON><PERSON><PERSON> tipe cuti (string, opsional)
 * - daysAllowed: <PERSON><PERSON><PERSON> hari yang di<PERSON> (number)
 * - requiresApproval: <PERSON><PERSON><PERSON><PERSON> (boolean, opsional)
 *
 * Response:
 * - 200: Tipe cuti berhasil diperbarui
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Tipe cuti tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function PUT(request: Request) {
  // Get the ID from the URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 2]; // Get the ID from the URL path

  try {
    // Verify admin access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const data = await request.json();

    // Validate required fields
    if (!data.name || !data.daysAllowed) {
      return NextResponse.json(
        { error: 'Name and days allowed are required' },
        { status: 400 }
      );
    }

    // Check if leave type exists
    const existingLeaveType = await prisma.leaveType.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingLeaveType) {
      return NextResponse.json({ error: 'Leave type not found' }, { status: 404 });
    }

    // Check if name is already used by another leave type
    const duplicateName = await prisma.leaveType.findFirst({
      where: {
        name: data.name,
        NOT: {
          id: parseInt(id)
        }
      }
    });

    if (duplicateName) {
      return NextResponse.json(
        { error: 'Leave type with this name already exists' },
        { status: 400 }
      );
    }

    // Explicitly construct the update data object
    const updateData = {
      name: data.name,
      description: data.description,
      daysAllowed: Number(data.daysAllowed), // Ensure this is a number
      requiresApproval: Boolean(data.requiresApproval) // Ensure this is a boolean
    };

    // Update leave type
    const updatedLeaveType = await prisma.leaveType.update({
      where: { id: parseInt(id) },
      data: updateData
    });

    return NextResponse.json(updatedLeaveType);
  } catch (error) {
    console.error('Error updating leave type:', error);
    return NextResponse.json(
      { error: 'Failed to update leave type' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
