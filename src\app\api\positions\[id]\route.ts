/**
 * API Route: /api/positions/[id]
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/db';
import { NextRequest } from 'next/server';



export async function DELETE(_request: NextRequest) {
  try {
    // Authentication check
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extract ID from URL path
    const id = _request.nextUrl.pathname.split('/').pop();
    const positionId = Number(id);

    if (isNaN(positionId)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    // Check if position has any employees
    const employeesCount = await prisma.employee.count({
      where: { positionId }
    });

    if (employeesCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete position that has associated employees' },
        { status: 400 }
      );
    }

    // If no employees are associated, proceed with deletion
    await prisma.position.delete({
      where: { id: positionId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting position:', error);
    return NextResponse.json(
      { error: 'Failed to delete position. It might be referenced by other records.' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Await params before accessing its properties
    // params is now directly available
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    // Extract ID from URL path
    const id = request.nextUrl.pathname.split('/').pop();
    const positionId = Number(id);

    if (isNaN(positionId)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    // Convert salary values to numbers or null if empty
    const minSalary = data.minSalary ? Number(data.minSalary) : null;
    const maxSalary = data.maxSalary ? Number(data.maxSalary) : null;

    const updatedPosition = await prisma.position.update({
      where: { id: positionId },
      data: {
        title: data.title,
        departmentId: data.departmentId,
        description: data.description,
        minSalary,
        maxSalary,
      },
      include: {
        department: {
          select: {
            name: true
          }
        }
      }
    });

    return NextResponse.json(updatedPosition);
  } catch (error) {
    console.error('Error updating position:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
