"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth";
import { But<PERSON> } from "@/components/ui/button";
import { logger } from "@/lib/logger";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Pencil, Lock } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import Navbar from "@/components/layout/Navbar";
import ProtectedRoute from "@/components/ProtectedRoute";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

// Data untuk dropdown
const religions = [
  "Islam",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "Hindu",
  "Buddha",
  "<PERSON><PERSON><PERSON>",
];

const provinces = [
  "Aceh",
  "Sumatera Utara",
  "Sumatera Barat",
  "Riau",
  "Jambi",
  "Sumatera Selatan",
  "Bengkulu",
  "Lampung",
  "Kepulauan Bangka Belitung",
  "Kepulauan Riau",
  "DKI Jakarta",
  "Jawa Barat",
  "Jawa Tengah",
  "DI Yogyakarta",
  "Jawa Timur",
  "Banten",
  "Bali",
  "Nusa Tenggara Barat",
  "Nusa Tenggara Timur",
  "Kalimantan Barat",
  "Kalimantan Tengah",
  "Kalimantan Selatan",
  "Kalimantan Timur",
  "Kalimantan Utara",
  "Sulawesi Utara",
  "Sulawesi Tengah",
  "Sulawesi Selatan",
  "Sulawesi Tenggara",
  "Gorontalo",
  "Sulawesi Barat",
  "Maluku",
  "Maluku Utara",
  "Papua",
  "Papua Barat",
];

const cities: Record<string, string[]> = {
  "Aceh": ["Banda Aceh", "Langsa", "Lhokseumawe", "Sabang", "Subulussalam", "Bireuen", "Aceh Besar", "Aceh Utara", "Aceh Selatan", "Aceh Barat", "Aceh Tengah", "Aceh Timur", "Aceh Jaya", "Aceh Tamiang", "Aceh Tenggara", "Bener Meriah", "Gayo Lues", "Nagan Raya", "Pidie", "Pidie Jaya", "Simeulue"],
  "Sumatera Utara": ["Medan", "Binjai", "Padang Sidempuan", "Pematangsiantar", "Sibolga", "Tanjungbalai", "Tebing Tinggi", "Gunungsitoli", "Asahan", "Batu Bara", "Dairi", "Deli Serdang", "Humbang Hasundutan", "Karo", "Labuhanbatu", "Labuhanbatu Selatan", "Labuhanbatu Utara", "Langkat", "Mandailing Natal", "Nias", "Nias Barat", "Nias Selatan", "Nias Utara", "Padang Lawas", "Padang Lawas Utara", "Pakpak Bharat", "Samosir", "Serdang Bedagai", "Simalungun", "Tapanuli Selatan", "Tapanuli Tengah", "Tapanuli Utara", "Toba Samosir"],
  "Sumatera Barat": ["Padang", "Bukittinggi", "Padang Panjang", "Payakumbuh", "Sawahlunto", "Solok", "Pariaman", "Agam", "Dharmasraya", "Kepulauan Mentawai", "Lima Puluh Kota", "Padang Pariaman", "Pasaman", "Pasaman Barat", "Pesisir Selatan", "Sijunjung", "Solok", "Solok Selatan", "Tanah Datar"],
  "Riau": ["Pekanbaru", "Dumai", "Bengkalis", "Indragiri Hilir", "Indragiri Hulu", "Kampar", "Kepulauan Meranti", "Kuantan Singingi", "Pelalawan", "Rokan Hilir", "Rokan Hulu", "Siak"],
  "Jambi": ["Jambi", "Sungai Penuh", "Batanghari", "Bungo", "Kerinci", "Merangin", "Muaro Jambi", "Sarolangun", "Tanjung Jabung Barat", "Tanjung Jabung Timur", "Tebo"],
  "Sumatera Selatan": ["Palembang", "Lubuklinggau", "Pagar Alam", "Prabumulih", "Banyuasin", "Empat Lawang", "Lahat", "Muara Enim", "Musi Banyuasin", "Musi Rawas", "Musi Rawas Utara", "Ogan Ilir", "Ogan Komering Ilir", "Ogan Komering Ulu", "Ogan Komering Ulu Selatan", "Ogan Komering Ulu Timur", "Penukal Abab Lematang Ilir"],
  "Bengkulu": ["Bengkulu", "Bengkulu Selatan", "Bengkulu Tengah", "Bengkulu Utara", "Kaur", "Kepahiang", "Lebong", "Mukomuko", "Rejang Lebong", "Seluma"],
  "Lampung": ["Bandar Lampung", "Metro", "Lampung Barat", "Lampung Selatan", "Lampung Tengah", "Lampung Timur", "Lampung Utara", "Mesuji", "Pesawaran", "Pesisir Barat", "Pringsewu", "Tanggamus", "Tulang Bawang", "Tulang Bawang Barat", "Way Kanan"],
  "Kepulauan Bangka Belitung": ["Pangkal Pinang", "Bangka", "Bangka Barat", "Bangka Selatan", "Bangka Tengah", "Belitung", "Belitung Timur"],
  "Kepulauan Riau": ["Tanjung Pinang", "Batam", "Bintan", "Karimun", "Kepulauan Anambas", "Lingga", "Natuna"],
  "DKI Jakarta": ["Jakarta Pusat", "Jakarta Utara", "Jakarta Barat", "Jakarta Selatan", "Jakarta Timur", "Kepulauan Seribu"],
  "Jawa Barat": ["Bandung", "Bekasi", "Bogor", "Depok", "Cimahi", "Tasikmalaya", "Sukabumi", "Banjar", "Cirebon", "Bandung Barat", "Ciamis", "Cianjur", "Cirebon", "Garut", "Indramayu", "Karawang", "Kuningan", "Majalengka", "Pangandaran", "Purwakarta", "Subang", "Sumedang", "Tasikmalaya"],
  "Jawa Tengah": ["Semarang", "Surakarta", "Salatiga", "Pekalongan", "Tegal", "Magelang", "Banjarnegara", "Banyumas", "Batang", "Blora", "Boyolali", "Brebes", "Cilacap", "Demak", "Grobogan", "Jepara", "Karanganyar", "Kebumen", "Kendal", "Klaten", "Kudus", "Magelang", "Pati", "Pekalongan", "Pemalang", "Purbalingga", "Purworejo", "Rembang", "Semarang", "Sragen", "Sukoharjo", "Tegal", "Temanggung", "Wonogiri", "Wonosobo"],
  "DI Yogyakarta": ["Yogyakarta", "Bantul", "Gunungkidul", "Kulon Progo", "Sleman"],
  "Jawa Timur": ["Surabaya", "Malang", "Kediri", "Madiun", "Mojokerto", "Blitar", "Pasuruan", "Probolinggo", "Batu", "Bangkalan", "Banyuwangi", "Blitar", "Bojonegoro", "Bondowoso", "Gresik", "Jember", "Jombang", "Kediri", "Lamongan", "Lumajang", "Madiun", "Magetan", "Malang", "Mojokerto", "Nganjuk", "Ngawi", "Pacitan", "Pamekasan", "Pasuruan", "Ponorogo", "Probolinggo", "Sampang", "Sidoarjo", "Situbondo", "Sumenep", "Trenggalek", "Tuban", "Tulungagung"],
  "Banten": ["Serang", "Tangerang", "Cilegon", "Tangerang Selatan", "Lebak", "Pandeglang", "Serang", "Tangerang"],
  "Bali": ["Denpasar", "Badung", "Bangli", "Buleleng", "Gianyar", "Jembrana", "Karangasem", "Klungkung", "Tabanan"],
  "Nusa Tenggara Barat": ["Mataram", "Bima", "Bima", "Dompu", "Lombok Barat", "Lombok Tengah", "Lombok Timur", "Lombok Utara", "Sumbawa", "Sumbawa Barat"],
  "Nusa Tenggara Timur": ["Kupang", "Alor", "Belu", "Ende", "Flores Timur", "Kupang", "Lembata", "Malaka", "Manggarai", "Manggarai Barat", "Manggarai Timur", "Nagekeo", "Ngada", "Rote Ndao", "Sabu Raijua", "Sikka", "Sumba Barat", "Sumba Barat Daya", "Sumba Tengah", "Sumba Timur", "Timor Tengah Selatan", "Timor Tengah Utara"],
  "Kalimantan Barat": ["Pontianak", "Singkawang", "Bengkayang", "Kapuas Hulu", "Kayong Utara", "Ketapang", "Kubu Raya", "Landak", "Melawi", "Mempawah", "Sambas", "Sanggau", "Sekadau", "Sintang"],
  "Kalimantan Tengah": ["Palangka Raya", "Barito Selatan", "Barito Timur", "Barito Utara", "Gunung Mas", "Kapuas", "Katingan", "Kotawaringin Barat", "Kotawaringin Timur", "Lamandau", "Murung Raya", "Pulang Pisau", "Sukamara", "Seruyan"],
  "Kalimantan Selatan": ["Banjarmasin", "Banjarbaru", "Balangan", "Banjar", "Barito Kuala", "Hulu Sungai Selatan", "Hulu Sungai Tengah", "Hulu Sungai Utara", "Kotabaru", "Tabalong", "Tanah Bumbu", "Tanah Laut", "Tapin"],
  "Kalimantan Timur": ["Samarinda", "Balikpapan", "Bontang", "Berau", "Kutai Barat", "Kutai Kartanegara", "Kutai Timur", "Mahakam Ulu", "Paser", "Penajam Paser Utara"],
  "Kalimantan Utara": ["Tarakan", "Bulungan", "Malinau", "Nunukan", "Tana Tidung"],
  "Sulawesi Utara": ["Manado", "Bitung", "Kotamobagu", "Tomohon", "Bolaang Mongondow", "Bolaang Mongondow Selatan", "Bolaang Mongondow Timur", "Bolaang Mongondow Utara", "Kepulauan Sangihe", "Kepulauan Siau Tagulandang Biaro", "Kepulauan Talaud", "Minahasa", "Minahasa Selatan", "Minahasa Tenggara", "Minahasa Utara"],
  "Sulawesi Tengah": ["Palu", "Banggai", "Banggai Kepulauan", "Banggai Laut", "Buol", "Donggala", "Morowali", "Morowali Utara", "Parigi Moutong", "Poso", "Sigi", "Tojo Una-Una", "Tolitoli"],
  "Sulawesi Selatan": ["Makassar", "Palopo", "Parepare", "Bantaeng", "Barru", "Bone", "Bulukumba", "Enrekang", "Gowa", "Jeneponto", "Kepulauan Selayar", "Luwu", "Luwu Timur", "Luwu Utara", "Maros", "Pangkajene dan Kepulauan", "Pinrang", "Sidenreng Rappang", "Sinjai", "Soppeng", "Takalar", "Tana Toraja", "Toraja Utara", "Wajo"],
  "Sulawesi Tenggara": ["Kendari", "Baubau", "Bombana", "Buton", "Buton Selatan", "Buton Tengah", "Buton Utara", "Kolaka", "Kolaka Timur", "Kolaka Utara", "Konawe", "Konawe Kepulauan", "Konawe Selatan", "Konawe Utara", "Muna", "Muna Barat", "Wakatobi"],
  "Gorontalo": ["Gorontalo", "Boalemo", "Bone Bolango", "Gorontalo", "Gorontalo Utara", "Pohuwato"],
  "Sulawesi Barat": ["Mamuju", "Majene", "Mamasa", "Mamuju Tengah", "Mamuju Utara", "Polewali Mandar"],
  "Maluku": ["Ambon", "Tual", "Buru", "Buru Selatan", "Kepulauan Aru", "Maluku Barat Daya", "Maluku Tengah", "Maluku Tenggara", "Maluku Tenggara Barat", "Seram Bagian Barat", "Seram Bagian Timur"],
  "Maluku Utara": ["Ternate", "Tidore Kepulauan", "Halmahera Barat", "Halmahera Tengah", "Halmahera Timur", "Halmahera Selatan", "Halmahera Utara", "Kepulauan Sula", "Pulau Morotai", "Pulau Taliabu"],
  "Papua": ["Jayapura", "Asmat", "Biak Numfor", "Boven Digoel", "Deiyai", "Dogiyai", "Intan Jaya", "Jayapura", "Jayawijaya", "Keerom", "Kepulauan Yapen", "Lanny Jaya", "Mamberamo Raya", "Mamberamo Tengah", "Mappi", "Merauke", "Mimika", "Nabire", "Nduga", "Paniai", "Pegunungan Bintang", "Puncak", "Puncak Jaya", "Sarmi", "Supiori", "Tolikara", "Waropen", "Yahukimo", "Yalimo"],
  "Papua Barat": ["Manokwari", "Sorong", "Fakfak", "Kaimana", "Manokwari", "Manokwari Selatan", "Maybrat", "Pegunungan Arfak", "Raja Ampat", "Sorong", "Sorong Selatan", "Tambrauw", "Teluk Bintuni", "Teluk Wondama"],
};

const banks = [
  "Bank Mandiri",
  "BCA (Bank Central Asia)",
  "BRI (Bank Rakyat Indonesia)",
  "BNI (Bank Negara Indonesia)",
  "BTN (Bank Tabungan Negara)",
  "CIMB Niaga",
  "Bank Permata",
  "Bank Danamon",
  "Bank Syariah Indonesia",
  "Bank OCBC NISP",
  "Bank Panin",
  "Bank Maybank Indonesia",
  "Bank BTPN",
  "Bank Mega",
  "Bank DBS Indonesia",
  "Bank UOB Indonesia",
  "Bank HSBC Indonesia",
  "Bank Commonwealth",
  "Bank Sinarmas",
  "Bank Bukopin",
];

interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  birthDate: string;
  birthPlace: string;
  gender: string;
  religion: string;
  maritalStatus: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  identityType: string;
  identityNumber: string;
  npwp: string;
  bpjsKesehatan: string;
  bpjsKetenagakerjaan: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  bankName: string;
  bankAccount: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function MyProfilePage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [employeeData, setEmployeeData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("profile");
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordFormData, setPasswordFormData] = useState<PasswordFormData>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [formData, setFormData] = useState<ProfileFormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    postalCode: "",
    country: "",
    birthDate: "",
    birthPlace: "",
    gender: "",
    religion: "",
    maritalStatus: "",
    identityType: "",
    identityNumber: "",
    npwp: "",
    bpjsKesehatan: "",
    bpjsKetenagakerjaan: "",
    emergencyContactName: "",
    emergencyContactPhone: "",
    bankName: "",
    bankAccount: "",
  });

  // Effect untuk mengupdate daftar kota saat provinsi berubah
  useEffect(() => {
    if (formData.state) {
      const citiesForProvince = cities[formData.state] || [];
      setAvailableCities(citiesForProvince);
      logger.debug(`Province changed to: ${formData.state}`);
      logger.debug(`Available cities count: ${citiesForProvince.length}`);

      // Periksa apakah kota yang tersimpan ada dalam daftar
      if (formData.city && !citiesForProvince.includes(formData.city)) {
        // Jika kota tidak ada dalam daftar, reset nilai city
        setFormData(prev => ({
          ...prev,
          city: ''
        }));
      }
    } else {
      // Jika tidak ada provinsi yang dipilih, kosongkan daftar kota
      setAvailableCities([]);
    }
  }, [formData.state]);

  useEffect(() => {
    const fetchEmployeeData = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        logger.debug('Fetching employee data');
        const response = await fetch(`/api/employees/${user.id}`, {
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch employee data');
        }

        const data = await response.json();
        logger.debug('Employee data received successfully');

        setEmployeeData(data);
        setFormData({
          firstName: data.firstName || "",
          lastName: data.lastName || "",
          email: data.email || "",
          phone: data.phone || "",
          address: data.address || "",
          city: data.city || "",
          state: data.state || "",
          postalCode: data.postalCode || "",
          country: data.country || "",
          birthDate: data.birthDate ? data.birthDate.split('T')[0] : "",
          birthPlace: data.birthPlace || "",
          gender: data.gender || "",
          religion: data.religion || "",
          maritalStatus: data.maritalStatus || "",
          identityType: data.identityType || "",
          identityNumber: data.identityNumber || "",
          npwp: data.npwp || "",
          bpjsKesehatan: data.bpjsKesehatan || "",
          bpjsKetenagakerjaan: data.bpjsKetenagakerjaan || "",
          emergencyContactName: data.emergencyContactName || "",
          emergencyContactPhone: data.emergencyContactPhone || "",
          bankName: data.bankName || "",
          bankAccount: data.bankAccount || "",
        });
      } catch (error) {
        logger.error('Error fetching employee data:', error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to fetch employee data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchEmployeeData();
  }, [user?.id, toast]);

  if (loading || !employeeData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // Pastikan value selalu terdefinisi
    const safeValue = value || "";
    setFormData(prev => ({
      ...prev,
      [name]: safeValue
    }));
  };



  const handleSelectChange = (name: string, value: string) => {
    // Pastikan value selalu terdefinisi
    const safeValue = value || "";

    // Update form data dengan nilai baru
    setFormData(prev => ({
      ...prev,
      [name]: safeValue
    }));

    // Jika provinsi berubah, update daftar kota
    if (name === 'state') {
      console.log("handleSelectChange - Province changed to:", safeValue);
      const citiesForProvince = safeValue ? (cities[safeValue] || []) : [];
      console.log("handleSelectChange - Available cities:", citiesForProvince);
      setAvailableCities(citiesForProvince);

      // Reset nilai kota jika provinsi berubah
      setFormData(prev => ({
        ...prev,
        city: ''
      }));
    }
  };

  const handlePasswordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordLoading(true);

    // Validate passwords
    if (passwordFormData.newPassword !== passwordFormData.confirmPassword) {
      toast({
        title: "Error",
        description: "New password and confirm password do not match",
        variant: "destructive",
      });
      setPasswordLoading(false);
      return;
    }

    if (passwordFormData.newPassword.length < 6) {
      toast({
        title: "Error",
        description: "New password must be at least 6 characters long",
        variant: "destructive",
      });
      setPasswordLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(passwordFormData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Password changed successfully",
        });
        // Reset form
        setPasswordFormData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
      } else {
        toast({
          title: "Error",
          description: data.message || "Failed to change password",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setPasswordLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch(`/api/employees/${user?.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to update profile');

      // Get the updated data from the response
      const updatedData = await response.json();

      // Update both states with the new data
      setEmployeeData(updatedData);
      setFormData({
        firstName: updatedData.firstName || "",
        lastName: updatedData.lastName || "",
        email: updatedData.email || "",
        phone: updatedData.phone || "",
        address: updatedData.address || "",
        city: updatedData.city || "",
        state: updatedData.state || "",
        postalCode: updatedData.postalCode || "",
        country: updatedData.country || "",
        birthDate: updatedData.birthDate ? updatedData.birthDate.split('T')[0] : "",
        birthPlace: updatedData.birthPlace || "",
        gender: updatedData.gender || "",
        religion: updatedData.religion || "",
        maritalStatus: updatedData.maritalStatus || "",
        identityType: updatedData.identityType || "",
        identityNumber: updatedData.identityNumber || "",
        npwp: updatedData.npwp || "",
        bpjsKesehatan: updatedData.bpjsKesehatan || "",
        bpjsKetenagakerjaan: updatedData.bpjsKetenagakerjaan || "",
        emergencyContactName: updatedData.emergencyContactName || "",
        emergencyContactPhone: updatedData.emergencyContactPhone || "",
        bankName: updatedData.bankName || "",
        bankAccount: updatedData.bankAccount || "",
      });

      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
      setIsEditing(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-8">My Profile</h1>

          <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab} className="mb-6">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="password">Change Password</TabsTrigger>
            </TabsList>

            <TabsContent value="profile">
              <div className="grid gap-6">
            {/* Employment Details Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold">Employment Details</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Employee ID</Label>
                    <div className="mt-1">{employeeData.employeeId}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Department</Label>
                    <div className="mt-1">{employeeData.department?.name || '-'}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Position</Label>
                    <div className="mt-1">{employeeData.position?.title || '-'}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Role</Label>
                    <div className="mt-1 capitalize">{employeeData.user?.role || '-'}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Status</Label>
                    <div className="mt-1">{employeeData.status || '-'}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Hire Date</Label>
                    <div className="mt-1">
                      {employeeData.hireDate
                        ? new Date(employeeData.hireDate).toLocaleDateString()
                        : '-'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Personal Information Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xl font-bold">Personal Information</CardTitle>
                <div className="flex items-center gap-2">
                  {employeeData.user && (
                    <Badge variant="outline" className="capitalize">
                      {employeeData.user.role}
                    </Badge>
                  )}
                  <Dialog open={isEditing} onOpenChange={setIsEditing}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Pencil className="h-4 w-4 mr-2" />
                        Edit Profile
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Edit Profile</DialogTitle>
                      </DialogHeader>
                      <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Basic Information */}
                        <div>
                          <h3 className="text-sm font-semibold mb-3">Basic Information</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                              <Label htmlFor="firstName">First Name</Label>
                              <Input
                                id="firstName"
                                name="firstName"
                                value={formData.firstName}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="lastName">Last Name</Label>
                              <Input
                                id="lastName"
                                name="lastName"
                                value={formData.lastName}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="email">Email</Label>
                              <Input
                                id="email"
                                name="email"
                                type="email"
                                value={formData.email}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="phone">Phone</Label>
                              <Input
                                id="phone"
                                name="phone"
                                value={formData.phone}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Personal Details */}
                        <div>
                          <h3 className="text-sm font-semibold mb-3">Personal Details</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                              <Label htmlFor="birthDate">Birth Date</Label>
                              <Input
                                id="birthDate"
                                name="birthDate"
                                type="date"
                                value={formData.birthDate}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="birthPlace">Birth Place</Label>
                              <Input
                                id="birthPlace"
                                name="birthPlace"
                                value={formData.birthPlace}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="gender">Gender</Label>
                              <Select name="gender" value={formData.gender} onValueChange={(value) => handleInputChange({ target: { name: 'gender', value }})}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select gender" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="male">Male</SelectItem>
                                  <SelectItem value="female">Female</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="religion">Religion</Label>
                              <Select name="religion" value={formData.religion} onValueChange={(value) => handleSelectChange('religion', value)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select religion" />
                                </SelectTrigger>
                                <SelectContent>
                                  {religions.map((religion) => (
                                    <SelectItem key={religion} value={religion}>{religion}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="maritalStatus">Marital Status</Label>
                              <Select name="maritalStatus" value={formData.maritalStatus} onValueChange={(value) => handleInputChange({ target: { name: 'maritalStatus', value }})}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="single">Single</SelectItem>
                                  <SelectItem value="married">Married</SelectItem>
                                  <SelectItem value="divorced">Divorced</SelectItem>
                                  <SelectItem value="widowed">Widowed</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>

                        {/* Address Information */}
                        <div>
                          <h3 className="text-sm font-semibold mb-3">Address Information</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2 col-span-2">
                              <Label htmlFor="address">Address</Label>
                              <Input
                                id="address"
                                name="address"
                                value={formData.address}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="state">State/Province</Label>
                              <Select name="state" value={formData.state} onValueChange={(value) => handleSelectChange('state', value)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select province" />
                                </SelectTrigger>
                                <SelectContent>
                                  {provinces.map((province) => (
                                    <SelectItem key={province} value={province}>{province}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="city">City</Label>
                              <Select
                                name="city"
                                value={formData.city || ""}
                                onValueChange={(value) => handleSelectChange('city', value)}
                                disabled={availableCities.length === 0}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder={availableCities.length === 0 ? "Select province first" : "Select city"} />
                                </SelectTrigger>
                                <SelectContent>
                                  {availableCities.map((city) => (
                                    <SelectItem key={city} value={city}>{city}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="postalCode">Postal Code</Label>
                              <Input
                                id="postalCode"
                                name="postalCode"
                                value={formData.postalCode || ""}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="country">Country</Label>
                              <Input
                                id="country"
                                name="country"
                                value={formData.country || ""}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Identity & Documents */}
                        <div>
                          <h3 className="text-sm font-semibold mb-3">Identity & Documents</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                              <Label htmlFor="identityType">Identity Type</Label>
                              <Select name="identityType" value={formData.identityType} onValueChange={(value) => handleInputChange({ target: { name: 'identityType', value }})}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="ktp">KTP</SelectItem>
                                  <SelectItem value="passport">Passport</SelectItem>
                                  <SelectItem value="sim">SIM</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="identityNumber">Identity Number</Label>
                              <Input
                                id="identityNumber"
                                name="identityNumber"
                                value={formData.identityNumber}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="npwp">NPWP</Label>
                              <Input
                                id="npwp"
                                name="npwp"
                                value={formData.npwp}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="bpjsKesehatan">BPJS Kesehatan</Label>
                              <Input
                                id="bpjsKesehatan"
                                name="bpjsKesehatan"
                                value={formData.bpjsKesehatan}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="bpjsKetenagakerjaan">BPJS Ketenagakerjaan</Label>
                              <Input
                                id="bpjsKetenagakerjaan"
                                name="bpjsKetenagakerjaan"
                                value={formData.bpjsKetenagakerjaan}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Emergency Contact */}
                        <div>
                          <h3 className="text-sm font-semibold mb-3">Emergency Contact</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                              <Label htmlFor="emergencyContactName">Contact Name</Label>
                              <Input
                                id="emergencyContactName"
                                name="emergencyContactName"
                                value={formData.emergencyContactName}
                                onChange={handleInputChange}
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="emergencyContactPhone">Contact Phone</Label>
                              <Input
                                id="emergencyContactPhone"
                                name="emergencyContactPhone"
                                value={formData.emergencyContactPhone}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Bank Information */}
                        <div>
                          <h3 className="text-sm font-semibold mb-3">Bank Information</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                              <Label htmlFor="bankName">Bank Name</Label>
                              <Select name="bankName" value={formData.bankName} onValueChange={(value) => handleSelectChange('bankName', value)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select bank" />
                                </SelectTrigger>
                                <SelectContent>
                                  {banks.map((bank) => (
                                    <SelectItem key={bank} value={bank}>{bank}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="bankAccount">Account Number</Label>
                              <Input
                                id="bankAccount"
                                name="bankAccount"
                                value={formData.bankAccount}
                                onChange={handleInputChange}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="flex justify-end gap-4">
                          <Button variant="outline" type="button" onClick={() => setIsEditing(false)}>
                            Cancel
                          </Button>
                          <Button type="submit">Save Changes</Button>
                        </div>
                      </form>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="rounded-lg border bg-muted/50 p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Badge variant="secondary" className="px-2 py-0.5">
                        Basic Information
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">First Name</Label>
                        <div className="mt-1">{employeeData.firstName || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Last Name</Label>
                        <div className="mt-1">{employeeData.lastName || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Email</Label>
                        <div className="mt-1">{employeeData.email || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Phone</Label>
                        <div className="mt-1">{employeeData.phone || '-'}</div>
                      </div>
                    </div>
                  </div>

                  {/* Personal Details */}
                  <div className="rounded-lg border bg-blue-50 dark:bg-blue-950/30 p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Badge variant="secondary" className="px-2 py-0.5">
                        Personal Details
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">Birth Date</Label>
                        <div className="mt-1">
                          {employeeData.birthDate ? new Date(employeeData.birthDate).toLocaleDateString() : '-'}
                        </div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Birth Place</Label>
                        <div className="mt-1">{employeeData.birthPlace || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Gender</Label>
                        <div className="mt-1 capitalize">{employeeData.gender || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Religion</Label>
                        <div className="mt-1">{employeeData.religion || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Marital Status</Label>
                        <div className="mt-1 capitalize">{employeeData.maritalStatus || '-'}</div>
                      </div>
                    </div>
                  </div>

                  {/* Address Information */}
                  <div className="rounded-lg border bg-green-50 dark:bg-green-950/30 p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Badge variant="secondary" className="px-2 py-0.5">
                        Address Information
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="col-span-2">
                        <Label className="text-sm font-medium">Address</Label>
                        <div className="mt-1">{employeeData.address || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">City</Label>
                        <div className="mt-1">{employeeData.city || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">State/Province</Label>
                        <div className="mt-1">{employeeData.state || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Postal Code</Label>
                        <div className="mt-1">{employeeData.postalCode || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Country</Label>
                        <div className="mt-1">{employeeData.country || '-'}</div>
                      </div>
                    </div>
                  </div>

                  {/* Identity & Documents */}
                  <div className="rounded-lg border bg-purple-50 dark:bg-purple-950/30 p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Badge variant="secondary" className="px-2 py-0.5">
                        Identity & Documents
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">Identity Type</Label>
                        <div className="mt-1 capitalize">{employeeData.identityType || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Identity Number</Label>
                        <div className="mt-1">{employeeData.identityNumber || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">NPWP</Label>
                        <div className="mt-1">{employeeData.npwp || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">BPJS Kesehatan</Label>
                        <div className="mt-1">{employeeData.bpjsKesehatan || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">BPJS Ketenagakerjaan</Label>
                        <div className="mt-1">{employeeData.bpjsKetenagakerjaan || '-'}</div>
                      </div>
                    </div>
                  </div>

                  {/* Emergency Contact */}
                  <div className="rounded-lg border bg-orange-50 dark:bg-orange-950/30 p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Badge variant="secondary" className="px-2 py-0.5">
                        Emergency Contact
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">Contact Name</Label>
                        <div className="mt-1">{employeeData.emergencyContactName || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Contact Phone</Label>
                        <div className="mt-1">{employeeData.emergencyContactPhone || '-'}</div>
                      </div>
                    </div>
                  </div>

                  {/* Bank Information */}
                  <div className="rounded-lg border bg-yellow-50 dark:bg-yellow-950/30 p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Badge variant="secondary" className="px-2 py-0.5">
                        Bank Information
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">Bank Name</Label>
                        <div className="mt-1">{employeeData.bankName || '-'}</div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Account Number</Label>
                        <div className="mt-1">{employeeData.bankAccount || '-'}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
            </TabsContent>

            <TabsContent value="password">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl font-bold">Change Password</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleChangePassword} className="space-y-6">
                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="currentPassword">Current Password</Label>
                        <Input
                          id="currentPassword"
                          name="currentPassword"
                          type="password"
                          value={passwordFormData.currentPassword}
                          onChange={handlePasswordInputChange}
                          required
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="newPassword">New Password</Label>
                        <Input
                          id="newPassword"
                          name="newPassword"
                          type="password"
                          value={passwordFormData.newPassword}
                          onChange={handlePasswordInputChange}
                          required
                        />
                        <p className="text-xs text-muted-foreground">Password must be at least 6 characters long</p>
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="confirmPassword">Confirm New Password</Label>
                        <Input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          value={passwordFormData.confirmPassword}
                          onChange={handlePasswordInputChange}
                          required
                        />
                      </div>
                    </div>

                    <Button type="submit" className="w-full" disabled={passwordLoading}>
                      {passwordLoading ? (
                        <>
                          <span className="mr-2">Changing Password...</span>
                          <span className="animate-spin">⏳</span>
                        </>
                      ) : (
                        <>
                          <Lock className="mr-2 h-4 w-4" />
                          Change Password
                        </>
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </ProtectedRoute>
  );
}












