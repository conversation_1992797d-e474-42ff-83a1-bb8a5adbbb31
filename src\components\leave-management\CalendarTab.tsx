import { useState } from "react";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";

interface CalendarTabProps {
  leaveEvents: any[];
  loading: boolean;
  userRole?: string;
  modifiers: any;
  modifiersStyles: any;
}

export function CalendarTab({
  leaveEvents,
  loading,
  userRole,
  modifiers,
  modifiersStyles
}: CalendarTabProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());

  // Filter events for the selected date (for highlighting in calendar)
  console.log('CalendarTab - leaveEvents:', leaveEvents);
  const eventsForSelectedDate = selectedDate
    ? leaveEvents.filter(event => {
        console.log('Checking event:', event);
        if (!event.from || !event.to) {
          console.log('Event missing from or to date:', event);
          return false;
        }
        const eventStart = new Date(event.from);
        eventStart.setHours(0, 0, 0, 0);
        const eventEnd = new Date(event.to);
        eventEnd.setHours(0, 0, 0, 0);
        const checkDate = new Date(selectedDate);
        checkDate.setHours(0, 0, 0, 0);
        const isInRange = checkDate >= eventStart && checkDate <= eventEnd;
        console.log('Event in range:', isInRange, 'Start:', eventStart, 'End:', eventEnd, 'Check:', checkDate);
        return isInRange;
      })
    : [];

  // Tampilkan semua event, tidak hanya yang sesuai dengan tanggal yang dipilih
  const allEvents = leaveEvents;
  console.log('Events for selected date:', eventsForSelectedDate);

  return (
    <div className="bg-card rounded-lg shadow-sm p-6">
      <div className="flex flex-col lg:flex-row gap-6">
        <div className="lg:w-auto">
          <div className="bg-card text-card-foreground rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">Calendar View</h2>
            {userRole === 'SUPERVISOR' && (
              <p className="text-xs text-muted-foreground">Showing department leave schedule</p>
            )}
            <CalendarComponent
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border"
              modifiers={modifiers}
              modifiersStyles={modifiersStyles}
            />
          </div>
        </div>
        <div className="bg-card text-card-foreground rounded-lg shadow-md p-6 flex-1">
          <h2 className="text-lg font-semibold mb-4">Leave Events</h2>
          <div className="h-[400px] overflow-hidden relative">
            <div className="absolute inset-0 overflow-y-auto pr-2">
              <div className="space-y-4">
                {loading ? (
                  <div className="text-center py-8">
                    <div className="flex justify-center items-center space-x-2">
                      <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                      <span>Loading leave events...</span>
                    </div>
                  </div>
                ) : allEvents.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No leave events found
                  </div>
                ) : (
                  allEvents.map((event) => (
                    <div
                      key={event.id}
                      className={`p-3 rounded-lg border mb-3 hover:shadow-md transition-shadow ${
                        event.status === 'approved'
                          ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                          : event.status === 'pending'
                            ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
                            : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{event.employee}</p>
                          <p className="text-sm text-gray-600">{event.type}</p>
                        </div>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-semibold ${
                            event.status === 'approved'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              : event.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                          }`}
                        >
                          {event.status ? event.status.charAt(0).toUpperCase() + event.status.slice(1) : 'Unknown'}
                        </span>
                      </div>
                      <div className="mt-2 text-sm">
                        <div className="flex items-center">
                          <CalendarIcon className="h-3 w-3 mr-1 text-muted-foreground" />
                          <span>
                            {event.from ? new Date(event.from).toLocaleDateString('id-ID', { day: 'numeric', month: 'numeric', year: 'numeric' }) : '-'} -
                            {event.to ? new Date(event.to).toLocaleDateString('id-ID', { day: 'numeric', month: 'numeric', year: 'numeric' }) : '-'}
                          </span>
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground">
                          {Math.ceil((new Date(event.to).getTime() - new Date(event.from).getTime()) / (1000 * 60 * 60 * 24)) + 1} hari
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
