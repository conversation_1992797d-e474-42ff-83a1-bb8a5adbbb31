/**
 * API Route: PUT /api/positions/[id]/update
 *
 * Deskripsi: Memperbarui data posisi berdasarkan ID
 * Penggunaan: Form edit posisi
 *
 * Path Parameters:
 * - id: ID posisi (number)
 *
 * Body:
 * - title: <PERSON><PERSON><PERSON> posisi (string)
 * - departmentId: ID departemen (number)
 * - description: <PERSON><PERSON><PERSON><PERSON> posisi (string, opsional)
 * - minSalary: <PERSON><PERSON><PERSON> minimum (number, opsional)
 * - maxSalary: <PERSON><PERSON><PERSON> (number, opsional)
 *
 * Response:
 * - 200: Posisi berhasil diperbarui
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Posisi tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/db';



export async function PUT(request: Request) {
  // Get the ID from the URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 2]; // Get the ID from the URL path

  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const positionId = Number(id);

    if (isNaN(positionId)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    // Validasi data yang diperlukan
    if (!data.title || !data.departmentId) {
      return NextResponse.json(
        { error: 'Title and department ID are required' },
        { status: 400 }
      );
    }

    // Cek apakah posisi ada
    const existingPosition = await prisma.position.findUnique({
      where: { id: positionId }
    });

    if (!existingPosition) {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    // Convert salary values to numbers or null if empty
    const minSalary = data.minSalary ? Number(data.minSalary) : null;
    const maxSalary = data.maxSalary ? Number(data.maxSalary) : null;

    // Validasi range gaji
    if (minSalary !== null && maxSalary !== null && minSalary > maxSalary) {
      return NextResponse.json(
        { error: 'Minimum salary cannot be greater than maximum salary' },
        { status: 400 }
      );
    }

    const updatedPosition = await prisma.position.update({
      where: { id: positionId },
      data: {
        title: data.title,
        departmentId: data.departmentId,
        description: data.description,
        minSalary,
        maxSalary,
      },
      include: {
        department: {
          select: {
            name: true
          }
        }
      }
    });

    return NextResponse.json(updatedPosition);
  } catch (error) {
    console.error('Error updating position:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
