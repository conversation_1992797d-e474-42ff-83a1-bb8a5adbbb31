import { useState } from 'react';
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2, Eye } from "lucide-react";
import * as XLSX from 'xlsx';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ImportStatus {
  total: number;
  current: number;
  imported: number;
  errors: number;
  isImporting: boolean;
  isPreviewMode: boolean;
}

interface ImportSalaryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportSuccess: () => void;
  type: 'staff' | 'guru' | 'honor';
}

export function ImportSalaryDialog({
  open,
  onOpenChange,
  onImportSuccess,
  type
}: ImportSalaryDialogProps) {
  const [importStatus, setImportStatus] = useState<ImportStatus>({
    total: 0,
    current: 0,
    imported: 0,
    errors: 0,
    isImporting: false,
    isPreviewMode: false
  });

  const [errorMessages, setErrorMessages] = useState<string[]>([]);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<string>("template");

  const readExcel = async (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // Look for the Template sheet first
          let sheetName = '';
          if (workbook.SheetNames.includes('Template')) {
            sheetName = 'Template';
          } else if (workbook.SheetNames.length > 0) {
            // If no Template sheet, use the last sheet (assuming it's the data sheet)
            // This handles both our template format and user-created files
            sheetName = workbook.SheetNames[workbook.SheetNames.length - 1];
          } else {
            throw new Error('No sheets found in Excel file');
          }

          console.log(`Reading data from sheet: ${sheetName}`);
          const worksheet = workbook.Sheets[sheetName];

          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = (error) => reject(error);
      reader.readAsArrayBuffer(file);
    });
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setImportStatus({
        total: 0,
        current: 0,
        imported: 0,
        errors: 0,
        isImporting: false,
        isPreviewMode: true
      });
      setErrorMessages([]);
      setActiveTab("preview");

      const data = await readExcel(file);

      if (!data || data.length === 0) {
        throw new Error('No data found in Excel file');
      }

      // Store the preview data
      setPreviewData(data);

      setImportStatus(prev => ({
        ...prev,
        total: data.length,
      }));

    } catch (error) {
      console.error('Preview error:', error);
      setErrorMessages(prev => [...prev, 'Preview process failed: ' + (error as Error).message]);
      setImportStatus(prev => ({
        ...prev,
        isPreviewMode: false
      }));
    } finally {
      event.target.value = '';
    }
  };

  const handleImport = async () => {
    try {
      if (previewData.length === 0) {
        throw new Error('No data to import');
      }

      setImportStatus(prev => ({
        ...prev,
        isImporting: true,
        isPreviewMode: false
      }));

      // Convert preview data back to Excel file
      const worksheet = XLSX.utils.json_to_sheet(previewData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

      // Create a Blob from the buffer
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const file = new File([blob], `${type}_import.xlsx`, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      const formData = new FormData();
      formData.append('file', file);

      console.log(`Sending import request to /api/salary/${type}/import`);
      console.log('Preview data sample:', previewData.slice(0, 2));

      const response = await fetch(`/api/salary/${type}/import`, {
        method: 'POST',
        body: formData,
      });

      console.log('Import response status:', response.status, response.statusText);
      const result = await response.json();
      console.log('Import response data:', result);

      if (!response.ok) {
        const errorMessage = `Import failed: ${result.error || response.statusText}`;
        const errorDetails = result.details ? ` - Details: ${result.details}` : '';
        console.error('Import API error:', errorMessage + errorDetails);
        if (result.errorDetails) {
          console.error('Error details from server:', result.errorDetails);
        }
        throw new Error(errorMessage + errorDetails);
      }

      setImportStatus(prev => ({
        ...prev,
        current: previewData.length,
        imported: result.imported || 0,
        errors: result.errors || 0,
        isImporting: false
      }));

      // Show detailed error message if there are errors
      if (result.errors && result.errors > 0) {
        let errorMsg = `${result.errors} records failed to import.`;

        // Add more details if available
        if (result.errorDetails && result.errorDetails.length > 0) {
          errorMsg += ' First error: ' + (result.errorDetails[0].error || 'Unknown error');
          console.error('Error details from server:', result.errorDetails);
        } else {
          errorMsg += ' Check console for details.';
        }

        setErrorMessages(prev => [...prev, errorMsg]);
      }

      // Clear preview data after successful import
      setPreviewData([]);

      // Call onImportSuccess after import is complete
      onImportSuccess();

    } catch (error) {
      console.error('Import error:', error);
      console.error('Error details:', error instanceof Error ? error.stack : 'Unknown error');

      // Show more detailed error message
      let errorMessage = 'Import process failed';
      if (error instanceof Error) {
        errorMessage += ': ' + error.message;
        if (error.cause) {
          errorMessage += ' - Cause: ' + JSON.stringify(error.cause);
        }
      } else {
        errorMessage += ': Unknown error';
      }

      setErrorMessages(prev => [...prev, errorMessage]);
      setImportStatus(prev => ({
        ...prev,
        isImporting: false
      }));
    }
  };

  const handleCancelImport = () => {
    setPreviewData([]);
    setImportStatus(prev => ({
      ...prev,
      isPreviewMode: false
    }));
    setActiveTab("template");
  };

  const downloadTemplate = () => {
    let fields: string[] = [];
    let sampleData: Record<string, any> = {};
    let instructionData: string[][] = [];

    // Set fields, sample data, and instructions based on type
    if (type === 'staff') {
      fields = [
        'EmployeeId', 'Nama', 'GP', 'Gol Kontrak', 'Gol Tetap', 'Beban Kerja', 'Insentif',
        'Tunjangan Keahlian', 'Tunjangan Jabatan', 'Total Gross', 'BPJS TK Yayasan',
        'BPJS Kesehatan Yayasan', 'PPh Yayasan', 'Total Gross Yayasan', 'AWOL',
        'Jumlah AWOL', 'Freq', 'Minute', 'Jumlah Freq', 'Jumlah Minute',
        'Potongan Absensi', 'Gaji Setelah Potongan Absensi', 'BPJS TK',
        'BPJS Kesehatan', 'Nett Setelah BPJS', 'PPh', 'Pinjaman Lain',
        'Iuran Wajib', 'Pinjaman Koperasi', 'Piutang', 'Potongan Bank',
        'Total Potongan', 'Gaji Netto', 'Periode'
      ];

      sampleData = {
        'EmployeeId': 1,
        'Nama': 'Nama Staff',
        'GP': 5000000,
        'Gol Kontrak': 0,
        'Gol Tetap': 1000000,
        'Beban Kerja': 500000,
        'Insentif': 300000,
        'Tunjangan Keahlian': 200000,
        'Tunjangan Jabatan': 400000,
        'Total Gross': 7400000,
        'BPJS TK Yayasan': 100000,
        'BPJS Kesehatan Yayasan': 50000,
        'PPh Yayasan': 75000,
        'Total Gross Yayasan': 7625000,
        'AWOL': 'Tidak',
        'Jumlah AWOL': 0,
        'Freq': 'Tidak',
        'Minute': 'Tidak',
        'Jumlah Freq': 0,
        'Jumlah Minute': 0,
        'Potongan Absensi': 0,
        'Gaji Setelah Potongan Absensi': 7400000,
        'BPJS TK': 100000,
        'BPJS Kesehatan': 50000,
        'Nett Setelah BPJS': 7250000,
        'PPh': 75000,
        'Pinjaman Lain': 0,
        'Iuran Wajib': 50000,
        'Pinjaman Koperasi': 0,
        'Piutang': 0,
        'Potongan Bank': 0,
        'Total Potongan': 275000,
        'Gaji Netto': 7125000,
        'Periode': '1/2023'
      };

      instructionData = [
        ['PETUNJUK PENGISIAN TEMPLATE IMPORT GAJI STAFF'],
        [''],
        ['1. Isi data gaji staff di sheet "Template" sesuai dengan format yang telah disediakan.'],
        ['2. Kolom EmployeeId harus diisi dengan ID karyawan yang valid dari sistem.'],
        ['3. Format nilai uang: angka tanpa tanda pemisah ribuan (contoh: 5000000)'],
        ['4. Format AWOL, Freq, Minute: "Ya" atau "Tidak"'],
        ['5. Format Periode: bulan/tahun (contoh: 1/2023 untuk Januari 2023)'],
        ['6. Jangan mengubah nama kolom atau urutan kolom.'],
        ['7. Hapus baris contoh sebelum mengimpor data.'],
        ['8. Simpan file dalam format .xlsx'],
      ];
    } else if (type === 'guru') {
      fields = [
        'EmployeeId', 'Nama', 'GP', 'Load', 'HM', 'Nominal', 'Jumlah XC', 'Jabatan',
        'Wali Kelas', 'Pembina OSIS', 'XC', 'Menggantikan', 'Total',
        'SL', 'VL', 'AWOL', 'Jumlah SL', 'Jumlah VL', 'Jumlah AWOL',
        'Freq', 'Minute', 'Jumlah Freq', 'Jumlah Minute', 'Potongan Absensi',
        'Potongan Ngajar', 'Bruto', 'Potongan BPJS TK', 'Potongan BPJS Kesehatan',
        'Netto Setelah BPJS', 'PPh21', 'Pinjaman Lainnya', 'Iuran Wajib',
        'Pinjaman Koperasi', 'Piutang', 'Potongan Bank', 'Total Potongan',
        'Gaji Netto', 'Periode'
      ];

      sampleData = {
        'EmployeeId': 2,
        'Nama': 'Nama Guru',
        'GP': 4000000,
        'Load': 24,
        'HM': 50000,
        'Nominal': 1200000,
        'Jumlah XC': 200000,
        'Jabatan': 300000,
        'Wali Kelas': 250000,
        'Pembina OSIS': 0,
        'XC': 200000,
        'Menggantikan': 0,
        'Total': 5950000,
        'SL': 'Tidak',
        'VL': 'Tidak',
        'AWOL': 'Tidak',
        'Jumlah SL': 0,
        'Jumlah VL': 0,
        'Jumlah AWOL': 0,
        'Freq': 'Tidak',
        'Minute': 'Tidak',
        'Jumlah Freq': 0,
        'Jumlah Minute': 0,
        'Potongan Absensi': 0,
        'Potongan Ngajar': 0,
        'Bruto': 5950000,
        'Potongan BPJS TK': 100000,
        'Potongan BPJS Kesehatan': 50000,
        'Netto Setelah BPJS': 5800000,
        'PPh21': 75000,
        'Pinjaman Lainnya': 0,
        'Iuran Wajib': 50000,
        'Pinjaman Koperasi': 0,
        'Piutang': 0,
        'Potongan Bank': 0,
        'Total Potongan': 275000,
        'Gaji Netto': 5675000,
        'Periode': '1/2023'
      };

      instructionData = [
        ['PETUNJUK PENGISIAN TEMPLATE IMPORT GAJI GURU'],
        [''],
        ['1. Isi data gaji guru di sheet "Template" sesuai dengan format yang telah disediakan.'],
        ['2. Kolom EmployeeId harus diisi dengan ID karyawan yang valid dari sistem.'],
        ['3. Format nilai uang: angka tanpa tanda pemisah ribuan (contoh: 4000000)'],
        ['4. Format SL, VL, AWOL, Freq, Minute: "Ya" atau "Tidak"'],
        ['5. Format Periode: bulan/tahun (contoh: 1/2023 untuk Januari 2023)'],
        ['6. Jangan mengubah nama kolom atau urutan kolom.'],
        ['7. Hapus baris contoh sebelum mengimpor data.'],
        ['8. Simpan file dalam format .xlsx'],
      ];
    } else if (type === 'honor') {
      fields = [
        'EmployeeId', 'Nama', 'GP1', 'PPh Dibayar Sekolah', 'Total Tunjangan Tetap',
        'Honor', 'JP', 'Total Honor', 'Jumlah Hadir', 'Honor Hadir',
        'Total Honor Hadir', 'Sebelum PPh', 'Jumlah XC', 'XC', 'Tambahan',
        'Gaji Bruto', 'Bruto Setelah Potongan', 'Nett Setelah Jamsostek',
        'PPh21', 'Pinjaman Lainnya', 'Iuran Wajib', 'Pinjaman Koperasi',
        'Piutang', 'Potongan Bank', 'Gaji Netto', 'Periode'
      ];

      sampleData = {
        'EmployeeId': 3,
        'Nama': 'Nama Honor',
        'GP1': 3000000,
        'PPh Dibayar Sekolah': 50000,
        'Total Tunjangan Tetap': 3050000,
        'Honor': 200000,
        'JP': 100000,
        'Total Honor': 300000,
        'Jumlah Hadir': 22,
        'Honor Hadir': 25000,
        'Total Honor Hadir': 550000,
        'Sebelum PPh': 3900000,
        'Jumlah XC': 2,
        'XC': 100000,
        'Tambahan': 0,
        'Gaji Bruto': 4000000,
        'Bruto Setelah Potongan': 4000000,
        'Nett Setelah Jamsostek': 4000000,
        'PPh21': 50000,
        'Pinjaman Lainnya': 0,
        'Iuran Wajib': 50000,
        'Pinjaman Koperasi': 0,
        'Piutang': 0,
        'Potongan Bank': 0,
        'Gaji Netto': 3900000,
        'Periode': '1/2023'
      };

      instructionData = [
        ['PETUNJUK PENGISIAN TEMPLATE IMPORT GAJI HONOR'],
        [''],
        ['1. Isi data gaji honor di sheet "Template" sesuai dengan format yang telah disediakan.'],
        ['2. Kolom EmployeeId harus diisi dengan ID karyawan yang valid dari sistem.'],
        ['3. Format nilai uang: angka tanpa tanda pemisah ribuan (contoh: 3000000)'],
        ['4. Format Periode: bulan/tahun (contoh: 1/2023 untuk Januari 2023)'],
        ['5. Jangan mengubah nama kolom atau urutan kolom.'],
        ['6. Hapus baris contoh sebelum mengimpor data.'],
        ['7. Simpan file dalam format .xlsx'],
      ];
    }

    // Create workbook
    const wb = XLSX.utils.book_new();

    // Add instruction sheet
    const wsInstructions = XLSX.utils.aoa_to_sheet(instructionData);
    XLSX.utils.book_append_sheet(wb, wsInstructions, 'Instruksi');

    // Create template sheet with header and sample data
    const wsTemplate = XLSX.utils.aoa_to_sheet([fields]);
    XLSX.utils.sheet_add_json(wsTemplate, [sampleData], { skipHeader: true, origin: 'A2' });

    // Add note at the bottom
    const lastRow = 3;
    XLSX.utils.sheet_add_aoa(wsTemplate, [
      ['Catatan: Hapus baris contoh ini sebelum mengimpor data sebenarnya.']
    ], { origin: { r: lastRow, c: 0 } });
    XLSX.utils.book_append_sheet(wb, wsTemplate, 'Template');

    // Save the file
    XLSX.writeFile(wb, `salary_${type}_import_template.xlsx`);
  };

  // Render preview table
  const renderPreviewTable = () => {
    if (!previewData || previewData.length === 0) return null;

    // Get column headers from the first row
    const columns = Object.keys(previewData[0]);

    return (
      <div className="space-y-4 flex-1 overflow-hidden flex flex-col">
        {/* Wrapper div with fixed height and overflow properties */}
        <div
          className="overflow-x-auto overflow-y-auto w-full"
          style={{
            height: 'calc(70vh - 200px)',
            display: 'block',
            border: '1px solid #e2e8f0',
            borderRadius: '0.375rem'
          }}
        >
          {/* Force table to be wider than container */}
          <div style={{ minWidth: 'max-content', width: columns.length * 200 + 'px' }}>
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10">
                <TableRow>
                  {columns.map((column) => (
                    <TableHead
                      key={column}
                      className="px-4 py-2 whitespace-nowrap"
                      style={{ minWidth: '200px' }}
                    >
                      {column}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {previewData.slice(0, 100).map((row, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {columns.map((column) => (
                      <TableCell
                        key={`${rowIndex}-${column}`}
                        className="px-4 py-2 whitespace-nowrap"
                        style={{ minWidth: '200px' }}
                      >
                        {row[column] !== undefined && row[column] !== null ? String(row[column]) : ''}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
        {previewData.length > 100 && (
          <p className="text-sm text-gray-500 italic">
            Showing first 100 rows of {previewData.length} total rows
          </p>
        )}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={`${importStatus.isPreviewMode ? 'max-w-5xl' : 'max-w-md'} max-h-[90vh] overflow-hidden flex flex-col`}>
        <DialogHeader>
          <DialogTitle>
            Import {type === 'staff' ? 'Staff' : type === 'guru' ? 'Guru' : 'Honor'} Salary Data
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="template">Template</TabsTrigger>
            <TabsTrigger value="preview" disabled={!importStatus.isPreviewMode && previewData.length === 0}>
              Preview Data
            </TabsTrigger>
          </TabsList>

          <TabsContent value="template" className="space-y-4">
            <div className="flex items-center gap-4">
              <Button onClick={downloadTemplate}>
                Download Template
              </Button>
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                disabled={importStatus.isImporting}
                className="hidden"
                id="excel-import"
              />
              <Button
                onClick={() => document.getElementById('excel-import')?.click()}
                disabled={importStatus.isImporting || importStatus.isPreviewMode}
              >
                {importStatus.isImporting ? 'Importing...' : 'Select Excel File'}
              </Button>
            </div>

            {importStatus.isImporting && (
              <div className="space-y-2">
                <Progress
                  value={(importStatus.current / importStatus.total) * 100}
                  className="h-2"
                />
                <p className="text-sm text-gray-500">
                  Processing: {importStatus.current} of {importStatus.total} records
                </p>
              </div>
            )}

            {importStatus.imported > 0 && !importStatus.isImporting && (
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-600">
                  Successfully imported {importStatus.imported} records.
                  {importStatus.errors > 0 && ` (${importStatus.errors} errors)`}
                </AlertDescription>
              </Alert>
            )}

            {errorMessages.length > 0 && (
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="errors">
                  <AccordionTrigger className="text-red-500">
                    Import Errors ({errorMessages.length})
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="max-h-40 overflow-y-auto">
                      <ul className="list-disc pl-5 space-y-1">
                        {errorMessages.map((error, index) => (
                          <li key={index} className="text-sm text-red-500">
                            {error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}
          </TabsContent>

          <TabsContent value="preview" className="flex-1 flex flex-col">
            {renderPreviewTable()}

            <div className="sticky bottom-0 bg-white py-4 border-t mt-4">
              <DialogFooter>
                <Button variant="outline" onClick={handleCancelImport} disabled={importStatus.isImporting}>
                  Cancel
                </Button>
                <Button onClick={handleImport} disabled={importStatus.isImporting || previewData.length === 0}>
                  {importStatus.isImporting ? 'Importing...' : 'Confirm Import'}
                </Button>
              </DialogFooter>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
