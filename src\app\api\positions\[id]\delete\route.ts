/**
 * API Route: DELETE /api/positions/[id]/delete
 *
 * Deskripsi: Menghapus posisi berdasarkan ID
 * Penggunaan: Tombol hapus di halaman daftar posisi
 *
 * Path Parameters:
 * - id: ID posisi (number)
 *
 * Response:
 * - 200: Posisi berhasil dihapus
 * - 400: ID tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Posisi tidak ditemukan
 * - 409: Posisi tidak dapat dihapus (memiliki karyawan)
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/db';
import { NextRequest } from 'next/server';



export async function DELETE(_request: NextRequest) {
  try {
    // Await params before accessing its properties
    // params is now directly available
    // Authentication check
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie || JSON.parse(userCookie.value).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extract ID from URL path
    const id = _request.nextUrl.pathname.split('/').pop();
    const positionId = Number(id);

    if (isNaN(positionId)) {
      return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
    }

    // Check if position has any employees
    const employeesCount = await prisma.employee.count({
      where: { positionId }
    });

    if (employeesCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete position that has associated employees' },
        { status: 409 }
      );
    }

    // If no employees are associated, proceed with deletion
    await prisma.position.delete({
      where: { id: positionId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting position:', error);
    return NextResponse.json(
      { error: 'Failed to delete position. It might be referenced by other records.' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
