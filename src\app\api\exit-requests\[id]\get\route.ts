/**
 * API Route: GET /api/exit-requests/[id]/get
 *
 * Deskripsi: Mengambil detail permintaan keluar berdasarkan ID
 * Penggunaan: Halaman detail permintaan keluar
 *
 * Path Parameters:
 * - id: ID permintaan keluar (number)
 *
 * Response:
 * - 200: Detail permintaan keluar
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Permintaan keluar tidak ditemukan
 * - 500: Error server
 */

import { NextResponse, NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get ID from params
    const id = params.id;

    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    // Check if exit request exists
    const exitRequest = await prisma.exitRequest.findUnique({
      where: { id: parseInt(id) },
      include: {
        employee: {
          select: {
            firstName: true,
            lastName: true,
            departmentId: true,
          },
        },
      },
    });

    if (!exitRequest) {
      return NextResponse.json({ error: 'Exit request not found' }, { status: 404 });
    }

    // Admin dapat melihat semua exit requests
    if (userRole === 'ADMIN') {
      return NextResponse.json(exitRequest);
    }

    // Supervisor hanya dapat melihat exit request dari bawahannya
    if (userRole === 'SUPERVISOR') {
      // Ambil departemen supervisor
      const supervisor = await prisma.employee.findUnique({
        where: { id: parseInt(userData.id) },
        select: { departmentId: true },
      });

      if (!supervisor) {
        return NextResponse.json({ error: 'Supervisor not found' }, { status: 404 });
      }

      // Cek apakah karyawan adalah bawahan dari supervisor
      if (exitRequest.employee.departmentId !== supervisor.departmentId) {
        return NextResponse.json(
          { error: 'You can only view exit requests from your department' },
          { status: 403 }
        );
      }

      return NextResponse.json(exitRequest);
    }

    // Employee hanya dapat melihat exit request miliknya sendiri
    if (exitRequest.employeeId !== parseInt(userData.id)) {
      return NextResponse.json(
        { error: 'You can only view your own exit requests' },
        { status: 403 }
      );
    }

    return NextResponse.json(exitRequest);
  } catch (error: any) {
    console.error('Error fetching exit request:', error);
    return NextResponse.json(
      { error: 'Failed to fetch exit request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
