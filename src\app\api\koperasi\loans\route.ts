/**
 * API Route: /api/koperasi/loans
 *
 * Description: This endpoint is currently disabled as the Loans feature is under construction
 */

import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json(
    { 
      message: 'The Loans feature is currently under construction and will be available in a future update.',
      status: 'under_construction'
    },
    { status: 503 }
  );
}

export async function POST() {
  return NextResponse.json(
    { 
      message: 'The Loans feature is currently under construction and will be available in a future update.',
      status: 'under_construction'
    },
    { status: 503 }
  );
}
