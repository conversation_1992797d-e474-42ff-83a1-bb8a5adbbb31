"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Lock, Search, UserCog } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import Navbar from "@/components/layout/Navbar";
import ProtectedRoute from "@/components/ProtectedRoute";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  password: string;
  employee: {
    firstName: string;
    lastName: string;
    employeeId: string;
    department: string;
    position: string;
  } | null;
}

export default function SettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        const response = await fetch("/api/users");
        const data = await response.json();

        if (response.ok && data.success) {
          setUsers(data.data);
        } else {
          toast({
            title: "Error",
            description: data.message || "Failed to fetch users",
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [user?.id, toast]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const filteredUsers = users.filter((user) => {
    const searchValue = searchTerm.toLowerCase();
    return (
      user.username.toLowerCase().includes(searchValue) ||
      (user.employee?.firstName?.toLowerCase().includes(searchValue) || "") ||
      (user.employee?.lastName?.toLowerCase().includes(searchValue) || "") ||
      user.email.toLowerCase().includes(searchValue) ||
      user.role.toLowerCase().includes(searchValue)
    );
  });

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedUser) return;

    if (newPassword !== confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match",
        variant: "destructive",
      });
      return;
    }

    if (newPassword.length < 6) {
      toast({
        title: "Error",
        description: "Password must be at least 6 characters long",
        variant: "destructive",
      });
      return;
    }

    setIsChangingPassword(true);

    try {
      const response = await fetch('/api/users/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: selectedUser.id,
          newPassword,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: `Password for ${selectedUser.username} updated successfully`,
        });
        setNewPassword("");
        setConfirmPassword("");
        setSelectedUser(null);
        setIsDialogOpen(false); // Close the dialog
      } else {
        toast({
          title: "Error",
          description: data.message || "Failed to update password",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (user?.role !== "ADMIN") {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p>You do not have permission to access this page.</p>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-8">User Management</h1>

          <div className="mb-6 flex items-center">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-bold">User List</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Username</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead className="w-1/3">Password</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4">
                          No users found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>{user.username}</TableCell>
                          <TableCell>
                            {user.employee
                              ? `${user.employee.firstName} ${user.employee.lastName}`
                              : "N/A"}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                user.role === "ADMIN"
                                  ? "destructive"
                                  : user.role === "SUPERVISOR"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {user.role}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-2">
                              <div>
                                <span className="text-base font-medium">Password (Hashed):</span>
                                <div className="mt-1">
                                  <code className="text-sm bg-gray-100 p-2 rounded block w-full overflow-x-auto">
                                    {user.password}
                                  </code>
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Dialog open={isDialogOpen && selectedUser?.id === user.id} onOpenChange={(open) => {
                              if (open) {
                                setSelectedUser(user);
                                setIsDialogOpen(true);
                              } else {
                                setIsDialogOpen(false);
                                if (!isChangingPassword) {
                                  setSelectedUser(null);
                                  setNewPassword("");
                                  setConfirmPassword("");
                                }
                              }
                            }}>
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                >
                                  <Lock className="h-4 w-4 mr-2" />
                                  Change Password
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>Change Password</DialogTitle>
                                </DialogHeader>
                                <form onSubmit={handleChangePassword}>
                                  <div className="grid gap-4 py-4">
                                    <div className="grid gap-2">
                                      <Label htmlFor="username">Username</Label>
                                      <Input
                                        id="username"
                                        value={selectedUser?.username || ""}
                                        disabled
                                      />
                                    </div>
                                    <div className="grid gap-2">
                                      <Label htmlFor="newPassword">New Password</Label>
                                      <Input
                                        id="newPassword"
                                        type="password"
                                        value={newPassword}
                                        onChange={(e) => setNewPassword(e.target.value)}
                                        required
                                      />
                                    </div>
                                    <div className="grid gap-2">
                                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                                      <Input
                                        id="confirmPassword"
                                        type="password"
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        required
                                      />
                                    </div>
                                  </div>
                                  <DialogFooter>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      onClick={() => {
                                        setNewPassword("");
                                        setConfirmPassword("");
                                        setSelectedUser(null);
                                        setIsDialogOpen(false);
                                      }}
                                    >
                                      Cancel
                                    </Button>
                                    <Button type="submit" disabled={isChangingPassword}>
                                      {isChangingPassword ? (
                                        <>
                                          <span className="mr-2">Updating...</span>
                                          <span className="animate-spin">⏳</span>
                                        </>
                                      ) : (
                                        "Update Password"
                                      )}
                                    </Button>
                                  </DialogFooter>
                                </form>
                              </DialogContent>
                            </Dialog>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
