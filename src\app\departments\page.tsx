"use client";

import React, { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import Navbar from "@/components/layout/Navbar";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Search, Eye, Edit, Trash2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { useAuth } from "@/lib/auth";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useToast } from "@/components/ui/use-toast";
import { DepartmentForm } from "@/components/forms/department-form";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
// Select and ScrollArea components are used in DepartmentForm, not directly in this file
import useSWR from 'swr';
import { Loader2 } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Tambahkan fungsi fetcher di bawah imports
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('An error occurred while fetching the data.');
  }
  return response.json();
};



interface Department {
  id: number;
  name: string;
  description: string | null;
  headId: number | null;
  headName: string | null;
  head?: string | null; // Added for form compatibility
  employeeCount: number;
  createdAt: string;
  updatedAt: string;
}

// Definisikan schema untuk validasi
const departmentSchema = z.object({
  name: z.string().min(1, "Department name is required"),
  head: z.string().min(1, "Department head is required"),
  description: z.string().optional(),
});

type DepartmentFormData = z.infer<typeof departmentSchema>;

export default function DepartmentsPage() {
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');

  const { user } = useAuth();
  const { toast } = useToast();

  const isAdmin = user?.role === "ADMIN";
  const departmentsPerPage = 10;

  // Gunakan isLoading dari SWR
  const { data: departments = [], mutate, isLoading } = useSWR('/api/departments', fetcher, {
    revalidateOnFocus: true, // Ubah menjadi true agar data direfresh saat tab mendapat fokus
    revalidateOnReconnect: true, // Ubah menjadi true untuk revalidasi saat reconeksi
    dedupingInterval: 5000, // Kurangi interval deduping
  });

  // Inisialisasi form dengan react-hook-form
  const addForm = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: "",
      head: "",
      description: "",
    },
  });

  const editForm = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: selectedDepartment?.name || "",
      head: selectedDepartment?.head || "",
      description: selectedDepartment?.description || "",
    },
  });

  // Reset form ketika dialog ditutup
  useEffect(() => {
    if (!isAddDialogOpen) {
      addForm.reset();
    }
  }, [isAddDialogOpen]);

  useEffect(() => {
    if (!isEditDialogOpen) {
      editForm.reset();
    } else if (selectedDepartment) {
      editForm.reset({
        name: selectedDepartment.name,
        head: selectedDepartment.head || undefined, // Convert null to undefined
        description: selectedDepartment.description || undefined, // Convert null to undefined
      });
    }
  }, [isEditDialogOpen, selectedDepartment]);

  // Handler untuk submit form
  const onAddSubmit = async (data: DepartmentFormData) => {
    try {
      const response = await fetch('/api/departments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to add department');
      }

      await mutate();
      setIsAddDialogOpen(false);
      addForm.reset();
      toast({
        title: "Success",
        description: "Department added successfully",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add department",
      });
    }
  };

  const onEditSubmit = async (data: DepartmentFormData) => {
    if (!selectedDepartment) return;

    try {
      const response = await fetch(`/api/departments/${selectedDepartment.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: data.name,
          description: data.description,
          head: data.head // This will be either the employee ID or 'none'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update department');
      }

      await mutate(); // Refresh data using SWR
      setIsEditDialogOpen(false);
      editForm.reset();
      toast({
        title: "Success",
        description: "Department updated successfully",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update department",
      });
    }
  };

  // Handler untuk mengedit department
  const handleEdit = (id: number) => {
    const department = departments.find((d: Department) => d.id === id);
    if (department) {
      setSelectedDepartment(department);
      setIsEditDialogOpen(true);
    }
  };

  // Handler untuk melihat detail department
  const handleView = async (id: number) => {
    const department = departments.find((d: Department) => d.id === id);
    if (department) {
      setSelectedDepartment(department);
      setIsViewDialogOpen(true);
    }
  };

  // Handler untuk menghapus department
  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this department?')) {
      return;
    }

    try {
      const response = await fetch(`/api/departments/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete department');
      }

      await mutate(); // Refresh data
      toast({
        title: "Success",
        description: "Department deleted successfully",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete department",
      });
    }
  };

  // Tambahkan fungsi handleSearch
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset ke halaman pertama saat pencarian
  };

  // Fungsi untuk mendapatkan departments yang sudah difilter
  const getFilteredDepartments = useCallback(() => {
    return departments.filter((dept: Department) =>
      dept.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [departments, searchQuery]);

  // Fungsi untuk mendapatkan departments untuk halaman saat ini
  const getCurrentPageDepartments = useCallback(() => {
    const filtered = getFilteredDepartments();
    const startIndex = (currentPage - 1) * departmentsPerPage;
    const endIndex = startIndex + departmentsPerPage;
    return filtered.slice(startIndex, endIndex);
  }, [getFilteredDepartments, currentPage, departmentsPerPage]);

  // Hitung total halaman
  const totalPages = useMemo(() => {
    const filtered = getFilteredDepartments();
    return Math.ceil(filtered.length / departmentsPerPage);
  }, [getFilteredDepartments, departmentsPerPage]);

  // Table rendering
  return (
    <ProtectedRoute requiredRole="ADMIN">
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold">Departments</h1>
            <p className="text-muted-foreground mt-2">
              Manage organization departments and their structures
            </p>
          </div>

          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search departments..."
                className="pl-8"
                value={searchQuery}
                onChange={handleSearch}
              />
            </div>
            {isAdmin && (
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Department
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Department</DialogTitle>
                  </DialogHeader>
                  <DepartmentForm
                    onSubmit={onAddSubmit}
                    onCancel={() => setIsAddDialogOpen(false)}
                  />
                </DialogContent>
              </Dialog>
            )}
          </div>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Head</TableHead>
                  <TableHead>Employees</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center">
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        Loading...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : getCurrentPageDepartments().length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center">No departments found</TableCell>
                  </TableRow>
                ) : (
                  getCurrentPageDepartments().map((dept: Department) => (
                    <TableRow key={dept.id}>
                      <TableCell>{dept.name}</TableCell>
                      <TableCell>{dept.headName || 'No Head Assigned'}</TableCell>
                      <TableCell>{dept.employeeCount || 0}</TableCell>
                      <TableCell className="flex justify-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
                          onClick={() => handleView(dept.id)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        {isAdmin && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
                              onClick={() => handleEdit(dept.id)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-900 hover:bg-red-50"
                              onClick={() => handleDelete(dept.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </Button>
                          </>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Department Details</DialogTitle>
              </DialogHeader>
              {selectedDepartment && (
                <div className="space-y-4">
                  <div>
                    <Label>Department ID</Label>
                    <div>DEP{String(selectedDepartment.id).padStart(3, '0')}</div>
                  </div>
                  <div>
                    <Label>Name</Label>
                    <div>{selectedDepartment.name}</div>
                  </div>
                  <div>
                    <Label>Head</Label>
                    <div>{selectedDepartment.headName || "No Head Assigned"}</div>
                  </div>
                  <div>
                    <Label>Employee Count</Label>
                    <div>{selectedDepartment.employeeCount}</div>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Department</DialogTitle>
              </DialogHeader>
              {selectedDepartment && (
                <DepartmentForm
                  onSubmit={onEditSubmit}
                  onCancel={() => setIsEditDialogOpen(false)}
                  defaultValues={{
                    name: selectedDepartment.name,
                    description: selectedDepartment.description || '',
                    head: selectedDepartment.headId?.toString() || 'none' // Changed from empty string to 'none'
                  }}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Tambahkan Pagination */}
          {!isLoading && getCurrentPageDepartments().length > 0 && (
            <div className="flex items-center justify-center py-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}
