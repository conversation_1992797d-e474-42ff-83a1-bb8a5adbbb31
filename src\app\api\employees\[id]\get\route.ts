/**
 * API Route: GET /api/employees/[id]/get
 *
 * Deskripsi: Mengambil detail karyawan berdasarkan ID
 * Penggunaan: Halaman detail karyawan, form edit karyawan
 *
 * Path Parameters:
 * - id: <PERSON> karyawan (number)
 *
 * Response:
 * - 200: Detail karyawan
 * - 404: <PERSON><PERSON>wan tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Extract ID from URL path
    const id = parseInt(request.nextUrl.pathname.split('/')[3] || '0');

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid employee ID' },
        { status: 400 }
      );
    }

    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        department: true,
        position: true,
        user: true
      }
    });

    if (!employee || employee.isDeleted) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(employee);
  } catch (error: any) {
    console.error('API - Error fetching employee:', error);
    return NextResponse.json(
      { error: 'Failed to fetch employee' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
