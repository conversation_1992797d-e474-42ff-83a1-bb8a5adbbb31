"use client";

import { useForm } from "react-hook-form";
import { useEffect } from "react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

const leaveTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  daysAllowed: z.number().min(1, "Days allowed must be at least 1"),
  requiresApproval: z.boolean().default(true),
});

type LeaveTypeFormData = z.infer<typeof leaveTypeSchema>;

interface LeaveTypeFormProps {
  initialData?: Partial<LeaveTypeFormData>;
  onSubmit: (data: LeaveTypeFormData) => void;
  onCancel?: () => void;
}

export function LeaveTypeForm({ initialData, onSubmit, onCancel }: LeaveTypeFormProps) {
  const form = useForm<LeaveTypeFormData>({
    resolver: zodResolver(leaveTypeSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      daysAllowed: initialData?.daysAllowed || 1,
      requiresApproval: initialData?.requiresApproval ?? true,
    },
  });

  // Log initialData untuk debugging
  console.log('LeaveTypeForm initialData:', initialData);
  console.log('LeaveTypeForm defaultValues:', form.getValues());

  // Update form values when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log('Updating form with initialData:', initialData);
      form.reset({
        name: initialData.name || '',
        description: initialData.description || '',
        daysAllowed: initialData.daysAllowed || 1,
        requiresApproval: initialData.requiresApproval ?? true,
      });
    }
  }, [initialData, form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => {
        console.log('Form submitted with data:', data);
        onSubmit(data);
      })} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Leave Type Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Annual Leave" {...field} />
              </FormControl>
              <FormDescription>
                Enter a unique name for this leave type
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the purpose of this leave type..."
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="daysAllowed"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Days Allowed</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min={1}
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormDescription>
                Number of days allowed for this leave type (minimum 1 day)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="requiresApproval"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  Requires Approval
                </FormLabel>
                <FormDescription>
                  Check if this leave type requires supervisor approval
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit">Save Leave Type</Button>
        </div>
      </form>
    </Form>
  );
}


