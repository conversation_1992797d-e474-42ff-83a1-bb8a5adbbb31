/**
 * API Route: PUT /api/exit-requests/[id]/edit
 *
 * Deskripsi: Memperbarui permintaan keluar berdasarkan ID
 * Penggunaan: Form edit permintaan keluar
 *
 * Path Parameters:
 * - id: ID permintaan keluar (number)
 *
 * Body:
 * - exitType: <PERSON><PERSON><PERSON> keluar (string)
 * - exitDate: <PERSON><PERSON> keluar (string)
 * - exitTime: <PERSON><PERSON><PERSON> keluar (string)
 * - comebackTime: <PERSON><PERSON><PERSON> kembali (string, opsional)
 * - notComeback: Tidak kembali (boolean)
 * - reason: <PERSON>asan (string)
 *
 * Response:
 * - 200: Permintaan keluar berhasil diperbarui
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Permintaan keluar tidak ditemukan
 * - 409: Permintaan keluar tidak dapat diedit (status bukan pending)
 * - 500: Error server
 */

import { NextResponse, NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

// Helper function to convert time string (HH:MM) to minutes
function convertTimeToMinutes(timeStr: string): number {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);

    // Extract ID from URL path
    const id = request.nextUrl.pathname.split('/')[3];
    const data = await request.json();
    const { exitType, exitDate, exitTime, comebackTime, notComeback, reason } = data;

    // Check if exit request exists
    const exitRequest = await prisma.exitRequest.findUnique({
      where: { id: parseInt(id) },
    });

    if (!exitRequest) {
      return NextResponse.json({ error: 'Exit request not found' }, { status: 404 });
    }

    // Validasi: User hanya dapat mengedit exit request miliknya sendiri
    if (exitRequest.employeeId !== parseInt(userData.id) && userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'You can only edit your own exit requests' },
        { status: 403 }
      );
    }

    // Validasi: Hanya exit request dengan status pending yang dapat diedit
    if (exitRequest.status !== 'pending') {
      return NextResponse.json(
        { error: 'Only pending exit requests can be edited' },
        { status: 409 }
      );
    }

    // Validate exit time and comeback time
    if (!notComeback && comebackTime) {
      const exitTimeMinutes = convertTimeToMinutes(exitTime);
      const comebackTimeMinutes = convertTimeToMinutes(comebackTime);

      if (comebackTimeMinutes <= exitTimeMinutes) {
        return NextResponse.json(
          { error: 'Comeback time must be later than exit time' },
          { status: 400 }
        );
      }
    }

    // Update exit request
    const updatedExitRequest = await prisma.exitRequest.update({
      where: { id: parseInt(id) },
      data: {
        exitType,
        exitDate: new Date(exitDate),
        exitTime,
        comebackTime: notComeback ? '16:00' : comebackTime,
        notComeback,
        reason,
      },
    });

    return NextResponse.json({
      message: 'Exit request updated successfully',
      exitRequest: updatedExitRequest,
    });
  } catch (error: any) {
    console.error('Error updating exit request:', error);
    return NextResponse.json(
      { error: 'Failed to update exit request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
