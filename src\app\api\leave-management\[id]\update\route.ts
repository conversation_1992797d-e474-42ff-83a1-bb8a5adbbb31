/**
 * API Route: PUT /api/leave-management/[id]/update
 *
 * Deskripsi: Memperbarui status permintaan cuti
 * Penggunaan: Tombol approve/reject permintaan cuti
 *
 * Path Parameters:
 * - id: ID permintaan cuti (number)
 *
 * Body:
 * - status: Status baru ('approved', 'rejected', 'cancelled')
 * - approvedById: ID karyawan yang menyetujui/menolak (number, opsional)
 * - notes: <PERSON><PERSON><PERSON> tambahan (string, opsional)
 *
 * Response:
 * - 200: Permintaan cuti berhasil diperbarui
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 404: Permintaan cuti tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const isAdmin = userData.role === 'ADMIN';
    const isSupervisor = userData.role === 'SUPERVISOR';

    // Only admin and supervisor can approve/reject
    if (!isAdmin && !isSupervisor) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get ID from params
    const id = params.id;
    const data = await request.json();
    const { status, approvedById, notes } = data;

    // Validate status
    if (!status || !['approved', 'rejected', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be approved, rejected, or cancelled' },
        { status: 400 }
      );
    }

    // Check if leave request exists
    const leaveRequest = await prisma.leaveRequest.findUnique({
      where: { id: parseInt(id) },
      include: {
        employee: {
          include: {
            department: true
          }
        }
      }
    });

    if (!leaveRequest) {
      return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
    }

    // If supervisor, check if they are the head of the employee's department
    if (isSupervisor && !isAdmin) {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const employeeId = userData.employeeId ? parseInt(userData.employeeId) : parseInt(userData.id);

      // Supervisor tidak dapat approve/reject leave request miliknya sendiri
      if (leaveRequest.employeeId === employeeId) {
        return NextResponse.json(
          { error: 'Anda tidak dapat menyetujui/menolak permintaan leave Anda sendiri' },
          { status: 403 }
        );
      }

      // Supervisor hanya dapat approve/reject leave request karyawan di departemen yang dipimpinnya
      if (leaveRequest.employee.department.headId !== employeeId) {
        return NextResponse.json(
          { error: 'Anda hanya dapat menyetujui/menolak permintaan leave untuk karyawan di departemen Anda' },
          { status: 403 }
        );
      }
    }

    // Siapkan data untuk update
    const updateData: any = {
      status,
      updatedAt: new Date()
    };

    // Jika ada approvedById, tambahkan ke data
    if (approvedById) {
      updateData.approvedById = parseInt(approvedById);
      updateData.approvedAt = new Date(); // Tambahkan timestamp approval
    }

    // Jika ada notes, tambahkan ke reason (karena tidak ada field notes)
    if (notes) {
      updateData.reason = notes;
    }

    console.log('Update data:', updateData);

    // Update leave request
    const updatedLeaveRequest = await prisma.leaveRequest.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        employee: true,
        leaveType: true,
        approvedBy: true
      }
    });

    return NextResponse.json(updatedLeaveRequest);
  } catch (error) {
    console.error('Error updating leave request:', error);
    return NextResponse.json(
      { error: 'Failed to update leave request' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
