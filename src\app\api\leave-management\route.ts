/**
 * API Route: /api/leave-management
 *
 * Deskripsi: Endpoint untuk mengarahkan request ke API route yang sesuai
 *
 * Catatan: File ini hanya berfungsi sebagai router untuk mengarahkan request
 * ke endpoint yang sesuai. Implementasi sebenarnya ada di file terpisah
 * untuk memudahkan maintenance.
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { logger } from '@/lib/logger';



export async function GET() {
  logger.debug('GET /api/leave-management - Start fetching leave requests');
  try {
    const cookieStore = await cookies();
    const userCookie = await cookieStore.get('user');

    if (!userCookie) {
      // Jangan log error, ini normal ketika user logout
      return NextResponse.json([], { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    logger.debug('GET /api/leave-management - Authenticated user');
    logger.debug('GET /api/leave-management - Executing database query');

    // Buat where clause berdasarkan role
    let whereClause = {};

    try {
      // Gunakan employeeId jika ada, jika tidak gunakan id
      const employeeId = userData.employeeId ? parseInt(userData.employeeId) : parseInt(userData.id);

      if (userData.role === 'EMPLOYEE') {
        // EMPLOYEE hanya melihat leave request miliknya sendiri
        whereClause = {
          employeeId: employeeId
        };
        logger.debug('EMPLOYEE filter - showing leave requests for employee');
      } else if (userData.role === 'SUPERVISOR') {
        // SUPERVISOR melihat leave request bawahannya
        logger.debug('SUPERVISOR - fetching department for supervisor');

        // Pastikan ID valid
        if (!employeeId || isNaN(employeeId)) {
          logger.debug('Invalid supervisor ID');
          whereClause = {}; // Tampilkan semua jika ID tidak valid
        } else {
          try {
            // Cari departemen yang dipimpin oleh supervisor ini
            const departments = await prisma.department.findMany({
              where: {
                headId: employeeId
              },
              select: {
                id: true,
                name: true
              }
            });

            logger.debug(`Found ${departments.length} departments headed by supervisor`);

            if (departments.length > 0) {
              // Dapatkan semua karyawan di departemen yang dipimpin oleh supervisor
              const departmentIds = departments.map(dept => dept.id);

              const departmentEmployees = await prisma.employee.findMany({
                where: {
                  departmentId: { in: departmentIds },
                  // Kecualikan supervisor itu sendiri jika tidak ingin melihat leave requestnya sendiri
                  // id: { not: parseInt(userData.id) }
                  isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
                },
                select: { id: true, firstName: true, lastName: true }
              });

              logger.debug(`Found ${departmentEmployees.length} employees in departments`);

              if (departmentEmployees.length > 0) {
                // Buat array ID karyawan
                const employeeIds = departmentEmployees.map(emp => emp.id);

                logger.debug(`Employee IDs for filtering: ${employeeIds.length} employees`);

                // Tambahkan ID supervisor ke array employeeIds
                if (!employeeIds.includes(employeeId)) {
                  employeeIds.push(employeeId);
                }

                logger.debug('Employee IDs updated to include supervisor');

                // Filter leave request berdasarkan karyawan di departemen dan supervisor sendiri
                whereClause = {
                  employeeId: { in: employeeIds }
                };
              } else {
                logger.debug('No employees found in departments, showing all leave requests');
                // Jika tidak ada karyawan di departemen, tampilkan semua leave request
                whereClause = {};
              }
            } else {
              // Jika supervisor tidak memimpin departemen, coba cari departemen tempat dia bekerja
              const supervisor = await prisma.employee.findUnique({
                where: { id: parseInt(userData.id) },
                select: { departmentId: true, firstName: true, lastName: true }
              });

              logger.debug('Supervisor data retrieved');

              if (supervisor && supervisor.departmentId) {
                logger.debug('Found department ID for supervisor');
                // Dapatkan semua karyawan di departemen yang sama
                const departmentEmployees = await prisma.employee.findMany({
                  where: {
                    departmentId: supervisor.departmentId,
                    // Kecualikan supervisor itu sendiri jika tidak ingin melihat leave requestnya sendiri
                    // id: { not: parseInt(userData.id) }
                    isDeleted: false // Mengecualikan employee yang sudah di-soft-delete
                  },
                  select: { id: true, firstName: true, lastName: true }
                });

                logger.debug(`Found ${departmentEmployees.length} employees in department`);

                if (departmentEmployees.length > 0) {
                  // Buat array ID karyawan
                  const employeeIds = departmentEmployees.map(emp => emp.id);

                  logger.debug(`Employee IDs for filtering: ${employeeIds.length} employees`);

                  // Tambahkan ID supervisor ke array employeeIds
                  if (!employeeIds.includes(employeeId)) {
                    employeeIds.push(employeeId);
                  }

                  logger.debug('Employee IDs updated to include supervisor');

                  // Filter leave request berdasarkan karyawan di departemen dan supervisor sendiri
                  whereClause = {
                    employeeId: { in: employeeIds }
                  };
                } else {
                  logger.debug('No employees found in department, showing supervisor leave requests only');
                  whereClause = {
                    employeeId: employeeId
                  };
                }
              } else {
                logger.debug('No department found for supervisor or departmentId is null');
                // Jika supervisor tidak memiliki departemen, tampilkan hanya leave request miliknya sendiri
                whereClause = {
                  employeeId: employeeId
                };
              }
            }
          } catch (error) {
            logger.error('Error finding departments or employees:', error);
            // Jika terjadi error, tampilkan hanya leave request miliknya sendiri
            whereClause = {
              employeeId: employeeId
            };
          }
        }
      } else if (userData.role === 'HEAD') {
        // HEAD melihat leave request dari karyawan dengan posisi Chief, Kepala, atau Psikolog
        logger.debug('HEAD - fetching employees with Chief, Kepala, or Psikolog positions');

        // Untuk HEAD, kita tidak menggunakan filter di where clause
        // karena kita perlu mengambil semua leave requests terlebih dahulu
        // dan kemudian memfilter berdasarkan posisi karyawan
        whereClause = {};
      } else {
        logger.debug('ADMIN - showing all leave requests');
      }
    } catch (error) {
      logger.error('Error setting up where clause:', error);
      // Jika terjadi error, tampilkan semua leave requests (untuk ADMIN)
      // atau hanya milik user sendiri (untuk EMPLOYEE/SUPERVISOR)
      if (userData.role !== 'ADMIN' && userData.id) {
        try {
          whereClause = {
            employeeId: parseInt(userData.id)
          };
        } catch (e) {
          logger.error('Error setting fallback where clause:', e);
          whereClause = {};
        }
      }
    }
    // Untuk ADMIN, whereClause tetap kosong (menampilkan semua)

    logger.debug('Final where clause for leave requests prepared');

    // Tambahkan include position untuk role HEAD
    const includeOptions = {
      employee: {
        include: {
          department: {
            include: {
              head: {
                select: {
                  id: true,
                  employeeId: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          position: true // Tambahkan position untuk role HEAD
        }
      },
      leaveType: true,
      approvedBy: true
    };

    let leaveRequests = await prisma.leaveRequest.findMany({
      where: whereClause,
      include: includeOptions,
      orderBy: {
        createdAt: 'desc' // Urutkan dari yang terbaru
      }
    });

    // Jika role adalah HEAD, filter leave requests berdasarkan posisi karyawan
    if (userData.role === 'HEAD') {
      logger.debug('Filtering leave requests for HEAD based on position title');

      // Pastikan employeeId didefinisikan dengan benar
      const headEmployeeId = userData.employeeId ? parseInt(userData.employeeId) : parseInt(userData.id);

      // Filter leave requests untuk hanya menampilkan karyawan dengan posisi Chief, Kepala, atau Psikolog
      leaveRequests = leaveRequests.filter(request => {
        // Tambahkan request milik HEAD sendiri
        if (request.employeeId === headEmployeeId) {
          return true;
        }

        // Filter berdasarkan posisi
        const positionTitle = request.employee?.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        return isChiefOrKepalaOrPsikolog;
      });

      logger.debug(`Filtered to ${leaveRequests.length} leave requests for Chief/Kepala/Psikolog positions`);
    }

    logger.debug(`Found ${leaveRequests.length} leave requests matching criteria`);
    return NextResponse.json(leaveRequests);
  } catch (error) {
    logger.error('GET /api/leave-management - Failed to fetch leave requests:', error);
    return NextResponse.json({ error: 'Failed to fetch leave requests' }, { status: 500 });
  } finally {
    // Jangan log pesan disconnect, ini normal
    await prisma.$disconnect();
  }
}
