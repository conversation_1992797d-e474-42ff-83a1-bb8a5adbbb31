/**
 * API Route: GET /api/koperasi/savings/[id]/get
 *
 * Deskripsi: Mengambil detail simpanan koperasi berdasarkan ID
 * Penggunaan: Halaman detail simpanan koperasi
 *
 * Path Parameters:
 * - id: ID simpanan koperasi (number)
 *
 * Response:
 * - 200: Detail simpanan koperasi
 * - 401: Tidak terautentikasi
 * - 404: Simpanan koperasi tidak ditemukan
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from params
    const id = params.id;
    const saving = await prisma.koperasiSaving.findUnique({
      where: { id: parseInt(id) },
      include: {
        member: {
          include: {
            employee: {
              select: {
                firstName: true,
                lastName: true,
                department: {
                  select: {
                    name: true
                  }
                },
                position: {
                  select: {
                    title: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!saving) {
      return NextResponse.json({ error: 'Koperasi saving not found' }, { status: 404 });
    }

    // Format response
    const formattedSaving = {
      id: saving.id,
      member_id: saving.memberId,
      member_name: `${saving.member.employee.firstName} ${saving.member.employee.lastName}`,
      department: saving.member.employee.department?.name,
      position: saving.member.employee.position?.title,
      amount: Number(saving.amount),
      type: saving.type,
      date: saving.date,
      notes: saving.notes,
      created_at: saving.createdAt,
      updated_at: saving.updatedAt
    };

    return NextResponse.json(formattedSaving);
  } catch (error) {
    console.error('Error fetching koperasi saving:', error);
    return NextResponse.json(
      { error: 'Failed to fetch koperasi saving' },
      { status: 500 }
    );
  }
}
