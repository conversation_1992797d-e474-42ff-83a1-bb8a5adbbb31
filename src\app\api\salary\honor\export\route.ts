import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import * as XLSX from 'xlsx';

// POST: Export honor salary data to Excel
export async function POST(request: Request) {
  try {
    // Verify user role
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = JSON.parse(userCookie.value);

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { data } = await request.json();

    if (!data || !Array.isArray(data) || data.length === 0) {
      return NextResponse.json({ error: 'No data to export' }, { status: 400 });
    }

    // Format currency for Excel
    const formatCurrency = (value: number | null | undefined) => {
      if (value === null || value === undefined) return 0;
      return value;
    };

    // Format date for Excel
    const formatDate = (date: Date) => {
      const d = new Date(date);
      return `${d.getMonth() + 1}/${d.getFullYear()}`;
    };

    // Prepare data for Excel
    const excelData = data.map(item => ({
      'Nama': item.nama,
      'GP1': formatCurrency(item.gp1),
      'PPh Dibayar Sekolah': formatCurrency(item.pph_dibayar_sklh),
      'Total Tunjangan Tetap': formatCurrency(item.tot_tun_tep),
      'Honor': formatCurrency(item.hnr),
      'JP': formatCurrency(item.jp),
      'Total Honor': formatCurrency(item.tot_hnr),
      'Jumlah Hadir': item.jlh_hdr || 0,
      'Honor Hadir': formatCurrency(item.hnr_hdr),
      'Total Honor Hadir': formatCurrency(item.tot_hnr_hdr),
      'Sebelum PPh': formatCurrency(item.sblm_pph),
      'Jumlah XC': item.jlhxc || 0,
      'XC': formatCurrency(item.xc),
      'Tambahan': formatCurrency(item.tmbhn),
      'Gaji Bruto': formatCurrency(item.gaji_bruto),
      'Bruto Setelah Potongan': formatCurrency(item.bruto_stlh_pot),
      'Nett Setelah Jamsostek': formatCurrency(item.nettstlhjamsostek),
      'PPh21': formatCurrency(item.pph21),
      'Pinjaman Lainnya': formatCurrency(item.pinj_lainnya),
      'Iuran Wajib': formatCurrency(item.iuran_wajib),
      'Pinjaman Koperasi': formatCurrency(item.pinj_kop),
      'Piutang': formatCurrency(item.piutang),
      'Potongan Bank': formatCurrency(item.pot_bank),
      'Gaji Netto': formatCurrency(item.gaji_netto),
      'Periode': formatDate(item.period),
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Honor Salary');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });

    // Return Excel file as response
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="honor_salary_export.xlsx"',
      },
    });
  } catch (error) {
    console.error('Failed to export honor salary data:', error);
    return NextResponse.json(
      {
        error: 'Failed to export honor salary data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
