const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  // Create Departments first
  const itDepartment = await prisma.department.upsert({
    where: { name: 'Information Technology' },
    update: {},
    create: {
      name: 'Information Technology',
      description: 'IT Department'
    }
  });

  const hrDepartment = await prisma.department.upsert({
    where: { name: 'Human Resources' },
    update: {},
    create: {
      name: 'Human Resources',
      description: 'HR Department'
    }
  });

  // Create Positions with id in where clause
  const managerPosition = await prisma.position.upsert({
    where: { 
      id: 1 // Menggunakan ID sebagai unique identifier
    },
    update: {
      title: 'Manager',
      departmentId: itDepartment.id
    },
    create: {
      title: 'Manager',
      description: 'Department Manager',
      departmentId: itDepartment.id
    }
  });

  const staffPosition = await prisma.position.upsert({
    where: { 
      id: 2 // Menggunakan ID sebagai unique identifier
    },
    update: {
      title: 'Staff',
      departmentId: hrDepartment.id
    },
    create: {
      title: 'Staff',
      description: 'Regular Staff',
      departmentId: hrDepartment.id
    }
  });

  // Create Employees with new status
  const adminEmployee = await prisma.employee.upsert({
    where: { employeeId: 'EMP001' },
    update: {
      status: 'Tetap'
    },
    create: {
      employeeId: 'EMP001',
      firstName: 'John',
      lastName: null,
      email: '<EMAIL>',
      phone: '08123456789',
      hireDate: new Date('2024-01-01'),
      departmentId: itDepartment.id,
      positionId: managerPosition.id,
      status: 'Tetap'
    }
  });

  const supervisorEmployee = await prisma.employee.upsert({
    where: { employeeId: 'EMP002' },
    update: {
      status: 'Kontrak'
    },
    create: {
      employeeId: 'EMP002',
      firstName: 'Sarah',
      lastName: null,
      email: '<EMAIL>',
      phone: '08234567890',
      hireDate: new Date('2024-01-02'),
      departmentId: hrDepartment.id,
      positionId: staffPosition.id,
      status: 'Kontrak'
    }
  });

  // Create or update users
  await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: await hash('admin123', 10),
      role: 'ADMIN',  // Gunakan string uppercase
      employeeId: adminEmployee.id,
      lastLogin: new Date()
    }
  });

  await prisma.user.upsert({
    where: { username: 'supervisor' },
    update: {},
    create: {
      username: 'supervisor',
      email: '<EMAIL>',
      password: await hash('super123', 10),
      role: 'SUPERVISOR',  // Gunakan string uppercase
      employeeId: supervisorEmployee.id,
      lastLogin: new Date()
    }
  });

  console.log('Seed data updated successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });


