"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>ger,
  SheetHeader,
  SheetT<PERSON>le,
  SheetClose,
} from "@/components/ui/sheet";
import { Cross2Icon, ChevronRightIcon } from "@radix-ui/react-icons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/lib/auth";
import { Icons } from "@/components/ui/icons";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  UserCircle,
  ChevronDown,
  User,
  <PERSON>ting<PERSON>,
  <PERSON>,
  UserX,
  Calendar,
} from "lucide-react";

type UserRole = "ADMIN" | "SUPERVISOR" | "EMPLOYEE" | "OPERATOR_KOP" | "HEAD";

interface SubNavLink {
  href: string;
  label: string;
  adminOnly?: boolean;
  supervisorOnly?: boolean;
  icon?: React.ReactNode;
}

interface NavLink {
  href: string;
  label: string;
  adminOnly?: boolean;
  supervisorOnly?: boolean;
  subMenu?: SubNavLink[];
  icon?: React.ReactNode;
}

interface NavbarProps {
  logo?: string;
  userRole?: UserRole;
}

const Navbar = ({ logo = "EMS" }: NavbarProps) => {
  const router = useRouter();
  const { user, logout } = useAuth();
  const role = user?.role || "EMPLOYEE";
  const isAuthenticated = !!user;

  // Prevent hydration errors by using useEffect to ensure client-side only rendering for auth state
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const getMenuLinks = () => {
    const baseLinks: NavLink[] = [
      { href: "/", label: "Home" },
    ];

    if (!isAuthenticated) {
      return baseLinks;
    }

    const authenticatedLinks: NavLink[] = [
      {
        href: "/dashboard",
        label: "Dashboard",
        adminOnly: true,
        icon: <Icons.dashboard className="h-4 w-4 mr-2" />
      },
      {
        href: "/employees",
        label: "Employees",
        adminOnly: false,
        supervisorOnly: true,
        icon: <Users className="h-4 w-4 mr-2" />,
        subMenu: [
          {
            href: "/employees/exited",
            label: "Exited Employees",
            adminOnly: true,
            icon: <UserX className="h-4 w-4 mr-2" />
          }
        ]
      },
      {
        href: "/departments",
        label: "Departments",
        adminOnly: true,
        icon: <Icons.department className="h-4 w-4 mr-2" />
      },
      {
        href: "/positions",
        label: "Positions",
        adminOnly: true,
        icon: <Icons.position className="h-4 w-4 mr-2" />
      },
      {
        href: "/managers", // Memindahkan menu Managers setelah Positions
        label: "Managers",
        adminOnly: true,
        icon: <Icons.manager className="h-4 w-4 mr-2" />
      },
      {
        href: "/leave-management",
        label: "Leave Management",
        icon: <Icons.leave className="h-4 w-4 mr-2" />
      },
      {
        href: "/koperasi",
        label: "Koperasi",
        icon: <Icons.koperasi className="h-4 w-4 mr-2" />
      },
      {
        href: "/salary",
        label: "Salary",
        icon: <Icons.salary className="h-4 w-4 mr-2" />
      }
    ];

    const filteredLinks = authenticatedLinks.filter(link => {
      if (role === "ADMIN") return true;
      if (role === "SUPERVISOR" && !link.adminOnly) return true;
      if (role === "HEAD" && !link.adminOnly) return true;
      if (role === "OPERATOR_KOP") {
        // OPERATOR_KOP hanya dapat mengakses halaman Koperasi dan Home
        return link.href === "/koperasi" || link.href === "/";
      }
      return !link.adminOnly && !link.supervisorOnly;
    });

    return [...baseLinks, ...filteredLinks];
  };

  const links = getMenuLinks();

  const handleLogout = async () => {
    await logout();
    window.location.href = "/"; // Mengubah redirect ke landing page
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between">
        {/* Logo Section - Left */}
        <div className="flex items-center">
          <Link
            href="/"
            className="flex items-center space-x-2 px-3 py-2 rounded-md transition-all duration-200 hover:bg-primary/10 hover:text-primary relative group"
          >
            <Icons.logo className="h-6 w-6" />
            <span className="font-bold">{logo}</span>
            <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary transform origin-left scale-x-0 transition-transform duration-200 group-hover:scale-x-100" />
          </Link>
        </div>

        {/* Navigation and User Menu - Right */}
        <div className="flex items-center space-x-4">
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {links.map((link) => (
              link.subMenu ? (
                <DropdownMenu key={link.href}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="relative px-4 py-2 text-sm rounded-md transition-all duration-200 group
                        hover:bg-primary/10 hover:text-primary"
                    >
                      {link.label}
                      <ChevronDown className="h-4 w-4 ml-1" />
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary transform origin-left scale-x-0 transition-transform duration-200 group-hover:scale-x-100" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link href={link.href} className="flex items-center cursor-pointer transition-colors
                        hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary">
                        {link.icon}
                        {link.label}
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {link.subMenu.map((subLink) => (
                      (role === "ADMIN" || !subLink.adminOnly) && (
                        <DropdownMenuItem key={subLink.href} asChild>
                          <Link href={subLink.href} className="flex items-center cursor-pointer transition-colors
                            hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary">
                            {subLink.icon}
                            {subLink.label}
                          </Link>
                        </DropdownMenuItem>
                      )
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link
                  key={link.href}
                  href={link.href}
                  className="block px-4 py-2 text-sm relative group transition-all duration-200 hover:bg-primary/10 hover:text-primary rounded-md"
                  onClick={(e) => {
                    router.push(link.href);
                  }}
                >
                  {link.label}
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary transform origin-left scale-x-0 transition-transform duration-200 group-hover:scale-x-100" />
                </Link>
              )
            ))}
            {mounted && isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="relative px-3 py-2 rounded-md transition-all duration-200 group
                      hover:bg-primary/10 hover:text-primary"
                  >
                    <UserCircle className="h-5 w-5 mr-2" />
                    Profile
                    <span className={cn(
                      "ml-2 text-xs px-1.5 py-0.5 rounded-md capitalize",
                      role === "ADMIN" ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300" :
                      role === "SUPERVISOR" ? "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300" :
                      role === "HEAD" ? "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300" :
                      "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                    )}>
                      {role}
                    </span>
                    <ChevronDown className="h-4 w-4 ml-1" />
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary transform origin-left scale-x-0 transition-transform duration-200 group-hover:scale-x-100" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem asChild>
                    <Link href="/my-profile" className="flex items-center cursor-pointer transition-colors
                      hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary">
                      <User className="h-4 w-4 mr-2" />
                      My Profile
                    </Link>
                  </DropdownMenuItem>
                  {role === "ADMIN" && (
                    <>
                      <DropdownMenuItem asChild>
                        <Link href="/settings" className="flex items-center cursor-pointer transition-colors
                          hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary">
                          <Settings className="h-4 w-4 mr-2" />
                          User Management
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/settings/academic-year" className="flex items-center cursor-pointer transition-colors
                          hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary">
                          <Calendar className="h-4 w-4 mr-2" />
                          Academic Year
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="flex items-center cursor-pointer text-red-600
                      transition-colors hover:bg-red-50 hover:text-red-700
                      focus:bg-red-50 focus:text-red-700"
                    onClick={handleLogout}
                  >
                    <Icons.logout className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => router.push("/login")}
                  className="bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200"
                >
                  Login
                </Button>
              </div>
            )}
          </nav>

          {/* Theme Switcher */}
          <div className="border-l pl-4 ml-4">
            <ThemeSwitcher />
          </div>

          {/* Mobile Menu Button */}
          <Sheet>
            <SheetTrigger asChild className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                className="text-foreground/80 hover:bg-primary/10 hover:text-primary transition-colors duration-200"
              >
                <Icons.menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="h-full">

                <SheetHeader>
                <div className="flex flex-col items-center mb-4 pb-4 border-b">
                  <SheetTitle className="mb-2 font-bold text-xl">Menu</SheetTitle>
                  <div className="relative w-28 h-20 overflow-hidden rounded-md transition-all duration-300 hover:scale-105">
                    <div className="absolute inset-0">
                      <Image
                        src="/images/LOGO-BIG-Transparent.gif"
                        alt="Logo"
                        fill
                        sizes="(max-width: 768px) 100vw, 112px"
                        className="object-contain"
                        unoptimized
                        onError={(e) => {
                          // Fallback if image doesn't exist
                          e.currentTarget.src = 'https://via.placeholder.com/112x80?text=Logo';
                        }}
                      />
                    </div>
                  </div>
                </div>
                </SheetHeader>
              <nav className="flex flex-col gap-2 mt-4 px-2">
                {links.map((link) => (
                  <React.Fragment key={link.href}>
                    <Link
                      href={link.href}
                      className="text-base font-medium py-2 px-3 rounded-md transition-all duration-200
                        hover:bg-primary/10 hover:text-primary relative group"
                      onClick={(e) => {
                        router.push(link.href);
                      }}
                    >
                      {link.label}
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary transform origin-left
                        scale-x-0 transition-transform duration-200 group-hover:scale-x-100" />
                    </Link>

                    {/* Sub menu items */}
                    {link.subMenu && (
                      <div className="ml-4 pl-2 border-l border-muted">
                        {link.subMenu.map((subLink) => (
                          (role === "ADMIN" || !subLink.adminOnly) && (
                            <Link
                              key={subLink.href}
                              href={subLink.href}
                              className="flex items-center text-sm font-medium py-2 px-3 rounded-md transition-all duration-200
                                hover:bg-primary/10 hover:text-primary relative group"
                              onClick={(e) => {
                                router.push(subLink.href);
                              }}
                            >
                              {subLink.icon}
                              {subLink.label}
                              <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary transform origin-left
                                scale-x-0 transition-transform duration-200 group-hover:scale-x-100" />
                            </Link>
                          )
                        ))}
                      </div>
                    )}
                  </React.Fragment>
                ))}
              </nav>

              {/* Mobile Profile Section */}
              {mounted && !isAuthenticated && (
                <div className="mt-6 pt-6 border-t relative">
                  <Button
                    variant="default"
                    size="lg"
                    onClick={() => router.push("/login")}
                    className="w-full bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200"
                  >
                    Login
                  </Button>
                </div>
              )}
              {mounted && isAuthenticated && (
                <div className="mt-6 pt-6 border-t relative">
                  <div className="flex items-center mb-4 p-2">
                    <UserCircle className="h-6 w-6 mr-2" />
                    <span className="font-medium">Profile</span>
                    <span className={cn(
                      "ml-2 text-xs px-1.5 py-0.5 rounded-md capitalize",
                      role === "ADMIN" ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300" :
                      role === "SUPERVISOR" ? "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300" :
                      role === "HEAD" ? "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300" :
                      "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                    )}>
                      {role}
                    </span>
                  </div>

                  <div className="flex flex-col gap-2">
                    <Link
                      href="/my-profile"
                      className="flex items-center py-2 px-3 rounded-md transition-all duration-200
                        hover:bg-primary/10 hover:text-primary w-full"
                      onClick={(e) => {
                        console.log('Clicking my profile');
                        router.push('/my-profile');
                      }}
                    >
                      <User className="h-4 w-4 mr-2" />
                      My Profile
                    </Link>

                    {role === "ADMIN" && (
                      <>
                        <Link
                          href="/settings"
                          className="flex items-center py-2 px-3 rounded-md transition-all duration-200
                            hover:bg-primary/10 hover:text-primary w-full"
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          User Management
                        </Link>
                        <Link
                          href="/settings/academic-year"
                          className="flex items-center py-2 px-3 rounded-md transition-all duration-200
                            hover:bg-primary/10 hover:text-primary w-full"
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          Academic Year
                        </Link>
                      </>
                    )}

                    <button
                      onClick={handleLogout}
                      className="flex items-center py-2 px-3 rounded-md text-red-600 text-left
                        transition-colors hover:bg-red-50 hover:text-red-700 w-full"
                    >
                      <Icons.logout className="h-4 w-4 mr-2" />
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Navbar;






