/**
 * API Route: POST /api/leave-types/create
 *
 * Deskripsi: Membuat tipe cuti baru
 * Penggunaan: Form tambah tipe cuti
 *
 * Body:
 * - name: <PERSON>a tipe cuti (string)
 * - description: <PERSON>kripsi tipe cuti (string, opsional)
 * - daysAllowed: <PERSON><PERSON><PERSON> ya<PERSON> (number)
 * - requiresApproval: <PERSON><PERSON><PERSON><PERSON> (boolean, opsional)
 *
 * Response:
 * - 201: Tipe cuti berhasil dibuat
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function POST(request: Request) {
  try {
    // Verify admin/supervisor access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    if (!['ADMIN', 'SUPERVISOR', 'HEAD'].includes(userData.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await request.json();
    const { name, description, daysAllowed, requiresApproval } = data;

    // Validate required fields
    if (!name || !daysAllowed) {
      return NextResponse.json(
        { error: 'Name and days allowed are required' },
        { status: 400 }
      );
    }

    // Check if leave type with the same name already exists
    const existingLeaveType = await prisma.leaveType.findFirst({
      where: { name }
    });

    if (existingLeaveType) {
      return NextResponse.json(
        { error: 'Leave type with this name already exists' },
        { status: 400 }
      );
    }

    // Create new leave type
    const leaveType = await prisma.leaveType.create({
      data: {
        name,
        description: description || null,
        daysAllowed: Number(daysAllowed),
        requiresApproval: requiresApproval || false
      }
    });

    return NextResponse.json(leaveType, { status: 201 });
  } catch (error) {
    console.error('Error creating leave type:', error);
    return NextResponse.json(
      { error: 'Failed to create leave type' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
