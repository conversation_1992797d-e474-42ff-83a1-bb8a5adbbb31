/**
 * API Route: /api/academic-years
 *
 * Deskripsi: Endpoint untuk mengelola tahun akademik
 * Penggunaan: Halaman pengaturan tahun akademik
 *
 * GET: Mengambil daftar tahun akademik dengan semua karyawan (termasuk yang soft delete)
 * POST: Membuat tahun akademik baru dan otomatis assign karyawan sesuai option
 *
 * Fitur:
 * - Option untuk include/exclude karyawan yang sudah keluar (includeExitedEmployees)
 * - Default: hanya menghitung dan memindahkan karyawan yang masih aktif (isDeleted: false)
 * - Mengecualikan admin web (employeeId dimulai dengan "EMP") dan anggota eksternal ("EXT")
 * - Menyimpan detail jumlah karyawan historis (active/exited) saat tahun akademik dibuat
 *
 * Body Parameters (POST):
 * - ta: string (required) - Tahun akademik, contoh: "2024/2025"
 * - description: string (optional) - Deskripsi tahun akademik
 * - includeExitedEmployees: boolean (optional, default: false) - Include karyawan yang sudah keluar
 *
 * Historical Count Fields:
 * - currentEmployeeCount: Total karyawan (active + exited)
 * - historicalActiveCount: Jumlah karyawan aktif saat academic year dibuat
 * - historicalExitedCount: Jumlah karyawan yang sudah keluar saat academic year dibuat
 *
 * Response:
 * - 200: Sukses
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { logger } from '@/lib/logger';

// GET: Mengambil daftar tahun akademik
export async function GET(_request: NextRequest) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only ADMIN can access this resource' },
        { status: 403 }
      );
    }

    // Ambil semua tahun akademik dengan semua employees (termasuk yang soft delete)
    const academicYears = await prisma.academicYear.findMany({
      orderBy: {
        ta: 'desc'
      },
      include: {
        employees: {
          where: {
            NOT: {
              OR: [
                { employeeId: { startsWith: "EMP" } }, // Exclude admin web
                { employeeId: { startsWith: "EXT" } }  // Exclude external members
              ]
            }
          },
          select: {
            id: true,
            employeeId: true,
            firstName: true,
            lastName: true,
            isDeleted: true
          }
        }
      }
    });

    return NextResponse.json(academicYears);
  } catch (error: any) {
    logger.error('API - Error fetching academic years:', error);
    return NextResponse.json(
      { error: 'Failed to fetch academic years' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// POST: Membuat tahun akademik baru
export async function POST(request: NextRequest) {
  try {
    // Verifikasi akses admin
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = JSON.parse(userCookie.value);
    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden - Only ADMIN can access this resource' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { ta, description, includeExitedEmployees } = body;

    // Validasi data
    if (!ta) {
      return NextResponse.json(
        { error: 'Academic year (TA) is required' },
        { status: 400 }
      );
    }

    // Cek apakah tahun akademik sudah ada
    const existingAcademicYear = await prisma.academicYear.findFirst({
      where: {
        ta
      }
    });

    if (existingAcademicYear) {
      return NextResponse.json(
        { error: 'Academic year already exists' },
        { status: 400 }
      );
    }

    try {
      // Cari tahun akademik terbaru yang sudah ada (sebelum yang baru dibuat)
      // Build where clause berdasarkan includeExitedEmployees option
      const employeeWhereClause: any = {
        NOT: {
          OR: [
            { employeeId: { startsWith: "EMP" } }, // Exclude admin web
            { employeeId: { startsWith: "EXT" } }  // Exclude external members
          ]
        }
      };

      // Jika tidak include exited employees, tambahkan filter isDeleted: false
      if (!includeExitedEmployees) {
        employeeWhereClause.isDeleted = false;
      }

      const latestAcademicYear = await prisma.academicYear.findFirst({
        orderBy: {
          ta: 'desc'
        },
        include: {
          employees: {
            where: employeeWhereClause
          }
        }
      });

      // Hitung detail karyawan di tahun akademik terbaru untuk disimpan sebagai historical count
      const allEmployees = latestAcademicYear?.employees || [];
      const activeEmployees = allEmployees.filter(emp => !emp.isDeleted);
      const exitedEmployees = allEmployees.filter(emp => emp.isDeleted);

      const totalEmployeeCount = allEmployees.length;
      const activeEmployeeCount = activeEmployees.length;
      const exitedEmployeeCount = exitedEmployees.length;

      // Buat tahun akademik baru dengan menyimpan detail karyawan dari tahun sebelumnya
      const academicYear = await prisma.academicYear.create({
        data: {
          ta,
          description,
          currentEmployeeCount: totalEmployeeCount,
          historicalActiveCount: activeEmployeeCount,
          historicalExitedCount: exitedEmployeeCount
        }
      });

      // Jika ada karyawan aktif di tahun akademik sebelumnya, pindahkan mereka ke tahun akademik baru
      if (latestAcademicYear && latestAcademicYear.employees && latestAcademicYear.employees.length > 0) {
        // Dapatkan ID karyawan aktif dari tahun akademik sebelumnya
        const activeEmployeeIds = latestAcademicYear.employees.map(emp => emp.id);

        // Update karyawan untuk menggunakan tahun akademik baru
        // Build where clause untuk update berdasarkan includeExitedEmployees option
        const updateWhereClause: any = {
          id: {
            in: activeEmployeeIds
          }
        };

        // Jika tidak include exited employees, pastikan hanya update karyawan yang masih aktif
        if (!includeExitedEmployees) {
          updateWhereClause.isDeleted = false;
        }

        await prisma.employee.updateMany({
          where: updateWhereClause,
          data: {
            academicYearId: academicYear.id
          }
        });
      }

      return NextResponse.json(academicYear);
    } catch (error) {
      logger.error('API - Error creating academic year:', error);
      return NextResponse.json(
        { error: 'Failed to create academic year' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    logger.error('API - Error creating academic year:', error);
    return NextResponse.json(
      { error: 'Failed to create academic year' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
