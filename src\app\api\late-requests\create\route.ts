import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function POST(request: Request) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { employeeId, lateType, lateDate, estimatedTime, reason } = data;

    // Validate required fields
    if (!employeeId || !lateType || !lateDate || !estimatedTime) {
      return NextResponse.json(
        { error: 'Employee ID, late type, date, and estimated time are required' },
        { status: 400 }
      );
    }

    // Create late request
    const lateRequest = await prisma.lateRequest.create({
      data: {
        employeeId: parseInt(employeeId),
        lateType,
        lateDate: new Date(lateDate),
        estimatedTime,
        reason,
        status: 'pending',
      },
    });

    return NextResponse.json({
      message: 'Late request created successfully',
      lateRequest,
    });
  } catch (error) {
    console.error('Error creating late request:', error);
    return NextResponse.json(
      { error: 'Failed to create late request' },
      { status: 500 }
    );
  }
}
