import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { prisma } from '@/lib/db';
import { existsSync } from 'fs';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';


export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const r2 = new S3Client({
      region: 'auto',
      endpoint: process.env.R2_ENDPOINT,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
      },
    });
    // Get the ID from the URL params
    const id = params.id;
    console.log('Uploading attachment for leave request ID:', id);

    // Check authentication
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);

    // Check if leave request exists
    const leaveRequest = await prisma.leaveRequest.findUnique({
      where: { id: parseInt(id) },
    });

    if (!leaveRequest) {
      return NextResponse.json({ error: 'Leave request not found' }, { status: 404 });
    }

    // Verify user has permission to edit this leave request
    if (userData.role !== 'ADMIN' && leaveRequest.employeeId !== parseInt(userData.id)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Pastikan request adalah multipart/form-data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file || !(file instanceof File)) {
      return NextResponse.json({ error: 'No valid file uploaded' }, { status: 400 });
    }

    // Validasi tipe file
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'File type not allowed' }, { status: 400 });
    }

    // Validasi ukuran file (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File size exceeds 5MB' }, { status: 400 });
    }

    // Buat nama file unik
    const fileExtension = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExtension}`;

    // Path untuk menyimpan file
    const publicDir = join(process.cwd(), 'public');
    const uploadsDir = join(publicDir, 'uploads');

    // Create uploads directory if it doesn't exist
    if (!existsSync(uploadsDir)) {
      console.log('Creating uploads directory:', uploadsDir);
      await mkdir(uploadsDir, { recursive: true });
    }

    const filePath = join(uploadsDir, fileName);
    console.log('Saving file to:', filePath);

    // Baca file sebagai array buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    const bucketName = process.env.R2_BUCKET_NAME!;
    const r2Key = `leave-attachments/${fileName}`;

    await r2.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: r2Key,
      Body: buffer,
      ContentType: file.type,
    }));

    // Tulis file ke disk
    await writeFile(filePath, buffer);

    // URL file yang dapat diakses publik
    const fileUrl = `${process.env.R2_PUBLIC_URL}/${r2Key}`;

    // Update leave request dengan URL attachment baru
    const updatedLeaveRequest = await prisma.leaveRequest.update({
      where: { id: parseInt(id) },
      data: {
        attachmentUrl: fileUrl,
        updatedAt: new Date()
      }
    });

    console.log('Attachment uploaded successfully:', fileUrl);

    return NextResponse.json({
      message: 'Attachment uploaded successfully',
      url: fileUrl,
      leaveRequest: updatedLeaveRequest
    });
  } catch (error) {
    console.error('Error uploading file for leave request:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
