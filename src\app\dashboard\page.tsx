"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Navbar from "@/components/layout/Navbar";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useAuth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import {
  // Organization Overview icons
  Users,          // Total Employees
  Building2,      // Departments
  // UserCheck,      // <PERSON><PERSON><PERSON>tap (unused)
  // User,           // <PERSON><PERSON><PERSON> (unused)
  // UserMinus,      // Karyawan Honor (unused)
  UserPlus,       // New Employee
  TrendingUp,     // Trend indicators

  // Financial Overview icons
  // Wallet,         // Active Loans (unused)
  Wallet2,        // Total Savings
  CreditCard,     // Expenses
  CircleDollarSign, // Net Income
  DollarSign,     // Currency icon

  // Leave Management icons
  Calendar,       // Active Leaves
  Clock,          // Pending Approvals
  // CheckCircle,    // Currently on leave (unused)
  AlertCircle,    // Awaiting approval

  // Action icons
  // Plus,           // Add new (unused)
  // MoreHorizontal  // Menu actions (unused)
} from "lucide-react";

interface DepartmentCount {
  id: number;
  name: string;
  count: number;
}

interface DashboardData {
  organization: {
    employeeCount: number;
    departmentCount: number;
    tetapCount: number;
    kontrakCount: number;
    honorCount: number;
    tkCount: number;
    sdCount: number;
    smpCount: number;
    smaCount: number;
    smpSmaCount: number;
    staffCount: number;
    managerCount: number;
    nonAcademicDepartments: DepartmentCount[];
  };
  leaves: {
    totalLeaveRequests: number;
    activeLeaves: number;
    pendingLeaves: number;
    totalLateRequests: number;
    pendingLateRequests: number;
    totalExitRequests: number;
    pendingExitRequests: number;
  };
  financial: {
    totalPayroll: number;
    activeLoans: number;
  };
  koperasi: {
    totalMembers: number;
    internalMembers: number;
    externalMembers: number;
    activeMembers: number;
    inactiveMembers: number;
    totalSavings: number;
    activeLoans: number;
  };
  salary: {
    lastStaffUpdate: string | null;
    lastGuruUpdate: string | null;
    lastHonorUpdate: string | null;
  };
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        logger.debug('Fetching dashboard data...');
        const response = await fetch('/api/dashboard/overview', {
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        logger.debug(`Dashboard API response status: ${response.status}`);
        const data = await response.json();
        logger.debug('Dashboard data received successfully');

        if (!response.ok) {
          throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }

        if (data.error) {
          throw new Error(data.error);
        }

        setData(data);
      } catch (error) {
        logger.error('Error fetching dashboard data:', error);
        // Set default values for the dashboard
        setData({
          organization: {
            employeeCount: 0,
            departmentCount: 0,
            tetapCount: 0,
            kontrakCount: 0,
            honorCount: 0,
            tkCount: 0,
            sdCount: 0,
            smpCount: 0,
            smaCount: 0,
            smpSmaCount: 0,
            staffCount: 0,
            managerCount: 0,
            nonAcademicDepartments: []
          },
          leaves: {
            totalLeaveRequests: 0,
            activeLeaves: 0,
            pendingLeaves: 0,
            totalLateRequests: 0,
            pendingLateRequests: 0,
            totalExitRequests: 0,
            pendingExitRequests: 0
          },
          financial: {
            totalPayroll: 0,
            activeLoans: 0
          },
          koperasi: {
            totalMembers: 0,
            internalMembers: 0,
            externalMembers: 0,
            activeMembers: 0,
            inactiveMembers: 0,
            totalSavings: 0,
            activeLoans: 0
          },
          salary: {
            lastStaffUpdate: null,
            lastGuruUpdate: null,
            lastHonorUpdate: null
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-lg">Loading dashboard data...</div>
      </div>
    );
  }

  return (
    <ProtectedRoute requiredRole="ADMIN">
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground mt-2">
              Overview of employee management system
            </p>
          </div>

          {/* Organization Overview */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Organization Overview</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
                  <Users className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.organization.employeeCount}</div>
                  <div className="grid grid-cols-3 gap-1 mt-2">
                    <div className="text-xs">
                      <span className="font-semibold text-green-600">{data?.organization.tetapCount}</span> Tetap
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-orange-600">{data?.organization.kontrakCount}</span> Kontrak
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-purple-600">{data?.organization.honorCount}</span> Honor
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Akademik</CardTitle>
                  <Building2 className="h-4 w-4 text-indigo-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{(data?.organization.tkCount || 0) + (data?.organization.sdCount || 0) + (data?.organization.smpCount || 0) + (data?.organization.smaCount || 0) + (data?.organization.smpSmaCount || 0)}</div>
                  <div className="grid grid-cols-2 gap-1 mt-2">
                    <div className="text-xs">
                      <span className="font-semibold text-indigo-600">{data?.organization.tkCount}</span> TK
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-indigo-600">{data?.organization.sdCount}</span> SD
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-indigo-600">{data?.organization.smpCount}</span> SMP
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-indigo-600">{data?.organization.smaCount}</span> SMA
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-indigo-600">{data?.organization.smpSmaCount}</span> SMP-SMA
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Non Akademik</CardTitle>
                  <Building2 className="h-4 w-4 text-cyan-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.organization.staffCount}</div>
                  <div className="grid grid-cols-2 gap-1 mt-2 max-h-24 overflow-y-auto">
                    {data?.organization.nonAcademicDepartments ? (
                      data.organization.nonAcademicDepartments
                        .filter(dept => dept.count > 0)
                        .sort((a, b) => b.count - a.count)
                        .map(dept => (
                          <div key={dept.id} className="text-xs">
                            <span className="font-semibold text-cyan-600">{dept.count}</span> {dept.name}
                          </div>
                        ))
                    ) : (
                      <div className="text-xs text-cyan-600 flex items-center">
                        <TrendingUp className="h-4 w-4 mr-1" />
                        Employees outside academic departments
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Manager</CardTitle>
                  <UserPlus className="h-4 w-4 text-emerald-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.organization.managerCount}</div>
                  <p className="text-xs text-emerald-600 flex items-center mt-2">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    Employees with managerial positions
                  </p>
                </CardContent>
              </Card>


            </div>
          </div>

          {/* Leave Management */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Leave Management</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Leave Requests</CardTitle>
                  <Calendar className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.leaves.totalLeaveRequests}</div>
                  <div className="grid grid-cols-2 gap-1 mt-2">
                    <div className="text-xs">
                      <span className="font-semibold text-green-600">{data?.leaves.activeLeaves}</span> Active
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-orange-600">{data?.leaves.pendingLeaves}</span> Pending
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Late Requests</CardTitle>
                  <Clock className="h-4 w-4 text-amber-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.leaves.totalLateRequests}</div>
                  <p className="text-xs text-amber-600 flex items-center mt-2">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {data?.leaves.pendingLateRequests} Pending approvals
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Exit Requests</CardTitle>
                  <Clock className="h-4 w-4 text-rose-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.leaves.totalExitRequests}</div>
                  <p className="text-xs text-rose-600 flex items-center mt-2">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {data?.leaves.pendingExitRequests} Pending approvals
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Koperasi Overview */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Koperasi Overview</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Members</CardTitle>
                  <Users className="h-4 w-4 text-teal-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.koperasi.totalMembers}</div>
                  <div className="grid grid-cols-2 gap-1 mt-2">
                    <div className="text-xs">
                      <span className="font-semibold text-teal-600">{data?.koperasi.internalMembers}</span> Internal
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-teal-600">{data?.koperasi.externalMembers}</span> External
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-teal-600">{data?.koperasi.activeMembers}</span> Active
                    </div>
                    <div className="text-xs">
                      <span className="font-semibold text-teal-600">{data?.koperasi.inactiveMembers}</span> Inactive
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Savings</CardTitle>
                  <Wallet2 className="h-4 w-4 text-rose-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('id-ID', {
                      style: 'currency',
                      currency: 'IDR',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(data?.koperasi.totalSavings || 0)}
                  </div>
                  <p className="text-xs text-rose-600 flex items-center mt-2">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    Total member savings
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Loans</CardTitle>
                  <CreditCard className="h-4 w-4 text-amber-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.koperasi.activeLoans}</div>
                  <p className="text-xs text-amber-600 flex items-center mt-2">
                    <CircleDollarSign className="h-4 w-4 mr-1" />
                    Current active loans
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Salary Overview */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Salary Overview</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Staff Salary</CardTitle>
                  <DollarSign className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-medium mt-2">
                    Last Update:
                  </div>
                  <div className="text-base font-bold">
                    {data?.salary.lastStaffUpdate ? new Date(data.salary.lastStaffUpdate).toLocaleDateString('id-ID') : 'No data'}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Guru Salary</CardTitle>
                  <DollarSign className="h-4 w-4 text-emerald-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-medium mt-2">
                    Last Update:
                  </div>
                  <div className="text-base font-bold">
                    {data?.salary.lastGuruUpdate ? new Date(data.salary.lastGuruUpdate).toLocaleDateString('id-ID') : 'No data'}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Honor Salary</CardTitle>
                  <DollarSign className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-medium mt-2">
                    Last Update:
                  </div>
                  <div className="text-base font-bold">
                    {data?.salary.lastHonorUpdate ? new Date(data.salary.lastHonorUpdate).toLocaleDateString('id-ID') : 'No data'}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}


