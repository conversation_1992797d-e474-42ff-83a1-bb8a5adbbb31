import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { SalaryGuru } from "@/lib/types";

// Define schema for form validation
const formSchema = z.object({
  nama: z.string().min(1, "Nama is required"),
  gp: z.coerce.number().min(0, "GP must be a positive number"),
  load: z.coerce.number().optional(),
  hm: z.coerce.number().optional(),
  nominal: z.coerce.number().optional(),
  jlh_xc: z.coerce.number().optional(),
  jab: z.coerce.number().optional(),
  walas: z.coerce.number().optional(),
  pemb_osis: z.coerce.number().optional(),
  xc: z.coerce.number().optional(),
  menggantikan: z.coerce.number().optional(),
  tot: z.coerce.number().optional(),
  sl: z.boolean().optional(),
  vl: z.boolean().optional(),
  awol: z.boolean().optional(),
  jlh_sl: z.coerce.number().optional(),
  jlh_vl: z.coerce.number().optional(),
  jlh_awol: z.coerce.number().optional(),
  freq: z.boolean().optional(),
  minute: z.boolean().optional(),
  jlh_freq: z.coerce.number().optional(),
  jlh_minute: z.coerce.number().optional(),
  pot_abs: z.coerce.number().optional(),
  pot_ngajar: z.coerce.number().optional(),
  bruto: z.coerce.number().optional(),
  pot_bpjstku: z.coerce.number().optional(),
  pot_bpjskes: z.coerce.number().optional(),
  netto_stlh_bpjs: z.coerce.number().optional(),
  pph21: z.coerce.number().optional(),
  pinj_lainnya: z.coerce.number().optional(),
  iuran_wajib: z.coerce.number().optional(),
  pinj_kop: z.coerce.number().optional(),
  piutang: z.coerce.number().optional(),
  pot_bank: z.coerce.number().optional(),
  tot_pot: z.coerce.number().optional(),
  gaji_netto: z.coerce.number().min(0, "Gaji netto must be a positive number"),
});

type FormValues = z.infer<typeof formSchema>;

interface GuruSalaryFormProps {
  initialData?: SalaryGuru;
  onSubmit: (data: FormValues) => void;
}

export function GuruSalaryForm({ initialData, onSubmit }: GuruSalaryFormProps) {
  const [isCalculating, setIsCalculating] = useState(false);

  // Initialize form with default values or initial data
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData ? {
      nama: initialData.nama,
      gp: initialData.gp,
      load: initialData.load || 0,
      hm: initialData.hm || 0,
      nominal: initialData.nominal || 0,
      jlh_xc: initialData.jlh_xc || 0,
      jab: initialData.jab || 0,
      walas: initialData.walas || 0,
      pemb_osis: initialData.pemb_osis || 0,
      xc: initialData.xc || 0,
      menggantikan: initialData.menggantikan || 0,
      tot: initialData.tot || 0,
      sl: initialData.sl || false,
      vl: initialData.vl || false,
      awol: initialData.awol || false,
      jlh_sl: initialData.jlh_sl || 0,
      jlh_vl: initialData.jlh_vl || 0,
      jlh_awol: initialData.jlh_awol || 0,
      freq: initialData.freq || false,
      minute: initialData.minute || false,
      jlh_freq: initialData.jlh_freq || 0,
      jlh_minute: initialData.jlh_minute || 0,
      pot_abs: initialData.pot_abs || 0,
      pot_ngajar: initialData.pot_ngajar || 0,
      bruto: initialData.bruto || 0,
      pot_bpjstku: initialData.pot_bpjstku || 0,
      pot_bpjskes: initialData.pot_bpjskes || 0,
      netto_stlh_bpjs: initialData.netto_stlh_bpjs || 0,
      pph21: initialData.pph21 || 0,
      pinj_lainnya: initialData.pinj_lainnya || 0,
      iuran_wajib: initialData.iuran_wajib || 0,
      pinj_kop: initialData.pinj_kop || 0,
      piutang: initialData.piutang || 0,
      pot_bank: initialData.pot_bank || 0,
      tot_pot: initialData.tot_pot || 0,
      gaji_netto: initialData.gaji_netto,
    } : {
      nama: "",
      gp: 0,
      load: 0,
      hm: 0,
      nominal: 0,
      jlh_xc: 0,
      jab: 0,
      walas: 0,
      pemb_osis: 0,
      xc: 0,
      menggantikan: 0,
      tot: 0,
      sl: false,
      vl: false,
      awol: false,
      jlh_sl: 0,
      jlh_vl: 0,
      jlh_awol: 0,
      freq: false,
      minute: false,
      jlh_freq: 0,
      jlh_minute: 0,
      pot_abs: 0,
      pot_ngajar: 0,
      bruto: 0,
      pot_bpjstku: 0,
      pot_bpjskes: 0,
      netto_stlh_bpjs: 0,
      pph21: 0,
      pinj_lainnya: 0,
      iuran_wajib: 0,
      pinj_kop: 0,
      piutang: 0,
      pot_bank: 0,
      tot_pot: 0,
      gaji_netto: 0,
    },
  });

  // Calculate totals
  const calculateTotals = () => {
    setIsCalculating(true);
    
    try {
      const values = form.getValues();
      
      // Calculate nominal (if load and hm are provided)
      if (values.load && values.hm) {
        const nominal = values.load * values.hm;
        form.setValue("nominal", nominal);
      }
      
      // Calculate tot (total earnings)
      const tot = (
        (values.gp || 0) +
        (values.nominal || 0) +
        (values.jab || 0) +
        (values.walas || 0) +
        (values.pemb_osis || 0) +
        (values.xc || 0) +
        (values.menggantikan || 0) +
        (values.jlh_xc || 0)
      );
      form.setValue("tot", tot);
      
      // Calculate bruto (gross after attendance deductions)
      const bruto = tot - (values.pot_abs || 0) - (values.pot_ngajar || 0);
      form.setValue("bruto", bruto);
      
      // Calculate netto_stlh_bpjs
      const nettoStlhBpjs = bruto - (values.pot_bpjstku || 0) - (values.pot_bpjskes || 0);
      form.setValue("netto_stlh_bpjs", nettoStlhBpjs);
      
      // Calculate tot_pot (total deductions)
      const totPot = (
        (values.pot_abs || 0) +
        (values.pot_ngajar || 0) +
        (values.pot_bpjstku || 0) +
        (values.pot_bpjskes || 0) +
        (values.pph21 || 0) +
        (values.pinj_lainnya || 0) +
        (values.iuran_wajib || 0) +
        (values.pinj_kop || 0) +
        (values.piutang || 0) +
        (values.pot_bank || 0)
      );
      form.setValue("tot_pot", totPot);
      
      // Calculate gaji_netto (net salary)
      const gajiNetto = tot - totPot;
      form.setValue("gaji_netto", gajiNetto);
    } catch (error) {
      console.error("Error calculating totals:", error);
    } finally {
      setIsCalculating(false);
    }
  };

  // Handle form submission
  const handleSubmit = (data: FormValues) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 max-h-[70vh] overflow-y-auto p-1">
        <div className="grid grid-cols-2 gap-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="nama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nama</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gp"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GP</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="load"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Load</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hm"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>HM</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="nominal"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nominal</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="jlh_xc"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Jumlah XC</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="jab"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Jabatan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="walas"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Wali Kelas</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pemb_osis"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pembina OSIS</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="xc"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>XC</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="menggantikan"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Menggantikan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tot"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Deductions and Final Calculations */}
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="sl"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>SL</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="vl"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>VL</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="awol"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>AWOL</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="jlh_sl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah SL</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="jlh_vl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah VL</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="jlh_awol"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah AWOL</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="freq"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Freq</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="minute"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Minute</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="jlh_freq"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Freq</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="jlh_minute"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Minute</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="pot_abs"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Potongan Absensi</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pot_ngajar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Potongan Ngajar</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="bruto"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bruto</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="pot_bpjstku"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Potongan BPJS TK</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pot_bpjskes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Potongan BPJS Kesehatan</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="netto_stlh_bpjs"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Netto Setelah BPJS</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pph21"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>PPh21</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="pinj_lainnya"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pinjaman Lainnya</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="iuran_wajib"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Iuran Wajib</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="pinj_kop"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pinjaman Koperasi</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="piutang"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Piutang</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="pot_bank"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Potongan Bank</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tot_pot"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Potongan</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gaji_netto"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gaji Netto</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-between">
          <Button 
            type="button" 
            variant="outline" 
            onClick={calculateTotals}
            disabled={isCalculating}
          >
            {isCalculating ? "Calculating..." : "Calculate Totals"}
          </Button>
          <Button type="submit">Submit</Button>
        </div>
      </form>
    </Form>
  );
}
