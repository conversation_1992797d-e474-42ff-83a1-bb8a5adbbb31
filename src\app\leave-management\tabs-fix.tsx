// Kode untuk memperbaiki tab Leave Types agar sejajar dengan tab lainnya

// Kode yang salah:
/*
<TabsList className="grid w-full grid-cols-4">
  <TabsTrigger value="leave-requests">Leave Requests</TabsTrigger>
  <TabsTrigger value="late">Late Requests</TabsTrigger>
  <TabsTrigger value="exit">Exit From School</TabsTrigger>
  <TabsTrigger value="calendar">Calendar</TabsTrigger>
  {userRole === 'ADMIN' && (
    <TabsTrigger value="leave-types">Leave Types</TabsTrigger>
  )}
</TabsList>
*/

// Kode yang benar:
/*
<TabsList className="grid w-full grid-cols-5">
  <TabsTrigger value="leave-requests">Leave Requests</TabsTrigger>
  <TabsTrigger value="late">Late Requests</TabsTrigger>
  <TabsTrigger value="exit">Exit From School</TabsTrigger>
  <TabsTrigger value="calendar">Calendar</TabsTrigger>
  <TabsTrigger value="leave-types">Leave Types</TabsTrigger>
</TabsList>
*/

// Atau alternatif lain:
/*
<TabsList className="grid w-full md:w-auto md:inline-flex grid-cols-2 md:grid-cols-none mb-6">
  <TabsTrigger value="leave-requests" className="flex items-center gap-2">
    <FileText className="h-4 w-4" />
    Leave Requests
  </TabsTrigger>
  <TabsTrigger value="late" className="flex items-center gap-2">
    <Clock className="h-4 w-4" />
    Late
  </TabsTrigger>
  <TabsTrigger value="exit" className="flex items-center gap-2">
    <Clock className="h-4 w-4" />
    Exit From School
  </TabsTrigger>
  <TabsTrigger value="calendar" className="flex items-center gap-2">
    <CalendarIcon className="h-4 w-4" />
    Calendar View
  </TabsTrigger>
  <TabsTrigger value="leave-types" className="flex items-center gap-2">
    <FileText className="h-4 w-4" />
    Leave Types
  </TabsTrigger>
</TabsList>
*/
