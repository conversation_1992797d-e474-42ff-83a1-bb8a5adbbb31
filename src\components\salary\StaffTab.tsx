import { useState, useEffect } from "react";
import { SalaryStaff } from "@/lib/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, MoreHorizontal, Eye, Pencil, Trash2, Download, Search, FileText } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { ImportSalaryDialog } from "./ImportSalaryDialog";
import { StaffSalarySlipDialog } from "./StaffSalarySlipDialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { StaffSalaryForm } from "@/components/forms/staff-salary-form";
import { format } from "date-fns";
import { id } from "date-fns/locale";

interface StaffTabProps {
  data: SalaryStaff[];
  loading: boolean;
  userRole?: string;
  onSubmit: (data: any) => Promise<void>;
  onRefresh: () => Promise<void>;
}

export function StaffTab({ data, loading, userRole, onSubmit, onRefresh }: StaffTabProps) {
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isSlipOpen, setIsSlipOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<SalaryStaff | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<SalaryStaff[]>([]);
  const itemsPerPage = 18;

  // Filter data based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredData(data);
    } else {
      const filtered = data.filter(item =>
        item.nama.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredData(filtered);
    }
    setCurrentPage(1); // Reset to first page when search changes
  }, [searchTerm, data]);

  // Format currency
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return "-";
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (date: Date) => {
    return format(new Date(date), "MMMM yyyy", { locale: id });
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );



  // Handle edit
  const handleEdit = (item: SalaryStaff) => {
    setSelectedItem(item);
    setIsEditOpen(true);
  };

  // Handle view
  const handleView = (item: SalaryStaff) => {
    setSelectedItem(item);
    setIsViewOpen(true);
  };

  // Handle delete
  const handleDelete = (item: SalaryStaff) => {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  };

  // Handle slip
  const handleSlip = (item: SalaryStaff) => {
    setSelectedItem(item);
    setIsSlipOpen(true);
  };

  // Handle delete confirmation
  const confirmDelete = async () => {
    if (!selectedItem) return;

    try {
      const response = await fetch(`/api/salary/staff/${selectedItem.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete staff salary data');
      }

      setIsDeleteOpen(false);
      toast({
        title: "Success",
        description: "Staff salary data deleted successfully",
      });
      onRefresh();
    } catch (error) {
      console.error('Error deleting staff salary:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to delete staff salary data',
        variant: "destructive",
      });
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const response = await fetch('/api/salary/staff/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: data,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to export staff salary data');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'staff_salary_export.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting staff salary:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Staff Salary</h2>
        <div className="flex gap-2">
          {userRole === "ADMIN" && (
            <>
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button onClick={() => setIsImportOpen(true)}>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Search bar - only for ADMIN */}
      {userRole === "ADMIN" && (
        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Cari berdasarkan nama..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {searchTerm && (
            <Button variant="ghost" onClick={() => setSearchTerm("")}>Clear</Button>
          )}
        </div>
      )}

      <div className="rounded-md border overflow-auto">
        <div className="min-w-max overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="whitespace-nowrap">Nama</TableHead>
                <TableHead className="text-right whitespace-nowrap">Actions</TableHead>
                <TableHead className="whitespace-nowrap">Periode</TableHead>
                <TableHead className="whitespace-nowrap">Gaji Netto</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center">Loading...</TableCell>
                </TableRow>
              ) : paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center">No data found</TableCell>
                </TableRow>
              ) : (
                paginatedData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.nama}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {userRole === "ADMIN" && (
                          <DropdownMenuItem onClick={() => handleView(item)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleSlip(item)}>
                          <FileText className="mr-2 h-4 w-4" />
                          Slip Gaji
                        </DropdownMenuItem>
                        {userRole === "ADMIN" && (
                          <>
                            <DropdownMenuItem onClick={() => handleEdit(item)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(item)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                  <TableCell>{formatDate(item.period)}</TableCell>
                  <TableCell>{formatCurrency(item.gaji_netto)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        </div>
      </div>

      {totalPages > 1 && (
        <Pagination className="mt-4">
          <PaginationContent>
            <PaginationItem>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
            </PaginationItem>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <PaginationItem key={page}>
                <Button
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              </PaginationItem>
            ))}
            <PaginationItem>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}

      {/* Import Dialog */}
      <ImportSalaryDialog
        open={isImportOpen}
        onOpenChange={setIsImportOpen}
        onImportSuccess={onRefresh}
        type="staff"
      />

      {/* Edit Dialog */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Staff Salary</DialogTitle>
            <DialogDescription>
              Edit staff salary data
            </DialogDescription>
          </DialogHeader>
          {selectedItem && (
            <StaffSalaryForm
              initialData={selectedItem}
              onSubmit={(data) => {
                onSubmit({ ...data, id: selectedItem.id });
                setIsEditOpen(false);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Staff Salary Details</DialogTitle>
            <DialogDescription>
              View staff salary details
            </DialogDescription>
          </DialogHeader>
          {selectedItem && (
            <div className="grid grid-cols-2 gap-4 max-h-[70vh] overflow-y-auto">
              <div className="space-y-2">
                <div>
                  <h3 className="font-medium">Nama</h3>
                  <p>{selectedItem.nama}</p>
                </div>
                <div>
                  <h3 className="font-medium">GP</h3>
                  <p>{formatCurrency(selectedItem.gp)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Gol Kontrak</h3>
                  <p>{formatCurrency(selectedItem.gol_kontrak)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Gol Tetap</h3>
                  <p>{formatCurrency(selectedItem.gol_tetap)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Beban Kerja</h3>
                  <p>{formatCurrency(selectedItem.beban_kerja)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Insentif</h3>
                  <p>{formatCurrency(selectedItem.insentif)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Tunjangan Keahlian</h3>
                  <p>{formatCurrency(selectedItem.tunj_keahlian)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Tunjangan Jabatan</h3>
                  <p>{formatCurrency(selectedItem.tunj_jab)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Total Gross</h3>
                  <p>{formatCurrency(selectedItem.tot_gross)}</p>
                </div>
                <div>
                  <h3 className="font-medium">BPJS TK Yayasan</h3>
                  <p>{formatCurrency(selectedItem.bpjstku_yay)}</p>
                </div>
                <div>
                  <h3 className="font-medium">BPJS Kesehatan Yayasan</h3>
                  <p>{formatCurrency(selectedItem.bpjskes_yay)}</p>
                </div>
                <div>
                  <h3 className="font-medium">PPh Yayasan</h3>
                  <p>{formatCurrency(selectedItem.pph_yay)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Total Gross Yayasan</h3>
                  <p>{formatCurrency(selectedItem.tot_gross_yay)}</p>
                </div>
              </div>
              <div className="space-y-2">
                <div>
                  <h3 className="font-medium">AWOL</h3>
                  <p>{selectedItem.awol ? 'Ya' : 'Tidak'}</p>
                </div>
                <div>
                  <h3 className="font-medium">Jumlah AWOL</h3>
                  <p>{selectedItem.jlh_awol}</p>
                </div>
                <div>
                  <h3 className="font-medium">Freq</h3>
                  <p>{selectedItem.freq ? 'Ya' : 'Tidak'}</p>
                </div>
                <div>
                  <h3 className="font-medium">Minute</h3>
                  <p>{selectedItem.minute ? 'Ya' : 'Tidak'}</p>
                </div>
                <div>
                  <h3 className="font-medium">Jumlah Freq</h3>
                  <p>{selectedItem.jlh_freq}</p>
                </div>
                <div>
                  <h3 className="font-medium">Jumlah Minute</h3>
                  <p>{selectedItem.jlh_minute}</p>
                </div>
                <div>
                  <h3 className="font-medium">Potongan Absensi</h3>
                  <p>{formatCurrency(selectedItem.pot_abs)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Gaji Setelah Potongan Absensi</h3>
                  <p>{formatCurrency(selectedItem.gaji_stlh_pot_abs)}</p>
                </div>
                <div>
                  <h3 className="font-medium">BPJS TK</h3>
                  <p>{formatCurrency(selectedItem.bpjstku)}</p>
                </div>
                <div>
                  <h3 className="font-medium">BPJS Kesehatan</h3>
                  <p>{formatCurrency(selectedItem.bpjskes)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Nett Setelah BPJS</h3>
                  <p>{formatCurrency(selectedItem.nett_stlh_bpjs)}</p>
                </div>
                <div>
                  <h3 className="font-medium">PPh</h3>
                  <p>{formatCurrency(selectedItem.pph)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Pinjaman Lain</h3>
                  <p>{formatCurrency(selectedItem.pinj_lain)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Iuran Wajib</h3>
                  <p>{formatCurrency(selectedItem.iuran_wajib)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Pinjaman Koperasi</h3>
                  <p>{formatCurrency(selectedItem.pinj_kop)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Piutang</h3>
                  <p>{formatCurrency(selectedItem.piutang)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Potongan Bank</h3>
                  <p>{formatCurrency(selectedItem.pot_bank)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Total Potongan</h3>
                  <p>{formatCurrency(selectedItem.tot_pot)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Gaji Netto</h3>
                  <p>{formatCurrency(selectedItem.gaji_netto)}</p>
                </div>
                <div>
                  <h3 className="font-medium">Periode</h3>
                  <p>{formatDate(selectedItem.period)}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this staff salary data? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Salary Slip Dialog */}
      <StaffSalarySlipDialog
        open={isSlipOpen}
        onOpenChange={setIsSlipOpen}
        data={selectedItem}
      />
    </div>
  );
}
