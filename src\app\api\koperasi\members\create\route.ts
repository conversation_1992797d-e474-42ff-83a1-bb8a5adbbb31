/**
 * API Route: POST /api/koperasi/members/create
 *
 * Deskripsi: Membuat anggota koperasi baru
 * Penggunaan: Form tambah anggota koperasi
 *
 * Body:
 * - employeeId: <PERSON> karyawan (number)
 * - monthlyContribution: <PERSON><PERSON><PERSON> bulanan (number)
 * - oneTimeContribution: Iuran satu kali (number, optional)
 * - optionalContribution: Iuran opsional (number, optional)
 *
 * Response:
 * - 201: Anggota koperasi berhasil dibuat
 * - 400: Data tidak valid
 * - 401: Tidak terautentikasi
 * - 403: Tidak memiliki izin
 * - 500: Error server
 */

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { db } from '@/lib/db';
import { hash } from 'bcryptjs';

// Use the shared Prisma client instance
const prisma = db;

export async function POST(request: Request) {
  try {
    // Verify admin access
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');
    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;

    if (userRole !== 'ADMIN' && userRole !== 'OPERATOR_KOP') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    console.log('POST /api/koperasi/members/create - Processing request', JSON.stringify(data, null, 2));

    const {
      memberType,
      employeeId,
      firstName,
      lastName,
      email,
      phone,
      address,
      monthlyContribution,
      oneTimeContribution,
      optionalContribution,
      joinDate,
      notes
    } = data;

    // Validate required fields
    if (!monthlyContribution) {
      return NextResponse.json(
        { error: 'Monthly contribution is required' },
        { status: 400 }
      );
    }

    // Define actualEmployeeId at a higher scope so it's accessible throughout the function
    let actualEmployeeId;

    // Validate based on member type
    if (memberType === 'employee') {
      if (!employeeId) {
        return NextResponse.json(
          { error: 'Employee ID is required for employee members' },
          { status: 400 }
        );
      }

      // Validate employee exists - try to find by ID first, then by employeeId if that fails
      let employee;

      // First try to parse as integer for database ID lookup
      const employeeIdNum = parseInt(employeeId);

      console.log(`Looking up employee with input: "${employeeId}", parsed as number: ${employeeIdNum}`);

      if (!isNaN(employeeIdNum)) {
        // Try to find by numeric ID first
        console.log(`Searching for employee by ID: ${employeeIdNum}`);
        employee = await prisma.employee.findUnique({
          where: { id: employeeIdNum }
        });

        if (employee) {
          console.log(`Found employee by ID ${employeeIdNum}: ${employee.firstName} ${employee.lastName}`);
        } else {
          console.log(`No employee found with ID: ${employeeIdNum}`);
        }
      }

      // If not found by ID, try to find by employeeId (string identifier)
      if (!employee) {
        console.log(`Searching for employee by employeeId: "${employeeId}"`);
        employee = await prisma.employee.findFirst({
          where: { employeeId: employeeId.toString() }
        });

        if (employee) {
          console.log(`Found employee by employeeId "${employeeId}": ${employee.firstName} ${employee.lastName} (ID: ${employee.id})`);
        } else {
          console.log(`No employee found with employeeId: "${employeeId}"`);
        }
      }

      if (!employee) {
        console.error(`Employee not found with ID or employeeId: ${employeeId}`);
        return NextResponse.json(
          { error: 'Employee not found' },
          { status: 404 }
        );
      }

      // Use the actual employee ID for further processing
      actualEmployeeId = employee.id;
      console.log(`Using actual employee ID for member creation: ${actualEmployeeId}`);

    } else if (memberType === 'external') {
      // Validate external member data
      if (!firstName || !lastName) {
        return NextResponse.json(
          { error: 'First name and last name are required for external members' },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid member type' },
        { status: 400 }
      );
    }

    // Check if employee is already a member (for employee type)
    if (memberType === 'employee' && employeeId) {
      if (!actualEmployeeId) {
        console.error('actualEmployeeId is undefined when checking for existing member');
        return NextResponse.json(
          { error: 'Internal server error: Employee ID not properly resolved' },
          { status: 500 }
        );
      }

      const existingMember = await prisma.koperasiMember.findUnique({
        where: { employeeId: actualEmployeeId }
      });

      if (existingMember) {
        return NextResponse.json(
          { error: 'Employee is already a koperasi member' },
          { status: 400 }
        );
      }
    }

    // Generate member code for display purposes only (not stored in database)
    const memberCount = await prisma.koperasiMember.count();
    const displayMemberCode = memberType === 'employee' ?
      `M${(memberCount + 1).toString().padStart(3, '0')}` :
      `E${(memberCount + 1).toString().padStart(3, '0')}`;

    console.log(`Generated display member code: ${displayMemberCode} (not stored in database)`);

    // Create new koperasi member and user account in a transaction
    const result = await prisma.$transaction(async (tx) => {

      // Konversi nilai kontribusi ke Decimal tanpa mengubah nilai asli
      const monthlyContributionValue = parseFloat(monthlyContribution) || 0;
      const oneTimeContributionValue = parseFloat(oneTimeContribution) || 0;
      const optionalContributionValue = parseFloat(optionalContribution) || 0;

      // Hitung total savings awal
      const totalSavings = monthlyContributionValue + oneTimeContributionValue + optionalContributionValue;

      // Prepare member data based on type - do NOT include memberCode as it's not in the schema
      const memberData: any = {
        joinDate: joinDate ? new Date(joinDate) : new Date(),
        monthlyContribution: monthlyContributionValue,
        oneTimeContribution: oneTimeContributionValue > 0 ? oneTimeContributionValue : null,
        optionalContribution: optionalContributionValue > 0 ? optionalContributionValue : null,
        totalSavings: totalSavings,
        status: 'active',
        notes: notes || null
      };

      // Add type-specific fields
      if (memberType === 'employee' && employeeId) {
        if (!actualEmployeeId) {
          console.error('actualEmployeeId is undefined when creating member');
          throw new Error('Employee ID not properly resolved');
        }
        memberData.employeeId = actualEmployeeId;
      } else {
        // For external members
        memberData.firstName = firstName;
        memberData.lastName = lastName;
        memberData.email = email;
        memberData.phone = phone;
        memberData.address = address;
      }
      // Log the data we're about to send to Prisma
      console.log('Creating koperasi member with data:', JSON.stringify(memberData, null, 2));

      // Create the koperasi member
      const member = await tx.koperasiMember.create({
        data: memberData,
        include: {
          employee: memberType === 'employee'
        }
      });

      console.log('Successfully created koperasi member with ID:', member.id);

      // If it's an employee member, create a user account if it doesn't exist
      if (memberType === 'employee' && employeeId) {
        if (!actualEmployeeId) {
          console.error('actualEmployeeId is undefined when checking for existing user');
          throw new Error('Employee ID not properly resolved');
        }

        // Check if user already exists for this employee
        const existingUser = await tx.user.findUnique({
          where: { employeeId: actualEmployeeId }
        });

        if (!existingUser) {
          // Get employee details - we already have the employee object, but let's ensure we have the latest data
          const employee = await tx.employee.findUnique({
            where: { id: actualEmployeeId }
          });

          if (employee) {
            // Hash the default password
            const hashedPassword = await hash('defaultpass123', 10);

            // Create user account
            await tx.user.create({
              data: {
                username: employee.employeeId, // Use employee ID as username
                password: hashedPassword,
                role: 'EMPLOYEE', // Standard employee role
                email: employee.email,
                employeeId: employee.id,
                isActive: true
              }
            });

            console.log(`Created user account for employee ${employee.employeeId} with default password 'defaultpass123'`);
          }
        } else {
          console.log(`User account already exists for employee ID ${employeeId}`);
        }
      }

      return member;
    });

    const member = result;

    // Get contribution values for savings transactions tanpa mengubah nilai asli
    const monthlyContributionVal = parseFloat(monthlyContribution) || 0;
    const oneTimeContributionVal = parseFloat(oneTimeContribution) || 0;
    const optionalContributionVal = parseFloat(optionalContribution) || 0;

    // Jika ada kontribusi awal, buat transaksi savings
    if (oneTimeContributionVal > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: member.id,
          amount: oneTimeContributionVal,
          type: 'deposit',
          contributionType: 'one_time',
          date: new Date(),
          notes: 'Initial one-time contribution'
        }
      });
    }

    if (monthlyContributionVal > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: member.id,
          amount: monthlyContributionVal,
          type: 'deposit',
          contributionType: 'monthly',
          date: new Date(),
          notes: 'Initial monthly contribution'
        }
      });
    }

    if (optionalContributionVal > 0) {
      await prisma.koperasiSaving.create({
        data: {
          memberId: member.id,
          amount: optionalContributionVal,
          type: 'deposit',
          contributionType: 'optional',
          date: new Date(),
          notes: 'Initial optional contribution'
        }
      });
    }

    // Prepare response data
    const responseData: any = {
      success: true,
      message: 'Member registered successfully',
      member: {
        id: member.id,
        member_code: displayMemberCode, // Use the display code we generated (not stored in DB)
        member_type: memberType,
        join_date: member.joinDate,
        monthly_contribution: Number(member.monthlyContribution),
        one_time_contribution: member.oneTimeContribution ? Number(member.oneTimeContribution) : 0,
        optional_contribution: member.optionalContribution ? Number(member.optionalContribution) : 0,
        total_savings: Number(member.totalSavings),
        status: member.status,
        notes: member.notes
      }
    };

    // Add type-specific fields to response
    if (memberType === 'employee' && member.employee) {
      responseData.member.employee_id = member.employeeId;
      responseData.member.employee_name = `${member.employee.firstName} ${member.employee.lastName}`;
    }

    return NextResponse.json(responseData, { status: 201 });
  } catch (error) {
    console.error('Failed to create koperasi member:', error);

    // Provide more detailed error information
    let errorMessage = 'Failed to create koperasi member';
    let errorDetails = {};
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        stack: error.stack,
        name: error.name
      };

      // Set specific status codes for certain errors
      if (error.message.includes('Employee not found') ||
          error.message.includes('Employee ID not properly resolved')) {
        statusCode = 404;
      } else if (error.message.includes('already a koperasi member')) {
        statusCode = 400;
      }
    }

    console.log(`Returning error response: ${errorMessage} with status ${statusCode}`);

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString()
      },
      { status: statusCode }
    );
  } finally {
    // Don't disconnect the shared Prisma client
    // The shared client is managed by the db.ts module
  }
}
