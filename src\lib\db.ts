import { PrismaClient } from '@prisma/client';

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
// Learn more: https://pris.ly/d/help/next-js-best-practices

// Define a global type for PrismaClient
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Create a new PrismaClient instance if one doesn't exist already
export const db =
  globalForPrisma.prisma ||
  new PrismaClient({
    // In production, only log errors; in development, log everything
    log: process.env.NODE_ENV === 'development'
      ? ['query', 'error', 'warn']
      : ['error'],
    errorFormat: process.env.NODE_ENV === 'development' ? 'pretty' : 'minimal',
  });

// Save the PrismaClient instance to the global object in development
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db;

// Export the PrismaClient instance for use throughout the application
export const prisma = db;
