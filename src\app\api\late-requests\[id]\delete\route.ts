import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';



export async function DELETE(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const isAdmin = userData.role === 'ADMIN';
    // Get ID from params
    const id = params.id;

    // Check if late request exists
    const lateRequest = await prisma.lateRequest.findUnique({
      where: { id: parseInt(id) },
    });

    if (!lateRequest) {
      return NextResponse.json({ error: 'Late request not found' }, { status: 404 });
    }

    // Only admin can delete any late request
    // Regular users can only delete their own pending requests
    if (!isAdmin) {
      if (lateRequest.employeeId !== parseInt(userData.id)) {
        return NextResponse.json(
          { error: 'You can only delete your own late requests' },
          { status: 403 }
        );
      }

      if (lateRequest.status !== 'pending') {
        return NextResponse.json(
          { error: 'You can only delete pending late requests' },
          { status: 409 }
        );
      }
    }

    // Delete late request
    await prisma.lateRequest.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Late request deleted successfully' });
  } catch (error) {
    console.error('Error deleting late request:', error);
    return NextResponse.json(
      { error: 'Failed to delete late request' },
      { status: 500 }
    );
  }
}
