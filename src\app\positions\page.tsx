"use client";

import React, { useEffect, useState, useCallback } from "react";
import Navbar from "@/components/layout/Navbar";
import { Button } from "@/components/ui/button";
import { Plus, Search, Eye, Edit, Trash2 } from "lucide-react";
import { logger } from "@/lib/logger";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useAuth } from "@/lib/auth";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useToast } from "@/components/ui/use-toast";
import { PositionForm } from "@/components/forms/position-form";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Position {
  id: number;
  title: string;
  department?: {
    id: number;
    name: string;
  };
  departmentId: number;
  minSalary: number;
  maxSalary: number;
  description?: string;
}

export default function PositionsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const positionsPerPage = 10;
  const [searchTitle, setSearchTitle] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [departments, setDepartments] = useState<{ id: number; name: string; }[]>([]);

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null);

  // Fungsi untuk mendapatkan filtered positions
  const getFilteredPositions = useCallback(() => {
    let filteredPositions = [...positions];

    if (searchTitle) {
      filteredPositions = filteredPositions.filter(position =>
        position.title.toLowerCase().includes(searchTitle.toLowerCase())
      );
    }

    if (selectedDepartment !== 'all') {
      filteredPositions = filteredPositions.filter(position =>
        position.departmentId === parseInt(selectedDepartment)
      );
    }

    return filteredPositions.sort((a, b) => a.title.localeCompare(b.title));
  }, [positions, searchTitle, selectedDepartment]);

  // Update total pages ketika filter berubah
  useEffect(() => {
    const filteredPositions = getFilteredPositions();
    setTotalPages(Math.ceil(filteredPositions.length / positionsPerPage));
  }, [positions, searchTitle, selectedDepartment, getFilteredPositions, positionsPerPage]);

  const getCurrentPagePositions = () => {
    const filteredPositions = getFilteredPositions();
    const startIndex = (currentPage - 1) * positionsPerPage;
    const endIndex = startIndex + positionsPerPage;
    return filteredPositions.slice(startIndex, endIndex);
  };

  // Fetch departments
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const response = await fetch('/api/departments');
        if (!response.ok) throw new Error('Failed to fetch departments');
        const data = await response.json();
        setDepartments(data);
      } catch (error) {
        logger.error('Error fetching departments:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch departments"
        });
      }
    };

    fetchDepartments();
  }, [toast]);

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions');
      if (!response.ok) throw new Error('Failed to fetch positions');
      const data = await response.json();
      // Pastikan setiap posisi memiliki data department
      const positionsWithDepartment = data.map((position: any) => ({
        ...position,
        department: position.department || { id: position.departmentId, name: '' }
      }));
      setPositions(positionsWithDepartment);
    } catch (error) {
      logger.error('Error fetching positions:', error);
      toast({
        title: "Error",
        description: "Failed to fetch positions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPositions();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user || user.role !== 'ADMIN') {
    return <div>Unauthorized access</div>;
  }

  const formatSalary = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount);
  };

  const handleAddSubmit = async (formData: any) => {
    try {
      const response = await fetch('/api/positions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to add position');

      await fetchPositions();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Position added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add position",
        variant: "destructive",
      });
    }
  };

  const handleEditSubmit = async (formData: any) => {
    if (!selectedPosition) return;

    try {
      console.log('Sending data to API:', {
        ...formData,
        departmentId: parseInt(formData.departmentId)
      });

      const response = await fetch(`/api/positions/${selectedPosition.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          departmentId: parseInt(formData.departmentId) // Pastikan departmentId adalah number
        }),
      });

      if (!response.ok) throw new Error('Failed to update position');

      await fetchPositions();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Position updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update position",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (positionId: number) => {
    try {
      const response = await fetch(`/api/positions/${positionId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete position');
      }

      await fetchPositions();
      setIsDeleteDialogOpen(false);
      setSelectedPosition(null);

      toast({
        title: "Success",
        description: "Position deleted successfully",
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete position";

      toast({
        title: "Cannot Delete Position",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <ProtectedRoute requiredRole="ADMIN">
      <div className="min-h-screen bg-background">
        <Navbar userRole={user?.role} />
        <main className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold">Positions</h1>
            <p className="text-muted-foreground mt-2">
              Manage job positions and roles
            </p>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div className="w-full sm:w-64">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search positions..."
                  className="pl-8 w-full"
                  value={searchTitle}
                  onChange={(e) => setSearchTitle(e.target.value)}
                />
              </div>
            </div>

            {user?.role === "ADMIN" && (
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="w-full sm:w-auto whitespace-nowrap">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Position
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Position</DialogTitle>
                  </DialogHeader>
                  <ScrollArea className="h-[400px] pr-4">
                    <PositionForm
                      departments={departments}
                      onSubmit={handleAddSubmit}
                      onCancel={() => setIsAddDialogOpen(false)}
                    />
                  </ScrollArea>
                </DialogContent>
              </Dialog>
            )}
          </div>

          <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
            <div className="p-4 border-b">
              <h2 className="text-lg font-semibold">Position List</h2>
            </div>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Salary Range</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center">
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : positions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center">
                        No positions found
                      </TableCell>
                    </TableRow>
                  ) : (
                    getCurrentPagePositions().map((position) => (
                      <TableRow key={position.id}>
                        <TableCell>POS{String(position.id).padStart(3, '0')}</TableCell>
                        <TableCell>{position.title}</TableCell>
                        <TableCell>{position.department.name}</TableCell>
                        <TableCell>
                          {formatSalary(position.minSalary)} - {formatSalary(position.maxSalary)}
                        </TableCell>
                        <TableCell className="text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
                            onClick={() => {
                              setSelectedPosition(position);
                              setIsViewDialogOpen(true);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          {user?.role === "ADMIN" && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 mr-2"
                                onClick={() => {
                                  setSelectedPosition(position);
                                  setIsEditDialogOpen(true);
                                }}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-900 hover:bg-red-50"
                                onClick={() => {
                                  setSelectedPosition(position);
                                  setIsDeleteDialogOpen(true);
                                }}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </Button>
                            </>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
              <div className="flex items-center justify-center py-4">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          </div>

          {/* View Dialog */}
          <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Position Details</DialogTitle>
              </DialogHeader>
              {selectedPosition && (
                <div className="grid gap-4">
                  <div>
                    <h4 className="font-medium">Title</h4>
                    <p>{selectedPosition.title}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Department</h4>
                    <p>{selectedPosition.department.name}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Salary Range</h4>
                    <p>{formatSalary(selectedPosition.minSalary)} - {formatSalary(selectedPosition.maxSalary)}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Description</h4>
                    <p>{selectedPosition.description || "-"}</p>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Edit Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Position</DialogTitle>
              </DialogHeader>
              {selectedPosition && (
                <PositionForm
                  departments={departments}
                  initialData={{
                    title: selectedPosition.title,
                    departmentId: (selectedPosition.department?.id || selectedPosition.departmentId).toString(),
                    description: selectedPosition.description || "",
                    minSalary: selectedPosition.minSalary?.toString() || "",
                    maxSalary: selectedPosition.maxSalary?.toString() || ""
                  }}
                  onSubmit={handleEditSubmit}
                  onCancel={() => setIsEditDialogOpen(false)}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Delete Dialog */}
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Position</DialogTitle>
                <DialogDescription className="space-y-2">
                  <p>Are you sure you want to delete this position? This action cannot be undone.</p>
                  <p className="text-yellow-600 dark:text-yellow-500">
                    Note: Positions with associated employees cannot be deleted. You must first reassign or remove all employees from this position.
                  </p>
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsDeleteDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => selectedPosition && handleDelete(selectedPosition.id)}
                >
                  Delete
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </main>
      </div>
    </ProtectedRoute>
  );
}
